<template>
  <div class="app-container">
    <div
      id="main__wrapper"
      class="main__wrapper"
    >
      <formdata
        ref="froms"
        :form-data="formData"
      />
    </div>
  </div>
</template>

<script>
import formdata from './components/formdata'
export default {
  components: { formdata },
  data() {
    return {
      formData: {
        doctorId: 0
      }
    }
  },
  mounted() {
    const fromInfo = this.$route.query
    this.formData.name = fromInfo.name
    this.formData.type = fromInfo.type
    this.formData.aiTest = fromInfo.aiTest || 0
    if (fromInfo.id) {
      this.formData.id = fromInfo.id
      this.$refs['froms'].getDetails(this.formData.id)
    }
  },
  methods: {},
  beforeRouteLeave(to, from, next) {
    const index = this.$store.state.tagsView.visitedViews.findIndex((item) => {
      return from.path == item.path
    })
    if (this.$refs['froms'].forms.length && !this.$refs['froms']._data.saveFlag) {
      this.$confirm('填写内容尚未保存, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          next()
        })
        .catch(() => {
          console.log('取消')
        })
    } else {
      this.$store.state.tagsView.visitedViews.splice(index, 1)
      next()
    }
  }
}
</script>
<style lang="css" scoped>
.main__wrapper {
  /*display:flex;*/
  height: calc(100vh - 200px);
}
</style>
