<template>
  <div class="app-container">
    <div class="title">{{ content.title }}</div>
    <div class="count">总数：{{ content.totalNumber }} 完成：{{ content.completePercent }}统计时间：{{ content.statisTime }}</div>
    <div class="filter-container">
      <el-input
        v-model="listQuery.name"
        clearable
        placeholder="请输入随访人"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <DictSelect
        v-model="listQuery.fillingStatus"
        placeholder="选择状态"
        type="follow_up_filling_status"
        style="width: 150px;"
        @change="handleFilter"
      />
      <DatePicker
        ref="datePickerRef"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        :query-model="listQuery"
        class="filter-item"
        style="width: 230px"
        @change="handleFilter"
      />
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >搜索</el-button>
      <el-button
        v-waves
        type="primary"
        icon="el-icon-refresh"
        @click="handleReset"
      >重置</el-button>
      <el-button
        type="primary"
        @click="handleDownload"
      >导出
      </el-button>
    </div>
    <el-table
      :key="tableKey"
      :data="content.result"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column
        v-for="(value,key) in content.head"
        :key="key"
        :label="value | filterFun"
        :prop="key"
        align="center"
      >
        <template slot="header">
          <el-tooltip class="item" effect="dark" :content="value">
            <span class="one-txt-cut">{{ value | filterFun }}</span>
          </el-tooltip>
        </template>

        <template slot-scope="{row}">
          <template v-if="row[key].type==2">
            <div v-if="row[key].value.length && row[key].value[0]">
              <el-image
                v-for="(item,k) in row[key].value"
                :key="k"
                style="width: 30px; height: 30px;margin:0 2px;"
                :src="item"
                fit="cover"
                :preview-src-list="row[key].value"
                lazy
              >
              </el-image>
            </div>
            <div v-else>-</div>
          </template>
          <template v-else>
            <el-popover
              trigger="hover"
              placement="bottom"
            >
              <div>{{ row[key].value[0] }}</div>
              <div
                slot="reference"
                class="ellipsis"
              >
                {{ row[key].value[0] | filterFun }}
              </div>
            </el-popover>
          </template>
        </template>
      </el-table-column>

      <el-table-column
        v-if="content.head"
        width="100"
        label="操作"
        align="center"
        fixed="right"
      >
        <template slot-scope="{row}">
          <el-button
            type="primary"
            size="mini"
            @click="handlookForm(row.id.value[0])"
          >查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog
      id="iframeDialog"
      :close-on-click-modal="false"
      title="预览"
      :visible.sync="dialogPreForm"
      width="400"
      top="5px"
    >
      <div id="completeDiv">
        <prevform
          :forms="forms"
          :is-disabled="isDisabled"
          :formtitle="formtitle"
          :form-data="formData"
          :describe="describe"
        />
      </div>
    </el-dialog>
  </div>

</template>

<script>
import DatePicker from '@/components/DatePicker'
import waves from '@/directive/waves'
import DictSelect from '@/components/DictSelect'
import { getToken, getTokenName } from '@/utils/auth'
import Prevform from '@/components/form_preview/prevform'
import { getQalist, exportQalist, getRecordDetail } from '@/api/from/index'
export default {
  name: 'DoctorTable',
  components: {
    DictSelect,
    DatePicker,
    Prevform
  },
  directives: { waves },
  filters: {
    filterFun: function(value) {
      if (value && value.length > 10) {
        //字符最大长度
        value = value.substring(0, 10) + '...' //超过省略
      }
      return value
    }
  },
  data() {
    return {
      tableKey: 0,
      content: [],
      forms: [],
      formData: null,
      isTitle: null,
      formtitle: '',
      describe: '',
      isDisabled: 'see',
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        qaId: ''
      },
      fromInfo: {
        checked: true
      },
      dialogPreForm: false,
      dialogAddForm: false,
      rules: {
        type: [{ required: true, message: '请选择类型', trigger: 'blur' }],
        name: [{ required: true, message: '请输入表单名称', trigger: 'blur' }]
      },
      id: '',
      headers: {}
    }
  },
  created() {
    this.headers[getTokenName()] = getToken()
    this.id = this.$route.query.id
    this.handleFilter()
  },
  methods: {
    // 获取数据
    getList() {
      this.listQuery.qaId = this.id
      getQalist(this.listQuery).then((response) => {
        // 过滤掉 content.head 中以 "input_" 开头的字段
        if (response.head) {
          const filteredHead = {}
          // 遍历原始的表头数据
          Object.keys(response.head).forEach(key => {
            // 如果键名不是以 "input_" 开头，则保留该字段
            if (!key.startsWith('input_')) {
              filteredHead[key] = response.head[key]
            }
          })
          // 用过滤后的表头替换原始表头
          response.head = filteredHead
        }

        // 过滤掉 content.result 中的每一行数据里以 "input_" 开头的字段
        if (response.result && response.result.length > 0) {
          response.result.forEach(row => {
            // 遍历每一行数据的所有字段
            Object.keys(row).forEach(key => {
              // 如果字段名以 "input_" 开头，则从该行数据中删除该字段
              if (key.startsWith('input_')) {
                delete row[key]
              }
            })
          })
        }

        this.content = response
        this.total = response.totalCount
      })
    },
    getDetail(formId) {
      getRecordDetail({
        followUpRecordFormId: formId
      }).then((response) => {
        this.forms = response.designForm.formDrawing
        if (response.designForm.formData) {
          this.formData =
            response.designForm.formData[response.designForm.lists[0].dataId]
        } else {
          this.formData = null
        }
        this.dialogPreForm = true
      })
    },
    handleFilter() {
      this.getList()
    },
    // 下载报表
    handleDownload() {
      const params = Object.assign({}, this.listQuery)
      params[getTokenName()] = getToken()
      this.downloadUrl = exportQalist(params)
      window.location.href = this.downloadUrl
    },

    // 浏览
    handlookForm(formId) {
      this.getDetail(formId)
    },
    handleReset() {
      this.listQuery = {
        pageNo: 1,
        pageSize: 10
      }
      this.$refs.datePickerRef.reset()
      this.handleFilter()
    }
  }
}
</script>
<style scoped>
.title {
  color: #909399;
  font-size: 18px;
  padding-bottom: 10px;
  font-weight: 900;
}
.count {
  color: #909399;
  padding-bottom: 10px;
}
.one-txt-cut{
overflow: hidden!important;
text-overflow:ellipsis!important;
white-space: nowrap !important;
}
</style>
