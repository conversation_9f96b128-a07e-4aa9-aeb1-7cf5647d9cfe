<template>
  <div style="display:flex;height:100%;">
    <div class="left">
      <Elements />
    </div>
    <div class="middle">
      <div
        ref="formsbox"
        class="wrapper--forms"
      >
        <draggable
          :list="forms"
          class="dragArea"
          :options="sortElementOptions"
          style="min-height:400px;"
        >
          <div
            v-for="(form, index) in forms"
            :key="index"
            :ref="form.inputname"
            v-bind="form"
            class="form__group"
            :class="[{ 'is--active': form === activeForm }]"
          >

            <div
              class="formfiled form__actionitem--move"
              @click="editElementProperties(form)"
            >
              <div
                v-show="form.hasOwnProperty('text')"
                class="form__label"
              ><span
                v-show="form.isRequired"
                class="srequired"
              >*</span>{{ form.text }}</div>
              <div
                v-show="form.helpBlockText!=''"
                class="form__helpblock"
              >{{ form.helpBlockText }} </div>

              <component
                :is="form.fieldType"
                :current-field="form"
                class="form__field"
              />
              <el-radio
                v-if="form.hasOwnProperty('isTitle') && type === 2"
                v-model="isTitle"
                class="isTitle"
                :label="form.inputname"
                @change="changeTitle"
              >设置为列表页标题</el-radio>
              <el-button-group
                v-if="form === activeForm"
                class="form__actionlist"
              >
                <el-button
                  v-show="!form.isUnique"
                  size="mini"
                  type="primary"
                  icon="el-icon-document-copy"
                  @click.stop="cloneElement(index, form)"
                />
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-delete"
                  @click.stop="deleteElement(index)"
                />
              </el-button-group>
              <div
                v-if="form === activeForm"
                class="form_move"
              >
                <el-button
                  v-show="!form.isUnique"
                  size="mini"
                  type="primary"
                  icon="el-icon-top"
                  @click.stop="moveUpElement(index)"
                />
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-bottom"
                  @click.stop="moveDownElement(index)"
                />
              </div>
            </div>
            <!-- <pre>{{ form }}</pre> -->
          </div>
        </draggable>
      </div>
      <div>
        <el-row
          :gutter="20"
          align="middle"
        >
          <el-col
            :span="24"
            style="text-align:center;"
          >
            <el-button
              type="primary"
              @click="handleSaveForm"
            >保存</el-button>
            <el-button
              type="primary"
              @click="dialogForm = true"
            >预览</el-button>
          </el-col>
        </el-row>
      </div>
      <!--div class="wrapper--snippet">
              <pre>{{ forms }}</pre>
            </div-->
    </div>
    <div class="right">
      <Properties />
    </div>
    <!--  预览 -->
    <el-dialog id="iframeDialog" :close-on-click-modal="false" title="预览" :visible.sync="dialogForm" width="400" top="5px">
      <div id="completeDiv">
        <Preview :forms="forms" :formtitle="formtitle" />
      </div>
    </el-dialog>
  </div>
</template>
<style lang="css" scoped>
.form__field{
	display: flex;
	flex-direction: column;
}
.form__field .el-checkbox-group{
		display: flex;
	flex-direction: column;
}
.middle .form__field,
.middle .form__helpblock {
  padding-left: 0;
}
.formtitle {
  padding: 5px 10px;
}
.formtitle input {
  border: none;
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
  padding-top: 5px;
  min-width: 400px;
}
.left {
  width: 150px;
  overflow-y: auto;
}
.describe {
  margin-bottom: 10px;
}
.middle {
  flex: 1;
  background-color: #fafafa;
  color: #666;
  position: relative;
}
.wrapper--forms {
  padding: 10px 10px 0;
  overflow-y: auto;
  height: calc(100vh - 180px);
}
.right {
  width: 300px;
  overflow-y: auto;
}
.form__field .el-input,
.form__field .el-textarea {
  width: auto;
  min-width: 220px;
}
.formfiled {
  padding: 10px;
  border: 1px dashed #ccc;
  background-color: rgba(236, 245, 255, 0.3);
  position: relative;
  margin-bottom: 10px;
}
.formfiled:after {
  position: absolute;
  content: " ";
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.form__actionlist {
  position: absolute;
  top: 5px;
  right: 30px;
  z-index: 10;
}
.form__actionlist button {
  padding: 5px;
}
.form__label {
  font-size: 14px;
  padding-bottom: 5px;
}
.form__field,
.form__helpblock {
  padding-left: 20px;
}
.form__helpblock {
  font-size: 14px;
  padding-right: 50px;
  padding-bottom: 5px;
}
.form__field label {
  display: block;
  padding: 2px 0;
}
.srequired {
  color: #f00;
}
.form_move {
  position: absolute;
  right: 5px;
  top: 5px;
  z-index: 10;
}
.form_move .el-button {
  display: flex;
  width: 20px;
  height: 20px;
  justify-content: center;
  align-items: center;
  padding: 0px;
  margin: 0;
}
.form_move .el-button:first-child {
  margin-bottom: 3px;
}
.is--active .formfiled {
  border-color: #1890ff;
}
</style>
<script>
import { mapGetters } from 'vuex'
import { FormBuilder } from '@/components/form_elements/formbuilder'
import { saveData, getDetail } from '@/api/from/index'
export default {
  components: FormBuilder.$options.components,
  props: {
    formData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      sortElementOptions: FormBuilder.$data.sortElementOptions,
      formtitle: '',
      saveFlag: false,
      dialogForm: false
    }
  },
  computed: {
    ...mapGetters(['forms', 'activeForm', 'activeTabForFields', 'themingVars']),
    activeTabForFields: {
      get() {
        return this.$store.state.formbuilder.activeTabForFields
      },
      set(val) {
        this.$store.state.formbuilder.activeTabForFields = val
      }
    },
    forms: {
      get() {
        return this.$store.state.formbuilder.forms
      },
      set(val) {
        this.$store.state.formbuilder.forms = val
      }
    },
    activeForm: {
      get() {
        return this.$store.state.formbuilder.activeForm
      },
      set(val) {
        this.$store.state.formbuilder.activeForm = val
      }
    }
  },
  mounted() {
    console.log('created->formbuilde=', FormBuilder.$data.sortElementOptions)
    this.$store.state.formbuilder.activeForm = {}
    this.$store.state.settings.fixedHeader = false
    console.log(this.formData, 289)
    if (this.formData.id) {
      this.getDetails()
    } else {
      this.forms = []
    }
  },
  created() {},
  methods: {
    getDetails(fromId) {
      getDetail(fromId).then(res => {
        this.forms = res.formDrawing
      })
    },
    handleSaveForm(type) {
      this.saveFlag = true
      console.log('saveForm', JSON.stringify(this.forms))
      this.formData.formDrawing = this.forms
      console.log(this.formData, 319)
      if (this.forms.length > 0) {
        saveData(this.formData).then(res => {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          this.$router.push({
            path: '/from/fromList'
          })
        })
      } else {
        this.$message({
          message: '请添加问卷问题',
          type: 'error'
        })
      }
    },
    deleteElement(index) {
      FormBuilder.deleteElement(index)
    },
    cloneElement(index, form) {
      FormBuilder.cloneElement(index, form)
    },
    moveUpElement(index) {
      FormBuilder.moveUpElement(index)
    },
    moveDownElement(index) {
      FormBuilder.moveDownElement(index)
    },
    editElementProperties(form) {
      FormBuilder.editElementProperties(form)
    }
  }
}
</script>
