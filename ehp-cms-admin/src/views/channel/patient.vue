<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.name"
        class="filter-item"
        maxlength="10"
        clearable
        placeholder="渠道名称"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
        @clear="handleFilter"
      />
      <DatePicker
        ref="datePickerRef"
        :query-model="listQuery"
        start-placeholder="创建开始时间"
        end-placeholder="创建结束时间"
        class="filter-item"
        style="width: 230px"
        @change="handleFilter"
      />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>

    </div>
    <div>
      <el-button
        v-permission="permissionCode.add"
        type="primary"
        icon="el-icon-plus"
        @click="handleAdd"
      >新建</el-button>
    </div>
    <el-table v-loading="loading" :data="list" fit highlight-current-row style="width: 100%;">
      <el-table-column label="渠道编号" prop="id" align="center" />
      <el-table-column label="渠道名称" prop="name" align="center" />
      <el-table-column label="渠道链接" prop="url" align="center" />
      <el-table-column label="渠道二维码" prop="qrCode" align="center">
        <template slot-scope="{ row }">
          <!-- {{ row.qrCode }} -->

          <div v-if="row.qrCode" class="qrcode-box">
            <el-image
              ref="image"
              class="qrCode"
              :src="row.qrCode"
              :preview-src-list="[row.qrCode]"
            >
            </el-image>
            <el-button type="primary" class="download" @click.stop="downloadImg(row)">下载</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="患者渠道状态" prop="status" align="center">
        <template slot-scope="{ row }">
          <el-tag :type="row.status===0?'success':'danger'">{{ row.status===0?'启用':'禁用' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="负责人" prop="principal" align="center">
      </el-table-column>
      <el-table-column label="创建时间" prop="createdAt" width="155px" align="center" />
      <el-table-column label="操作" fixed="right" align="center" width="150">
        <template slot-scope="{ row }">
          <el-button
            v-permission="permissionCode.edit"
            type="primary"
            @click="handleDetail(row, 'update')"
          >编辑</el-button>
          <el-button
            v-permission="permissionCode.edit"
            :type="row.status===0?'':'primary'"
            @click="handelStatus(row)"
          >{{ row.status===0?'禁用':'启用' }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      top="2vh"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-form-item label="渠道名称:" prop="name">
          <el-input
            v-model="form.name"
            :disabled="dialogStatus === 'detail'"
            maxlength="10"
            placeholder="请输入渠道名称"
            class="inputW"
          />
        </el-form-item>
        <el-form-item label="负责人:" prop="principal">
          <el-input
            v-model="form.principal"
            :disabled="dialogStatus === 'detail'"
            maxlength="10"
            placeholder="请输入负责人"
            class="inputW"
          />
        </el-form-item>
        <el-form-item label="状态:" prop="status">
          <el-switch
            v-model="form.status"
            :active-value="0"
            :inactive-value="1"
          >
          </el-switch>
        </el-form-item>
      </el-form>
      <div v-if="dialogStatus !== 'detail'" slot="footer" class="dialog-footer">
        <el-button :loading="loading" @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="updateData()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getChannelList, addChannel, editChannel, channelStatus } from '@/api/channel/patient'
import waves from '@/directive/waves'
import DatePicker from '@/components/DatePicker'
export default {
  name: 'ChannelManagement',
  directives: { waves },
  components: {
    DatePicker
  },
  filters: {},

  data() {
    return {
      permissionCode: {
        add: ['user:patient:channel:update'],
        edit: ['user:patient:channel:update'],
        see: ['user:patient:channel:detail']
      },
      props: {
        multiple: true,
        value: 'id',
        label: 'name',
        children: 'children'
      },
      list: [],
      rules: {
        name: [{ required: true, message: '请输入渠道名称', trigger: 'blur' }],
        principal: [{ required: true, message: '请输入负责人', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'blur' }]
      },
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        detail: '详情',
        update: '编辑',
        create: '新增'
      },
      form: {
        id: '',
        name: '',
        principal: '',
        status: 0
      },
      loading: false
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    // 获取数据
    getList() {
      this.loading = true
      getChannelList(this.listQuery).then((response) => {
        if (response) {
          this.list = response.list || []
          this.total = response.totalCount
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 搜索
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.$refs['form'].resetFields()
        this.$refs['form'].clearValidate()
      })
    },
    // 新增
    handleAdd() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.resetTemp()
    },
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          // eslint-disable-next-line no-self-assign
          this.dialogStatus === 'create' ? (this.form.id = undefined) : (this.form.id = this.form.id)
          const API = this.dialogStatus === 'create' ? addChannel : editChannel
          this.loading = true
          API(this.form, this.form.id).then(() => {
            this.dialogFormVisible = false
            if (this.dialogStatus === 'create') {
              this.handleFilter()
            } else {
              this.getList()
            }
            this.$message.success('保存成功！')
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    // 查询详情
    findAreaDetailsFun(row) {
      console.log(row)
      this.form.id = row.id
      this.form.name = row.name
      this.form.principal = row.principal
    },
    // 编辑 查看
    handleDetail(row, type) {
      this.dialogStatus = type
      this.dialogFormVisible = true
      this.resetTemp()
      setTimeout(() => {
        this.findAreaDetailsFun(row)
      }, 100)
    },
    handleReset() {
      this.listQuery.id = ''
      this.listQuery.name = ''
      this.listQuery.principal = ''
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    },
    handelStatus(item) {
      const status = item.status === 0 ? 1 : 0
      this.$confirm(`是否${status === 0 ? '启用' : '禁用'}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          channelStatus(item.id, status).then(res => {
            this.handleFilter()
          }).finally(() => {
            this.loading = false
          })
        })
        .then((res) => {
          this.$message.success('切换成功')
          this.getList()
        })
        .catch((e) => {})
        .finally((res) => {})
    },
    downloadImg(item) {
      const link = document.createElement('a')
      link.href = item.qrCode // 替换为你的base64数据
      link.download = item.name + '.png'
      link.click()
    }
  }
}
</script>

<style scoped>
.inputW {
  width: 250px;
}
.sameText {
  margin-bottom: 15px;
}
</style>
<style lang="scss" scoped>
.qrcode-box  {
  .download {
  }
}
</style>
