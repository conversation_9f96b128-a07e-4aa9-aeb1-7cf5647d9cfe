<template>
  <div class="app-container goods-info">
    <el-dialog top="8vh" width="1200px" append-to-body :close-on-click-modal="false" :visible.sync="dialogVisible" :before-close="close">
      <div slot="title" class="m-title">
        <span class="title">药品信息</span>
        <div v-if="dialogType === 'add' && cacheName" class="cache title" @click="handleCache">{{ cacheName }} {{ cacheTime }}</div>
      </div>
      <div v-loading="drugLoading" class="el-dialog-div">
        <el-scrollbar style="height: 100%;">
          <el-alert
            title="重要提示：互联网医院管理办法规定，不得在互联网上开具麻醉药品、精神类药品处方以及其他用药风险较高、有其他特殊管理规定的药品处方。系统禁止添加麻醉药品、精神类药品。"
            type="warning"
            :closable="false"
            center
            style="color: #6d000e; background-color: #ffeb3b; margin-bottom: 20px;"
          >
          </el-alert>
          <el-form ref="dataForm" :model="productData" :rules="rules" :inline="true" label-width="100px" class="demo-form-inline">
            <el-input v-model="productData.productId" type="hidden" />
            <el-row :gutter="20" type="flex" align="middle">
              <el-col :span="24">
                <el-form-item label="发布配置">
                  <DictSelect
                    v-model="release"
                    :options="releaseOptions"
                    placeholder="发布配置"
                    class="filter-item"
                    @change="handleRelease"
                  />
                  <span v-if="release == 2" class="tip_red">*药品信息可提供给医生开具处方，且患者可凭处方在线上直接购买药品</span>
                  <span
                    v-else
                    class="tip_red"
                  >*药品信息只提供给医生开具处方，患者只能凭处方去线下购买药品，患者端药房也无法展示该药品信息</span>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="release == 2" :gutter="20" type="flex" align="middle">
              <el-col :span="24">
                <el-form-item label="是否展示">
                  <DictSelect v-model="show" :options="showOptions" placeholder="是否展示" class="filter-item" />
                  <span v-if="show == 1" class="tip_red">*患者端可查看并搜索到该药品信息</span>
                  <span v-else class="tip_red">*患者端不可查看并搜索到该药品信息</span>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="border_line"></div>
            <h3 style="margin: 20px 0 20px 20px;">名称信息</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="商品名称" prop="name">
                  <el-input v-model="productData.name" disabled clearable />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="品牌名" prop="brandName">
                  <el-input v-model="productData.brandName" clearable @keyup.native.capture.prevent="productName" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="商品名" prop="drugName">
                  <el-input v-model="productData.drugName" clearable @keyup.native.capture.prevent="productName" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="药品通用名" prop="commonName">
                  <el-input v-model="productData.commonName" clearable @keyup.native.capture.prevent="productName" />
                </el-form-item>
              </el-col>
              <el-col v-if="release == 2" :span="8">
                <el-form-item label="英文名称" prop="englishName">
                  <el-input v-model="productData.englishName" clearable />
                </el-form-item>
              </el-col>
              <el-col v-if="release == 2" :span="8">
                <el-form-item label="汉语拼音" prop="pinyin">
                  <el-input v-model="productData.pinyin" clearable />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="border_line"></div>
            <h3 style="margin: 20px 0 20px 20px;">基础信息</h3>
            <el-row :gutter="20">
              <el-col v-if="release == 2" :span="8">
                <el-form-item label="展示分类" prop="categoryId">
                  <el-cascader
                    v-model="productData.categoryId"
                    :options="materialTypeData"
                    :props="props"
                    clearable
                    filterable
                    :show-all-levels="true"
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="release == 2" :span="8">
                <el-form-item label="药品分类" prop="drugClassificationId">
                  <el-cascader
                    v-model="productData.drugClassificationId"
                    :options="drugClassData"
                    :props="props"
                    clearable
                    filterable
                    :show-all-levels="true"
                    @change="handleChange"
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="release == 2" :span="8">
                <el-form-item label="贮藏" prop="storage">
                  <el-input v-model="productData.storage" clearable />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="安全类别" prop="nmpaType">
                  <DictSelect v-model="productData.nmpaType" placeholder="请选择" type="product_nmpa_type" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="剂型" prop="agentClassificationId">
                  <el-cascader v-model="productData.agentClassificationId" :options="agentsData" :props="props" filterable clearable />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="说明书规格" prop="specification">
                  <el-input v-model="productData.specification" clearable />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="药品本位码" prop="standardCode">
                  <el-input v-model="productData.standardCode" clearable />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="批准文号" prop="approvalNumber">
                  <el-input v-model="productData.approvalNumber" clearable placeholder="批准文号、进口注册证号、医药产品注册证号三选一" />
                </el-form-item>
              </el-col>
              <el-col v-if="release == 2" :span="8">
                <el-form-item label="原料分类" prop="materialType">
                  <DictSelect v-model="productData.materialType" filterable placeholder="请选择" type="product_material_type" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="release == 2" :gutter="20">
              <el-col :span="8">
                <el-form-item label="药理分类" prop="pharmacologyClassificationId">
                  <el-cascader
                    v-model="productData.pharmacologyClassificationId"
                    :options="pharmacologyData"
                    :props="props"
                    clearable
                    filterable
                    :show-all-levels="true"
                    @change="handleChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="border_line"></div>
            <div style="display: flex; align-items: center;">
              <h3 style="margin: 20px 0 20px 20px;">规格/单位</h3>
              <span style="margin-left: 20px; color: #1890ff; cursor: pointer;" @click="goExample">查看示例</span>
            </div>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="剂量规格" prop="titleSpec">
                  <el-input v-model="productData.titleSpec" placeholder="请输入" clearable />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="制剂单位" prop="packingUnitLimit">
                  <DictSelect v-model="productData.packingUnitLimit" filterable placeholder="请选择" type="product_packing_unit_limit" />
                </el-form-item>
              </el-col>
              <el-col :span="10">
                <el-form-item label="包装单位" prop="packingUnit">
                  <DictSelect v-model="productData.packingUnit" filterable placeholder="请选择" type="product_packing_unit" />
                </el-form-item>
              </el-col>
            </el-row>
            <div v-if="release == 1" class="border_line"></div>
            <h3 v-if="release == 1" style="margin: 20px 0 20px 20px;">包装规格</h3>
            <el-row v-if="release == 1" :gutter="20">
              <el-col v-for="(item, index) in productData.packageArr" :key="index" :span="24">
                <el-form-item class="package_item" :label="`包装规格${index + 1}`" required>
                  <div style="display: flex; align-items: center; margin-right: 20px;">
                    <el-input v-model="productData.titleSpec" placeholder="-" disabled clearable />
                    <span style="margin: 0 10px;">*</span>
                    <el-form-item
                      :prop="'packageArr.' + index + '.packingUnitNumber'"
                      :rules="{ required: true, message: '请输入制剂数量', trigger: 'blur' }"
                      style="margin-bottom: 0 !important;"
                    >
                      <el-input v-model="item.packingUnitNumber" placeholder="制剂数量" @input="packingUnitInput">
                        <template slot="append">
                          {{ productData.packingUnitLimit }}
                        </template>
                      </el-input>
                    </el-form-item>
                    <span style="margin: 0 10px;">/</span>
                    <el-input v-model="productData.packingUnit" disabled />
                  </div>
                  <el-button
                    v-if="index !== 0 && !item.skuId"
                    type="text"
                    style="color: red;"
                    @click="deletePackage(index)"
                  >删除</el-button>
                  <el-button
                    v-if="item.skuId"
                    type="text"
                    :style="{ color: item.status == 0 ? '#1890ff' : 'red' }"
                    @click="goGround(item, index)"
                  >{{ item.status == 0 ? '上架' : '下架' }}</el-button>
                </el-form-item>
              </el-col>
              <el-button type="text" style="margin-left: 20px;" @click="addPackage">+ 添加包装规格</el-button>
            </el-row>
            <div class="border_line"></div>
            <h3 style="margin: 20px 0 20px 20px;">更多</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="生产企业" prop="productionEnterprise" class="productionEnterprise">
                  <el-input v-model="productData.productionEnterprise" autosize placeholder="参考商品说明书填写" />
                </el-form-item>
              </el-col>
              <el-col v-if="release == 2" :span="8">
                <el-form-item label="执行标准:" prop="executionStandard">
                  <el-input v-model="productData.executionStandard" clearable />
                </el-form-item>
              </el-col>
              <el-col v-if="release == 2" :span="8">
                <el-form-item label="有效期" prop="periodValidity">
                  <el-input v-model="productData.periodValidity" clearable />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="labelitem">
              <el-form-item label="用法用量" :prop="release == 2 ? 'usageDosage' : ''">
                <el-col :span="24">
                  <el-input v-model="productData.usageDosage" type="textarea" autosize placeholder="参考商品说明书填写" />
                </el-col>
              </el-form-item>
            </el-row>

            <el-row v-if="release == 2" :gutter="20" class="labelitem">
              <el-form-item label="适应症" prop="indications">
                <el-col :span="24">
                  <el-input v-model="productData.indications" type="textarea" autosize width="50%" placeholder="参考商品说明书填写" />
                </el-col>
              </el-form-item>
            </el-row>

            <!-- <el-row v-if="release == 2" :gutter="20" class="labelitem">
              <el-form-item label="关联诊断">
                <el-col :span="24">
                  <el-select
                    ref="selectDom"
                    v-model="diagnosisInfoList"
                    multiple
                    filterable
                    remote
                    value-key="mainCode"
                    reserve-keyword
                    placeholder="请输入"
                    :remote-method="remoteMethodInfo"
                    style="width: 100%;"
                  >
                    <el-option
                      v-for="item in options"
                      :key="item.mainCode"
                      :label="item.name + '-' + item.mainCode"
                      :value="item.name + '-' + item.mainCode"
                    />
                  </el-select>
                </el-col>
                <el-col :span="24">
                  <div style="color: #999;">需在商城中展示的药品必须关联诊断并配置默认用法用量</div>
                </el-col>
              </el-form-item>
            </el-row> -->

            <el-row v-if="release == 2" :gutter="20" class="labelitem">
              <el-form-item label="成份" prop="ingredients">
                <el-col :span="24">
                  <el-input v-model="productData.ingredients" type="textarea" autosize width="50%" placeholder="参考商品说明书填写" />
                </el-col>
              </el-form-item>
            </el-row>
            <el-row v-if="release == 2" :gutter="20" class="labelitem">
              <el-form-item label="性状" prop="phenotypicTrait">
                <el-col :span="24">
                  <el-input v-model="productData.phenotypicTrait" type="textarea" autosize width="50%" placeholder="参考商品说明书填写" />
                </el-col>
              </el-form-item>
            </el-row>
            <el-row v-if="release == 2" :gutter="20" class="labelitem">
              <el-form-item label="不良反应" prop="adverseEffects">
                <el-col :span="24">
                  <el-input v-model="productData.adverseEffects" type="textarea" autosize width="50%" placeholder="参考商品说明书填写" />
                </el-col>
              </el-form-item>
            </el-row>
            <el-row v-if="release == 2" :gutter="20" class="labelitem">
              <el-form-item label="禁忌" prop="contraindications">
                <el-col :span="24">
                  <el-input v-model="productData.contraindications" type="textarea" autosize width="50%" placeholder="参考商品说明书填写" />
                </el-col>
              </el-form-item>
            </el-row>
            <el-row v-if="release == 2" :gutter="20" class="labelitem">
              <el-form-item label="注意事项" prop="mattersNeedingAttention">
                <el-col :span="24">
                  <el-input
                    v-model="productData.mattersNeedingAttention"
                    type="textarea"
                    autosize
                    width="50%"
                    placeholder="参考商品说明书填写"
                  />
                </el-col>
              </el-form-item>
            </el-row>

            <el-row v-if="release == 2" :gutter="20" class="labelitem">
              <el-form-item label="化学名称" prop="chemicalName">
                <el-col :span="24">
                  <el-input v-model="productData.chemicalName" type="textarea" autosize width="50%" placeholder="参考商品说明书填写" />
                </el-col>
              </el-form-item>
            </el-row>
          </el-form>
        </el-scrollbar>
      </div>
      <div slot="footer">
        <el-button @click="close()">取 消</el-button>
        <el-button type="primary" @click="saveProduct('dataForm')">保 存</el-button>
        <el-button type="primary" :disabled="release == 2 ? false : true" @click="saveProduct('dataForm', 'sku')">保存并设置SKU</el-button>
      </div>
    </el-dialog>
    <el-dialog width="500px" title="示例图片" :visible.sync="imageDialogVisible">
      <img style="width: 100%;" src="../../assets/images/drug.png" alt="" />
    </el-dialog>
  </div>
</template>
<script>
import api_product from '@/api/product/product'
import api_base from '@/api/product/base'
import DictSelect from '@/components/DictSelect'
import wangEditor from '@/components/wangEditor'
import { Loading } from 'element-ui'
export default {
  name: '',
  filters: {},
  components: {
    DictSelect,
    wangEditor
  },
  props: {
    createInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      productData: {
        packageArr: [
          {
            packingUnitNumber: ''
          }
        ]
      },
      drugClassData: null,
      pharmacologyData: null, //药理分类数据
      materialTypeData: null, //展示分类数据
      agentsData: null, //药剂分类
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      rules: {
        name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
        commonName: [
          { required: true, message: '请输入商品通用名', trigger: 'blur' }
          // { validator: validateName, trigger: 'blur' }
        ],
        nmpaType: [{ required: true, message: '请选择安全类别', trigger: 'blur' }],
        standardCode: [{ required: true, message: '请输入药品本位码', trigger: 'blur' }],
        approvalNumber: [
          {
            required: true,
            message: '请输入批准文号/注册证号',
            trigger: 'blur'
          }
        ],
        categoryId: [{ required: true, message: '请选择展示分类', trigger: 'blur' }],
        agentClassificationId: [{ required: true, message: '请选择型剂', trigger: 'blur' }],
        packingUnit: [{ required: true, message: '请选择包装', trigger: 'blur' }],
        packingUnitLimit: [{ required: true, message: '请选择最小包装单位', trigger: 'blur' }],
        specification: [{ required: true, message: '请输入规格', trigger: 'blur' }],
        storage: [{ required: true, message: '请输入贮藏', trigger: 'blur' }],
        indications: [{ required: true, message: '请输入适应症', trigger: 'blur' }],
        usageDosage: [{ required: true, message: '请输入用法用量', trigger: 'blur' }],
        productionEnterprise: [{ required: true, message: '请输入生产企业', trigger: 'blur' }],
        periodValidity: [{ required: true, message: '请输入有效期', trigger: 'blur' }],
        executionStandard: [{ required: true, message: '请输入执行标准', trigger: 'blur' }],
        drugClassificationId: [{ required: true, message: '请选择药品分类', trigger: 'blur' }]
        // packingUnitNumber: [{ required: true, message: '请输入制剂数量', trigger: 'blur' }]
      },
      dialogVisible: false,
      options: [],
      // diagnosisInfoList: [],
      releaseOptions: [
        {
          code: 2,
          value: '可供患者购买'
        },
        {
          code: 1,
          value: '仅供医生开方'
        }
      ],
      showOptions: [
        {
          code: 1,
          value: '是'
        },
        {
          code: 0,
          value: '否'
        }
      ],
      release: 2,
      show: 1,
      imageDialogVisible: false,
      cacheName: '',
      cacheTime: '',
      productDataCache: {},
      drugLoading: false,
      productId: '',
      dialogType: 'add'
    }
  },
  watch: {
    createInfo: function(val) {
      this.productData = val
      // localStorage.setItem('PRODUCTDATA', JSON.stringify(this.productData))
    },
    dialogVisible(val) {
      if (val && this.dialogType === 'add') {
        console.log('执行')
        const obj = JSON.parse(localStorage.getItem('PRODUCTDATA')) || {}
        this.cacheName = this.isEmptyObject(obj) ? '' : '使用草稿：' + obj.name
        this.cacheTime = localStorage.getItem('PRODUCTDATA_TIME')
      }
      // if (!val && !this.productId) {
      //   localStorage.setItem('PRODUCTDATA', JSON.stringify(this.productData))
      // }
      // if (val && localStorage.getItem('PRODUCTDATA') && !this.productId) {
      //   this.productData = JSON.parse(localStorage.getItem('PRODUCTDATA'))
      // }
    }
  },
  created() {
    this.getPharmacologyData()
    this.getCategoryList()
    this.getAgentsData()
    this.getDrugData()
  },
  methods: {
    isEmptyObject(obj) {
      return Object.entries(obj).length === 0
    },
    productName() {
      var second = '',
        three = ''
      if (this.productData.drugName && this.productData.brandName) {
        second = ' ' + this.productData.drugName
      }
      if (this.productData.commonName) {
        three = ' ' + this.productData.commonName
      }
      if (this.productData.brandName) {
        this.productData.name = this.productData.brandName + second + three
      } else {
        this.productData.name = second + three
      }
      this.productData = Object.assign({}, this.productData)
    },
    getDrugData() {
      api_base.drug().then((response) => {
        this.drugClassData = response
      })
    },
    getProductInfo(productId) {
      api_product.info(productId).then((response) => {
        this.productData = response
        this.release = this.productData.publishType // 发布类型
        this.show = this.productData.visibleType // 是否展示
        this.productData.packageArr = response.skus && response.skus.length > 0 ? response.skus : [{ packingUnitNumber: '' }] // 包装规格
      })
    },
    getPharmacologyData() {
      api_base.pharmacology().then((response) => {
        this.pharmacologyData = response
      })
    },
    getAgentsData() {
      api_base.agents().then((response) => {
        this.agentsData = response
      })
    },
    getCategoryList() {
      api_product.categorylist().then((response) => {
        this.materialTypeData = response.filter(item => item.children && item.children.length > 0)
      })
    },
    saveProduct(dataForm, sku) {
      this.$refs[dataForm].validate((valid) => {
        if (valid) {
          const loadingInstance = Loading.service({
            fullscreen: true
          })
          // this.productData.diagnoses = this.diagnosisInfoList.map((item) => {
          //   const parts = item.split('-')
          //   const name = parts[0]
          //   const code = parts[1]
          //   return { name, code }
          // })
          this.productData.publishType = this.release // 发布类型
          this.productData.visibleType = this.show // 是否展示
          if (this.productData.publishType == 1) { // 仅开方
            // 包装规格
            this.productData.skus = this.productData.packageArr.map((item, i) => {
              return {
                defaultSku: i == 0 ? 0 : 1,
                packingUnitNumber: item.packingUnitNumber,
                skuId: item.skuId,
                status: item.skuId ? item.status : 1
              }
            })
          } else {
            this.productData.skus = []
          }
          api_product
            .update(this.productData)
            .then((response) => {
              this.dialogVisible = false
              if (sku && this.release == 2) {
                this.$router.push({
                  path: './sku/' + response
                })
              } else {
                this.$message({
                  message: '保存成功',
                  type: 'success'
                })
                this.$emit('refresh')
              }
              if (this.dialogType === 'add') localStorage.setItem('PRODUCTDATA', JSON.stringify(this.productData))
            })
            .finally(function() {
              loadingInstance.close()
            })
        }
      })
    },
    handleChange(value) {},
    // 设置缓存数据
    handleCache() {
      this.drugLoading = true
      setTimeout(() => {
        this.drugLoading = false
        this.productData = JSON.parse(localStorage.getItem('PRODUCTDATA'))
        if (this.dialogType === 'add') {
          this.productData.productId && delete this.productData.productId
        }
      }, 500)
    },
    // 获取当前时间
    getNowDate() {
      const currentTime = new Date()
      const year = currentTime.getFullYear()
      const month = `${currentTime.getMonth() + 1}`.padStart(2, '0') // 补0
      const day = `${currentTime.getDate()}`.padStart(2, '0') // 补0
      const hour = `${currentTime.getHours()}`.padStart(2, '0') // 补0
      const minute = `${currentTime.getMinutes()}`.padStart(2, '0') // 补0
      const second = `${currentTime.getSeconds()}`.padStart(2, '0') // 补0

      const formattedTime = `${year}-${month}-${day} ${hour}:${minute}:${second}`
      console.log('关闭弹框时的当前时间：', formattedTime)
      localStorage.setItem('PRODUCTDATA_TIME', formattedTime)
    },
    close() {
      this.$confirm('确认关闭吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.dialogVisible = false
          if (this.dialogType === 'add') {
            localStorage.setItem('PRODUCTDATA', JSON.stringify(this.productData))
            this.getNowDate()
          }
        })
        .catch((e) => {})
        .finally((res) => {})
    },
    handleCreate(createInfo) {
      this.dialogType = 'add'
      this.dialogVisible = true
      this.release = 2
      this.productData.packageArr = [{ packingUnitNumber: '' }]
      this.resetTemp()
      this.productName()
    },
    handleUpdate(productId) {
      this.dialogType = 'edit'
      this.productId = productId
      this.dialogVisible = true
      this.resetTemp()
      this.getProductInfo(productId)
    },
    resetTemp() {
      this.$nextTick(() => {
        this.productData = {packageArr: [{ packingUnitNumber: '' }]}
        // this.diagnosisInfoList = []
        this.$refs['dataForm'].clearValidate()
      })
    },
    remoteMethodInfo(query) {
      if (query !== '') {
        this.loading = true
        const params = {
          key: query
        }
        api_product.getDiagnoseData(params).then((response) => {
          this.options = response.list
        })
      } else {
        this.options = []
      }
    },
    handleRelease(val) {
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    goExample() {
      this.imageDialogVisible = true
    },
    deletePackage(i) {
      this.productData.packageArr.splice(i, 1)
      this.$forceUpdate()
    },
    addPackage() {
      this.productData.packageArr.push({
        packingUnitNumber: ''
      })
      this.$forceUpdate()
    },
    goGround(item, i) {
      item.status = item.status == 0 ? 1 : 0
      this.$forceUpdate()
    },
    packingUnitInput() {
      this.$forceUpdate()
      this.productData = Object.assign({}, this.productData)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__header {
  height: 60px;
  background-color: #f8f9fb;
}

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}

::v-deep .is-horizontal .el-scrollbar__thumb {
  display: none;
}

>>> .el-dialog__header {
  background-color: #f5f7fa;
}

>>> .el-dialog__title {
  color: #333;
  font-weight: 600;
}

>>> .el-dialog__footer {
  padding: 16px 30px;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.2);
}

>>> .package_item .el-input__inner {
  width: 200px !important;
}

>>> .package_item .el-form-item__content {
  display: flex;
}

>>> .el-input__inner {
  height: 36px;
  line-height: 36px;
  width: 268px;
}

>>> .el-textarea__inner {
  height: 36px;
  line-height: 36px;
  width: 100%;
}

>>> .el-form--inline .el-form-item {
  display: flex;
  align-items: center;
}

>>> .labelitem .el-select .el-input__inner {
  width: 100% !important;
}

.el-dialog-div {
  height: 60vh;
  overflow-x: hidden;
}

.title {
  padding-bottom: 10px;
}

.tips {
  display: inline-block;
  margin-left: 10px;
}

.el-dialog__footer {
  padding-right: 20%;
}

.labelitem .el-form-item {
  display: flex;
}

.labelitem /deep/.el-form-item__content {
  // width: 84.2%;
  width: 100%;
}

.goods-info .el-divider--horizontal {
  margin-top: 0;
  margin-bottom: 20px;
}
.tip_red {
  color: red;
  margin-left: 15px;
}
.border_line {
  border: 1px dashed #dcdfe6;
}
.m-title {
  display: flex;
  .title {
    color: #333;
    font-weight: 600;
  }
  .cache {
    margin-left: 50px;
    cursor: pointer;
    text-decoration: underline;
    color: #6d000e;
  }
}
</style>
