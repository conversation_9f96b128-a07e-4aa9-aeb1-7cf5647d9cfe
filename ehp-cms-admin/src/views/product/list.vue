<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.id" placeholder="ID" clearable class="filter-item" style="width: 120px;" @keyup.enter.native="handleFilter" />
      <el-input
        v-model="listQuery.number"
        placeholder="商品编码"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.name"
        placeholder="商品名称"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.approvalNumber"
        placeholder="批准文号"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <!-- <el-input
        v-model="listQuery.disease"
        placeholder="诊断"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      /> -->
      <DictSelect
        v-model="listQuery.type"
        placeholder="请选择品类"
        class="filter-item"
        type="product_type"
        style="width: 110px;"
        @change="handleFilter"
      />
      <DictSelect
        v-model="listQuery.nmpaType"
        placeholder="安全分类"
        class="filter-item"
        style="width: 120px;"
        type="product_nmpa_type"
        @change="handleFilter"
      />
      <!-- <el-cascader
        v-model="listQuery.classificationId"
        placeholder="请选择药理分类"
        :options="baseoptions"
        :props="props"
        clearable
        class="filter-item"
        style="width: 150px;"
        @change="handleFilter"
      /> -->
      <DictSelect
        v-model="listQuery.relationSku"
        placeholder="是否关联SKU"
        class="filter-item"
        style="width: 120px;"
        type="product_relation_sku"
        @change="handleFilter"
      />
      <el-cascader
        v-model="listQuery.categoryId"
        placeholder="展示分类"
        :options="materialTypeData"
        :props="props"
        clearable
        class="filter-item"
        style="width: 120px;"
        @change="handleFilter"
      />
      <DictSelect
        v-model="listQuery.publishType"
        :options="releaseOptions"
        placeholder="发布配置"
        class="filter-item"
        style="width: 120px;"
        @change="handleFilter"
      />
      <DictSelect
        v-model="listQuery.visibleType"
        :options="showOptions"
        placeholder="是否显示"
        class="filter-item"
        style="width: 120px;"
        @change="handleFilter"
      />
      <el-select
        v-model="listQuery.warehouseId"
        clearable
        placeholder="合作商家"
        style="width: 120px;"
        @change="handleFilter"
      >
        <el-option
          v-for="item in dataIntegrityOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        ></el-option>
      </el-select>
      <DatePicker
        ref="datePickerRef"
        :query-model="listQuery"
        class="filter-item"
        style="width: 220px;"
        start-placeholder="建码开始时间"
        end-placeholder="建码结束时间"
        @change="handleFilter"
      />
      <!-- todo -->
      <!-- <DictSelect
        v-model="listQuery.featured"
        placeholder="是否首页推荐"
        class="filter-item"
        style="width: 120px;"
        type="product_data_integrity"
        @change="handleFilter"
      /> -->
      <el-button type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      <el-dropdown v-permission="['wms:medicine:update']" type="primary" split-button trigger="hover" :visible-arrow="false" @command="changeCommand" @click="changeCommand(1)">
        新建药品
        <el-dropdown-menu slot="dropdown">
          <!-- <el-dropdown-item :command="1">新建药品</el-dropdown-item> -->
          <el-dropdown-item :command="2">新建保健品</el-dropdown-item>
          <el-dropdown-item :command="3">新建医疗器械</el-dropdown-item>
          <el-dropdown-item :command="4">新建食品</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <!-- <el-button v-permission="['wms:medicine:update']" type="primary" icon="el-icon-plus" @click="handleCreate">新建商品</el-button> -->
    </div>
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      highlight-current-row
      :row-style="{ height: '42px' }"
      :header-row-style="{ height: '42px' }"
      :header-cell-style="{
        background: '#F8F9FB'
      }"
    >
      <el-table-column label="ID" prop="id" align="center" width="50px" fixed />
      <el-table-column label="商品编码" prop="number" width="120px" align="center" fixed />
      <el-table-column label="品类" prop="typeDescribe" align="center" width="60px" />
      <el-table-column label="展示分类" prop="categoryName" align="center" width="160px" />
      <el-table-column label="商品名称" prop="name" align="center" min-width="200px" show-overflow-tooltip />
      <el-table-column label="通用名" prop="commonName" min-width="200px" align="center" show-overflow-tooltip />
      <el-table-column label="安全分类" prop="nmpaTypeDescribe" align="center" width="70px" />
      <!-- <el-table-column label="药理分类" prop="classificationName" min-width="200px" align="center" /> -->
      <el-table-column label="批准文号/注册码/生产许可证编号" prop="approvalNumber" align="center" width="200px" />
      <el-table-column label="生产企业" prop="productionEnterprise" min-width="200px" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column label="建码时间" prop="createdAt" width="135px" align="center" />
      <el-table-column label="发布配置" prop="publishType" width="150px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.publishType == 1 ? '仅供开方' : '可供购买' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="展示" prop="visibleType" width="80px" align="center">
        <template slot="header">
          <span>展示
            <el-tooltip class="item" effect="dark" content="患者端可查看并搜索到该药品信息" placement="top-start">
              <i class="el-icon-question" style="color:#606266;" />
            </el-tooltip>
          </span>
        </template>
        <template slot-scope="{ row }">
          <span>{{ row.visibleType == 1 ? '是' : '否' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="关联sku" prop="relationSku" width="80px" fixed="right" align="center">
        <template slot-scope="{ row }">
          <el-checkbox v-model="row.relationSku" :true-label="1" :false-label="0" disabled />
        </template>
      </el-table-column>
      <el-table-column label="资料完整" prop="dataIntegrity" width="80px" align="center">
        <template slot-scope="{ row }">
          <el-checkbox v-model="row.dataIntegrity" :true-label="1" :false-label="0" disabled />
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" align="center" width="280px">
        <template slot-scope="{row}">
          <el-button v-permission="['wms:medicine:update']" type="primary" @click="editProduct(row)">编辑</el-button>
          <el-button
            v-permission="['wms:medicine:sku:list']"
            type="primary"
            :disabled="row.publishType === 1 ? true : false"
            @click="editSku(row.id)"
          >SKU</el-button>
          <el-button
            v-if="row.featured === 1"
            type="danger"
            :disabled="row.publishType === 1 ? true : false"
            @click="cancelRecommend(row.id)"
          >取消</el-button>
          <el-button v-else type="primary" :disabled="row.publishType === 1 ? true : false" @click="recommend(row.id)">推荐</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageNo" :limit.sync="listQuery.pageSize" @pagination="getList" />

    <!-- <el-dialog title="选择品类" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :model="createInfo" :rules="rules">
        <el-form-item label="品类" :label-width="formLabelWidth" prop="type">
          <DictSelect v-model="createInfo.type" placeholder="请选择" type="product_type" />
        </el-form-item>
        <el-form-item label="品牌" :label-width="formLabelWidth">
          <el-input v-model="createInfo.brandName" class="brandinput" placeholder="品牌名简称" maxlength="6" />
          <div class="tips">非必填，最多6个字符</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">返回</el-button>
        <el-button type="primary" @click="nextCreate('dataForm')">下一步</el-button>
      </div>
    </el-dialog> -->
    <DrugForm ref="drugForm" :create-info="createInfo" @refresh="getList"></DrugForm>
    <FoodInfo ref="foodInfo" :create-info="createInfo" @refresh="getList" />
  </div>
</template>
<script>
import api_product from '@/api/product/product'
import api_pharmacy from '@/api/pharmacy/index'
import api_base from '@/api/product/base'
import DictSelect from '@/components/DictSelect'
import DatePicker from '@/components/DatePicker'
import DrugForm from './drugInfo'
import FoodInfo from './foodInfo'
export default {
  name: 'Productlist',
  filters: {},
  components: {
    DictSelect,
    DatePicker,
    DrugForm,
    FoodInfo
  },
  data() {
    return {
      tableKey: 0,
      baseoptions: null,
      list: null,
      total: 0,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name',
        checkStrictly: true
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        name: '',
        type: '',
        classificationId: '',
        relationSku: '',
        dataIntegrity: '',
        nmpaType: '' // 安全分类
      },
      dialogFormVisible: false,
      formLabelWidth: '25%',
      createInfo: {},
      rules: {
        type: [{ required: true, message: '请选择品类', trigger: 'change' }]
      },
      status: '',
      productId: '',
      materialTypeData: null,
      releaseOptions: [
        {
          code: 2,
          value: '可供购买'
        },
        {
          code: 1,
          value: '仅供开方'
        }
      ],
      showOptions: [
        {
          code: 1,
          value: '是'
        },
        {
          code: 0,
          value: '否'
        }
      ],
      dataIntegrityOptions: [],
      listLoading: false
    }
  },
  created() {
    // this.handleFilter()
    this.getBaseData()
    this.getCategoryList()
    this.getCooperativeList()
  },
  activated() {
    this.getList()
    console.log('============activated=============')
  },
  methods: {
    recommend(id) {
      api_product
        .changeFeatured({
          featured: 1, // 	推荐状态 0：未推荐，1：推荐
          productId: id
        })
        .then((rs) => {
          this.$message.success('设为推荐成功！')
          this.getList()
        })
    },
    cancelRecommend(id) {
      api_product
        .changeFeatured({
          featured: 0, // 	推荐状态 0：未推荐，1：推荐
          productId: id
        })
        .then((rs) => {
          this.$message.success('取消推荐成功！')
          this.getList()
        })
    },
    // 获取数据
    getList() {
      this.listLoading = true
      api_product.list(this.listQuery).then((response) => {
        this.listLoading = false
        this.list = response.list
        this.total = response.totalCount
      })
    },
    getBaseData() {
      api_base.pharmacology().then((response) => {
        this.baseoptions = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.id = ''
      this.listQuery.number = ''
      this.listQuery.approvalNumber = ''
      this.listQuery.name = ''
      this.listQuery.type = ''
      this.listQuery.classificationId = ''
      this.listQuery.relationSku = ''
      this.listQuery.dataIntegrity = ''
      this.listQuery.nmpaType = ''
      this.listQuery.featured = ''
      this.listQuery.categoryId = ''
      this.listQuery.publishType = ''
      this.listQuery.visibleType = ''
      this.listQuery.warehouseId = ''
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    },
    handleCreate() {
      this.dialogFormVisible = true
      this.resetTemp()
    },
    /**
     * 点击新增
     * @param {number} command drug:新增药品
     */
    changeCommand(command) {
      switch (command) {
        case 1: // 药品
          this.$refs['drugForm'].handleCreate(this.createInfo)
          break
        case 2: // 保健品
          this.$refs['foodInfo'].handleCreate(this.createInfo)
          break
        case 3: // 医疗器械
          this.$refs['drugForm'].handleCreate(this.createInfo)
          break
        case 4: // 食品
          this.$refs['foodInfo'].handleCreate(this.createInfo)
          break
        default:
          break
      }
    },
    nextCreate(dataForm) {
      this.$refs[dataForm].validate((valid) => {
        if (valid) {
          this.dialogFormVisible = false
          if (this.createInfo.type == 1 || this.createInfo.type == 3) {
            // 药品信息
            this.$refs['drugForm'].handleCreate(this.createInfo)
          } else if (this.createInfo.type == 2 || this.createInfo.type == 4) {
            // 食品、保健品信息
            this.$refs['foodInfo'].handleCreate(this.createInfo)
          }
        }
      })
    },
    editProduct(row) {
      const { id, type } = row
      if (type == 1 || type == 3) {
        // 药品信息
        this.$refs['drugForm'].handleUpdate(id)
      } else if (type == 2 || type == 4) {
        // 食品、保健品信息
        this.$refs['foodInfo'].handleUpdate(id)
      }
      // this.$refs['drugForm'].handleUpdate(id)
    },
    editSku(productId) {
      this.productId = productId
      this.$router.push({
        path: './sku/' + productId
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        this.createInfo = {}
        this.$refs['dataForm'].clearValidate()
      })
    },
    getCategoryList() {
      api_product.categorylist().then(res => {
        this.materialTypeData = res
      })
    },
    getCooperativeList() {
      api_pharmacy.select().then((res) => {
        this.dataIntegrityOptions = res
      })
    }
  }
}
</script>
<style scoped>
.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #1890ff;
  border-color: #1890ff;
}
.brandinput {
  width: 193px;
}
.tips {
  display: inline-block;
  margin-left: 10px;
}
</style>
<style lang="scss" scoped>
.warpper {
  .product-info{
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
  }
}
::v-deep .el-dropdown-menu__item{
  padding: 5px 15px!important;
}
</style>
<style>
.el-tooltip__popper {
  max-width: 400px !important;
}
</style>
