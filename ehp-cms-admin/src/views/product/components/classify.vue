<template>
  <div class="classify">

    <div class="filter-container">
      <el-button
        v-permission="['wms:category:save']"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >新增分类</el-button>
    </div>
    <el-table :key="tableKey" :data="list" fit highlight-current-row>
      <el-table-column label="分类名称" prop="name" align="center" />
      <el-table-column label="分类图片" prop="icon" align="center">
        <template slot-scope="{row}">
          <el-image v-if="row.icon" :src="row.icon">
            <!-- <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div> -->
          </el-image>
          <img v-else src="@/assets/images/<EMAIL>" alt="">
        </template>
      </el-table-column>
      <el-table-column label="药品数量" prop="pnumber" align="center" />
      <el-table-column label="排序" prop="sort" align="center" width="180px" />
      <el-table-column label="创建时间" prop="createdAt" width="150px" align="center" />
      <el-table-column label="操作" fixed="right" align="center" width="180px">
        <!-- v-permission="['wms:medicine:update']" -->
        <template slot-scope="{row}">
          <el-button
            v-permission="['wms:category:save']"
            type="primary"
            @click="editProduct(row)"
          >编辑</el-button>
          <el-button
            v-permission="['wms:category:save']"
            type="danger"
            @click="deleteRow(row.id)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="createInfo.id?'编辑展示分类':'新增展示分类'" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :model="createInfo" :rules="rules">
        <el-form-item label="分类名称" :label-width="formLabelWidth" prop="name">
          <el-input
            v-model="createInfo.name"
            class="brandinput"
            placeholder="请输入分类名称"
            maxlength="6"
          />
        </el-form-item>
        <el-form-item label="备注" :label-width="formLabelWidth" prop="description">
          <el-input
            v-model="createInfo.description"
            type="textarea"
            :rows="5"
            class="brandinput"
            placeholder="请输入备注"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item label="分类图片" :label-width="formLabelWidth" prop="imgList">
          <uploadImage
            :value="createInfo.imgList"
            action="/storage"
            list-type="picture-card"
            :show-file-list="false"
            :limit="1"
            size-tips="图片尺寸92*92px；"
            accept="png、jpeg、jpg"
            @success="onSuccess"
            @remove="onRemove"
          >
          </uploadImage>
        </el-form-item>
        <el-form-item label="排序号" :label-width="formLabelWidth" prop="sort">
          <el-input-number v-model="createInfo.sort" :min="-999" :max="999" style="width:150px" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="saveClassify">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped>
/*.el-form-item .el-input__inner{width:auto;}*/
.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #1890ff;
  border-color: #1890ff;
}
.brandinput {
  width: 193px;
}
.tips {
  display: inline-block;
  margin-left: 10px;
}
 /deep/ .el-image img{
  width: 48px;
  height: 48px;
}
img{
  width: 48px;
}
</style>
<script>
import api_product from '@/api/product/product'
import api_base from '@/api/product/base'
import uploadImage from '@/components/uploadImage'
export default {
  name: 'Productlist',
  filters: {},
  components: {
    uploadImage
  },
  data() {
    return {
      tableKey: 0,
      baseoptions: null,
      list: null,
      total: 0,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      dialogFormVisible: false,
      formLabelWidth: '25%',
      createInfo: {
        name: '',
        description: '',
        imgList: [],
        icon: '',
        sort: ''
      },
      rules: {
        name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
        imgList: [{ required: true, message: '请上传分类图片', trigger: ['blur', 'change'] }],
        sort: [{ required: true, message: '请输入排序号', trigger: 'blur' }]
      },
      status: '',
      productId: '',
      nextSort: ''
    }
  },
  created() {
    // this.handleFilter()
    // this.getBaseData()
    this.getList()
  },
  activated() {
    // this.getList()
    console.log('============activated=============')
  },
  methods: {
    // 删除分类
    deleteRow(id) {
      this.$confirm('是否确认删除该分类？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api_product.delCategory({ id: id }).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.handleFilter()
        })
      })
    },
    // 保存分类
    saveClassify() {
      console.log(this.createInfo, 'createInfocreateInfo')
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          api_product.addOrEditCategory(this.createInfo).then(rs => {
            this.$message.success('操作成功！')
            this.dialogFormVisible = false
            this.handleFilter()
          })
          console.log('校验通过')
        }
      })
    },
    onSuccess(data) {
      console.log(this.createInfo, 472)
      this.createInfo.imgList = this.createInfo.imgList ? this.createInfo.imgList : []
      this.createInfo.imgList.push({
        url: data.response.data,
        uid: data.uid
      })
      this.createInfo.icon = data.response.data
      console.log(this.createInfo.imgList, 'onSuccess', 278)
    },
    onRemove(data) {
      console.log(data, 'onRemove')
      this.createInfo.imgList = data
      this.createInfo.icon = ''
      // console.log(this.createInfo.icon, 'onRemove', 301)
    },
    // 获取数据
    getList() {
      api_product.getCategoryList(this.listQuery).then(res => {
        this.list = res.list
        this.total = res.totalCount
        this.nextSort = res.nextSort
        this.createInfo.sort = this.nextSort
      })
    },
    getBaseData() {
      api_base.pharmacology().then(response => {
        this.baseoptions = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.name = ''
      this.listQuery.type = ''
      this.listQuery.classificationId = ''
      this.listQuery.relationSku = ''
      this.listQuery.dataIntegrity = ''
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    },
    handleCreate() {
      // createInfo: {
      //   name: '',
      //   description: '',
      //   imgList: [],
      //   imgList: [],
      //   icon: '',
      //   sort: ''
      // },
      // 11
      this.createInfo.id = ''
      this.createInfo.name = ''
      this.createInfo.imgList = []
      this.createInfo.icon = ''
      this.createInfo.sort = this.nextSort
      this.createInfo.description = ''

      this.dialogFormVisible = true
      this.resetTemp()
    },
    editProduct(row) {
      console.log(row, 'row')
      this.dialogFormVisible = true
      this.createInfo.name = row.name
      this.createInfo.sort = row.sort
      this.createInfo.imgList = row.icon ? [{
        url: row.icon
      }] : []
      this.createInfo.icon = row.icon
      this.createInfo.id = row.id
      this.createInfo.description = row.description
      // this.$router.push({
      //   path: './info/' + productId
      // })
    },
    editSku(productId) {
      this.productId = productId
      this.$router.push({
        path: './sku/' + productId
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        // this.createInfo = {}
        this.$refs['dataForm'].clearValidate()
      })
    }
  }
}
</script>
