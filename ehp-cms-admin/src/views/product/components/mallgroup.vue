<template>
  <!-- 专题营销 -->
  <div class="mallgroup">
    <div class="toptips">
      <i class="el-icon-warning-outline"></i>
      <span>完善主副标题且至少添加3个商品，即可在患者端首页展示专题营销模块，最多可添加9个专题推荐商品</span>
    </div>
    <div class="special-subject">
      <el-form
        ref="subjectForm"
        :rules="subjectRules"
        :model="subjectObj"
        label-width="80px"
        :inline="true"
      >
        <el-form-item label="主标题" prop="name">
          <el-input
            v-model="subjectObj.name"
            placeholder="主标题不超过5个字"
            :maxlength="5"
          ></el-input>
        </el-form-item>
        <el-form-item label="副标题" prop="subName">
          <el-input
            v-model="subjectObj.subName"
            placeholder="副标题不超过16个字"
            :maxlength="16"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button v-permission="['wms:section:save']" type="primary" @click="saveSubject">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="filter-container">
      <el-button
        v-permission="['wms:section:save']"
        type="primary"
        icon="el-icon-plus"
        :disabled="list && list[0] && list.length===9"
        @click="handleCreate"
      >添加商品({{ list && list[0]?list.length:0 }}/9)</el-button>
      <span class="add-tips">
        只可添加已上架且有货的商品
      </span>
    </div>
    <el-table :key="tableKey" :data="list" fit highlight-current-row>
      <el-table-column label="商品ID" prop="productId" align="center" />
      <el-table-column label="商品编码" prop="productNumber" align="center" />
      <el-table-column label="商品名称" prop="productName" align="center" />
      <el-table-column label="商品状态" prop="typeDescribe" align="center">
        <template slot-scope="{row}">
          <!-- 下架 > 无货 > 有货 -->
          <!-- 商品状态(0:下架，1:正常) -->
          <!-- <div v-if="row.status === 0" class="tag"> 下架</div>
          <div v-else-if="row.remainQuantity === 0" class="tag no"> 无货</div>
          <div v-else class="tag have">有货</div> -->

          <el-tag v-if="row.status === 0" type="danger" size="medium">下架</el-tag>
          <el-tag v-else-if="row.remainQuantity === 0" type="warning" size="medium">无货</el-tag>
          <el-tag v-else size="medium">有货</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="排序" prop="sort" align="center" />
      <el-table-column label="添加时间" prop="createdAt" align="center" />
      <el-table-column label="操作" fixed="right" align="center">
        <!-- v-permission="['wms:medicine:sku:list']" -->
        <template slot-scope="{row}">
          <el-button
            v-permission="['wms:section:save']"
            type="primary"
            @click="editProduct(row)"
          >编辑</el-button>
          <el-button
            v-permission="['wms:section:save']"
            type="danger"
            @click="deleteGoods(row.id)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    /> -->

    <el-dialog :title="createInfo.id?'编辑专题商品':'添加专题商品'" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :model="createInfo" :rules="rules">
        <el-form-item label="商品名称" :label-width="formLabelWidth" prop="productId">
          <el-select
            v-model="searchTxt"
            filterable
            remote
            reserve-keyword
            placeholder="商品名称"
            :remote-method="remoteMethod"
            :loading="loading"
            clearable
            value-key="productId"
            class="inputW"
            @change="changeSelect"
            @clear="clearSelect"
          >
            <el-option v-for="item in goodOptions" :key="item.productId" :label="item.productName" :value="item">
              <div>
                <span>{{ item.productName }}</span>
                <span>库存：{{ item.remainQuantity }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品编码" :label-width="formLabelWidth">
          <el-input
            v-model="createInfo.productNumber"
            :disabled="true"
            class="brandinput"
            placeholder=""
          />
        </el-form-item>
        <el-form-item label="排序号" :label-width="formLabelWidth" prop="sort">
          <el-input-number v-model="createInfo.sort" :min="-999" :max="999" style="width:150px" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="saveGood">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped>
.mallgroup .toptips i{
  color: #1890ff;
  font-size: 16px;
  margin-right: 6px;
}
.mallgroup .toptips {
  width: 100%;
  padding: 10px 10px;
  background: #EDF6FF;
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}
.mallgroup .add-tips{
  margin-left: 20px;
  font-size: 14px;
  color: #666;
}
.mallgroup .special-subject{
}
/*.el-form-item .el-input__inner{width:auto;}*/
.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #1890ff;
  border-color: #1890ff;
}
.brandinput {
  width: 193px;
}
.top {
  display: inline-block;
  margin-left: 10px;
}
.tag{
  width: 80px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #fff;
  background: #1890ff;
  margin-left: 30px;
  border-radius: 4px;
}
.no{
  background: #ff4949;
}
.have{
  background: orange;
}
/* /deep/ .el-image img{
  width: 48px;
}
.el-form-item__content .el-image {
  width: 48px;
} */
</style>
<script>
import api_product from '@/api/product/product'
export default {
  name: 'Productlist',
  filters: {},
  components: {
  },
  data() {
    return {
      tableKey: 0,
      baseoptions: null,
      list: null,
      total: 0,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        name: '',
        type: '',
        classificationId: '',
        relationSku: '',
        dataIntegrity: ''
      },
      dialogFormVisible: false,
      formLabelWidth: '25%',
      createInfo: {
        productId: '',
        sort: ''
      },
      rules: {
        productId: [{ required: true, message: '请选择商品', trigger: ['blur', 'change'] }],
        sort: [{ required: true, message: '请输入排序号', trigger: ['blur', 'change'] }]
      },
      status: '',
      productId: '',
      subjectObj: {
        name: '',
        subName: ''
      },
      subjectRules: {
        name: [{ required: true, message: '请输入主标题', trigger: 'change' }],
        subName: [{ required: true, message: '请输入副标题', trigger: 'change' }]
      },
      loading: false,
      searchTxt: '',
      goodOptions: []
    }
  },
  created() {
    this.getList()
  },
  activated() {
    this.getList()
  },
  methods: {
    saveGood() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          const resultArr = [
            {
              id: this.createInfo.id || '',
              productId: this.createInfo.productId,
              sort: this.createInfo.sort
            }
          ]
          console.log(resultArr, 'resultArr')
          api_product.setZtProducts(
            resultArr
          ).then(rs => {
            this.$message.success('操作成功！')
            this.dialogFormVisible = false
            this.getList()
          })
        }
      })
    },
    handleCreate() {
      this.searchTxt = ''
      this.createInfo.id = ''
      this.createInfo.productNumber = ''
      // this.createInfo.sort = ''

      this.createInfo.productId = ''
      this.createInfo.productName = ''
      this.createInfo.productNumber = ''
      this.dialogFormVisible = true
      // this.resetTemp()
    },
    editProduct(row) {
      this.searchTxt = row.productName
      // 编辑的时候
      this.createInfo.id = row.id
      this.createInfo.productId = row.productId
      this.createInfo.sort = row.sort

      this.createInfo.productName = row.productName
      this.createInfo.productNumber = row.productNumber
      this.dialogFormVisible = true
    },
    changeSelect(val) {
      console.log(val, 'changeSelect')
      this.createInfo.productId = val.productId
      this.createInfo.productName = val.productNumber
      this.createInfo.productNumber = val.productNumber
    },
    clearSelect() {
      this.createInfo.productId = ''
      this.createInfo.productName = ''
      this.createInfo.productNumber = ''
    },
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true
        api_product.getProduct({
          keyword: query,
          pageNo: 1,
          pageSize: 1000
          // nmpaType: 2
        }).then(res => {
          console.log(res, '商品')
          this.goodOptions = res.list || []
          this.loading = false
        })
      } else {
        this.goodOptions = []
      }
    },
    deleteGoods(id) {
      this.$confirm('是否确认删除该专题商品？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api_product.delProduct({ id: id }).then(() => {
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.getList()
        })
      })
    },
    // 保存主标题副标题
    saveSubject() {
      this.$refs.subjectForm.validate(valid => {
        if (valid) {
          // setZhuanti
          api_product.setZhuanti(
            {
              mainTitle: this.subjectObj.name,
              subTitle: this.subjectObj.subName
            }
          ).then(rs => {
            this.$message.success('操作成功！')
          })
        }
      })
    },
    // 获取数据
    getList() {
      api_product.getZhuanti().then(rs => {
        console.log(rs, '专题')
        this.createInfo.sort = rs.nextSort
        this.subjectObj.name = rs.mainTitle
        this.subjectObj.subName = rs.subTitle
        this.list = rs.products
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.name = ''
      this.listQuery.type = ''
      this.listQuery.classificationId = ''
      this.listQuery.relationSku = ''
      this.listQuery.dataIntegrity = ''
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    },
    nextCreate(dataForm) {
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          this.dialogFormVisible = false
          console.log(this.createInfo, 232)
          this.$refs['drugForm'].handleCreate(this.createInfo)
        }
      })
    },
    editSku(productId) {
      this.productId = productId
      this.$router.push({
        path: './sku/' + productId
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        // this.createInfo = {}
        this.$refs['dataForm'].clearValidate()
      })
    }
  }
}
</script>
