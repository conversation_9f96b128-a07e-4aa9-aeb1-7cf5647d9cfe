<template>
  <div class="app-container">
    <el-card class="box-card">
      <table class="table" cellspacing="1" cellpadding="0" border="0">
        <tr>
          <td class="bg-FA" align="left" width="10%">商品编码</td>
          <td width="20%" align="left">{{ productData.medicine.number || '-' }}</td>
          <td class="bg-FA" align="left" width="10%">商品名称</td>
          <td width="20%" align="left">{{ productData.medicine.name || '-' }}</td>
          <td class="bg-FA" align="left" width="10%">型剂</td>
          <td width="20%" align="left">{{ productData.medicine.agentClassificationName || '-' }}</td>
        </tr>
        <tr>
          <td class="bg-FA" align="left" width="10%">剂量规格</td>
          <td width="20%" align="left">{{ productData.medicine.titleSpec || '-' }}</td>
          <td class="bg-FA" align="left" width="10%">制剂单位</td>
          <td width="20%" align="left">{{ productData.medicine.packingUnitLimit || '-' }}</td>
          <td class="bg-FA" align="left" width="10%">包装单位</td>
          <td width="20%" align="left">{{ productData.medicine.packingUnit || '-' }}</td>
        </tr>
        <tr>
          <td class="bg-FA" align="left" width="10%">说明书规格</td>
          <td width="20%" align="left">{{ productData.medicine.specification || '-' }}</td>
          <td class="bg-FA" width="10%" align="left">适应症</td>
          <td colspan="5" align="left">{{ productData.medicine.indications || '-' }}</td>
        </tr>
      </table>
    </el-card>
    <el-card class="box-card">
      <el-button
        v-permission="['wms:medicine:sku:update']"
        style="margin-bottom: 20px;"
        type="primary"
        @click="newSku"
      >+ 新增Sku</el-button>
      <el-table
        :key="tableKey"
        :data="productData.list"
        fit
        highlight-current-row
        :row-style="{ height: '52px' }"
        :header-row-style="{ height: '52px' }"
        :header-cell-style="{
          background: '#F8F9FB'
        }"
      >
        <el-table-column label="操作" prop="defaultSku" width="80px" align="center">
          <template slot-scope="{ row }">
            <el-button v-permission="['wms:medicine:update']" type="text" @click="editProduct(row)">编辑</el-button>
          </template>
        </el-table-column>
        <el-table-column label="ID" prop="id" align="center" width="100px" />
        <el-table-column label="sku编码" prop="number" width="200px" align="center" />
        <el-table-column label="sku名称" prop="name" align="left" />
        <el-table-column label="销售价格" prop="salePrice" width="300px" align="left" />
        <el-table-column label="更新时间" prop="changedAt" align="left" width="200px">
          <template slot-scope="{ row }">
            {{ row.changedAt || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="上架状态" prop="statusDescribe" align="center" width="110px" />
        <el-table-column label="是否默认" prop="defaultSku" width="80px" align="center">
          <template slot-scope="{ row }">
            <div class="fixedradio" @click="changeDefault(row)"></div>
            <label>
              <input v-model="defaultSkus" type="radio" :value="row.id" />
              <span v-if="row.defaultSku"></span>
              <span v-else></span>
            </label>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 抽屉 -->
    <el-drawer :title="title" size="45%" :visible.sync="drawer" :direction="direction" :before-close="handleClose">
      <div class="demo-drawer__content">
        <el-form ref="dataForm" :model="skuData" :rules="rules" :inline="false" label-width="80px">
          <label>基础信息</label>
          <el-form-item style="margin-top: 20px;" label="是否默认" prop="defaultSku">
            <DictRadio v-model="skuData.defaultSku" type="default_sku" />
          </el-form-item>
          <el-form-item class="elbottom" label="sku名称" prop="brandName">
            <el-input v-model="skuData.name" style="width: 400px;" disabled />
          </el-form-item>
          <el-form-item label="包装规格" required>
            <div style="display: flex; align-items: center;">
              <el-input v-model="productData.medicine.titleSpec" style="width: 150px !important;" placeholder="-" disabled clearable />
              <span style="margin: 0 10px;">*</span>
              <el-form-item prop="packingUnitNumber" style="margin-bottom: 0 !important;">
                <el-input
                  v-model="skuData.packingUnitNumber"
                  style="width: 150px !important;"
                  placeholder="制剂数量"
                  @keyup.native="changeSkuName"
                >
                  <template slot="append">
                    {{ productData.medicine.packingUnitLimit }}
                  </template>
                </el-input>
              </el-form-item>
              <span style="margin: 0 10px;">/</span>
              <el-input v-model="productData.medicine.packingUnit" style="width: 50px !important;" disabled />
              <span style="margin-left: 20px; color: #1890ff; cursor: pointer;" @click="goExample">查看示例</span>
            </div>
          </el-form-item>
          <el-form-item label="销售价格" prop="salePrice">
            <el-input v-model="skuData.salePrice" style="width: 200px;" placeholder="请输入内容" clearable @keyup.native="formatPrice">
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
          <el-form-item label="展示价格">
            <el-input
              v-model="skuData.displayPrice"
              style="width: 200px;"
              placeholder="请输入"
              clearable
              @keyup.native="formatPricedisplayPrice"
            >
              <template slot="append">元</template>
            </el-input>
            <el-tooltip class="item" effect="dark" content="此价格将在患者侧列表和详情页进行展示" placement="right">
              <i style="font-size: 20px; margin: 5px 0 0 5px;" class="el-icon-question"></i>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="上架状态" prop="status">
            <DictRadio v-model="skuData.status" type="sku_status" />
          </el-form-item>
          <el-divider></el-divider>
          <label>其他信息</label>
          <el-form-item style="margin-top: 20px;" label="图片列表" prop="images">
            <div class="imgcontent">
              <div class="imglist">
                <div v-for="(fileitem, index) in skuData.images" :key="index" class="imgbox">
                  <div class="box">
                    <input
                      :id="'imgfile' + index"
                      :ref="'imgfile' + index"
                      :name="'imgfile' + index"
                      class="imgfile"
                      type="file"
                      multiple="false"
                      accept="image/png, image/gif, image/jpeg"
                      @change="handleFileChange($event, index)"
                    />
                    <label :for="'imgfile' + index"></label>
                    <div class="img">
                      <i v-if="index === 0" class="el-icon-star-on start-on"></i>
                      <div>
                        <img :src="fileitem" />
                      </div>
                    </div>
                  </div>
                  <div v-if="index === 0" class="cz">
                    <i class="el-icon-star-on start-off"></i>
                    <i class="el-icon-d-arrow-left start-off"></i>
                    <i class="el-icon-d-arrow-right start-off"></i>
                    <span style="flex: 1;"></span>
                    <i class="el-icon-circle-close start-off"></i>
                  </div>
                  <div v-else class="cz">
                    <i class="el-icon-star-on start-on" @click="checkIndex(index)"></i>
                    <i v-if="index === 1" class="el-icon-d-arrow-left start-off"></i>
                    <i v-else class="el-icon-d-arrow-left start-on" @click="leftIndex(index)"></i>
                    <i v-if="index < skuData.images.length - 1" class="el-icon-d-arrow-right start-on" @click="rightIndex(index)"></i>
                    <i v-else class="el-icon-d-arrow-right start-off"></i>
                    <span style="flex: 1;"></span>
                    <i class="el-icon-circle-close icon-close" @click="delIndex(index)"></i>
                  </div>
                </div>
                <div v-if="skuData.images.length < 5" class="box">
                  <input
                    :id="'imgfile_' + skuData.images.length"
                    :ref="'imgfile_' + skuData.images.length"
                    :name="'imgfile_' + skuData.images.length"
                    class="imgfile"
                    type="file"
                    multiple="false"
                    accept="image/png, image/gif, image/jpeg"
                    @change="handleFileChange($event)"
                  />
                  <label :for="'imgfile_' + skuData.images.length"></label>
                  <i class="el-icon-circle-plus-outline"></i>
                </div>
              </div>
              <div class="imgtips">* 商品图片尺寸800X800，最少上传1张，最多可上传5张</div>
            </div>
          </el-form-item>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button v-permission="['wms:medicine:sku:update']" type="primary" :loading="loading" @click="saveSku('dataForm')">{{
            loading ? '提交中 ...' : '保 存'
          }}</el-button>
        </div>
      </div>
    </el-drawer>

    <el-dialog width="500px" title="示例图片" :visible.sync="imageDialogVisible">
      <img style="width: 100%;" src="../../assets/images/example.png" alt="" />
    </el-dialog>
  </div>
</template>
<script>
import api_product from '@/api/product/product'
import DictSelect from '@/components/DictSelect'
import DictRadio from '@/components/DictRadio'
import { Loading } from 'element-ui'
export default {
  name: 'Sku',
  components: {
    DictSelect,
    DictRadio
  },
  data() {
    return {
      tableKey: 0,
      defaultSkus: 1, //默认id
      changeSku: {},
      editSkusIndex: 0, //编辑id
      dialogIndex: null,
      moveDirection: null,
      productData: {
        list: [],
        medicine: {}
      },
      skuData: {
        images: []
      },
      rules: {
        packingUnitNumber: [{ required: true, message: '请输入每盒数量', trigger: 'blur' }],
        salePrice: [{ required: true, message: '请输入销售价格', trigger: 'blur' }],
        status: [{ required: true, message: '请选择安全类别', trigger: 'blur' }],
        weight: [{ required: true, message: '请输入包装重量', trigger: 'blur' }],
        packingUnitLimit: [{ required: true, message: '请选择拆分单位', trigger: 'blur' }],
        defaultSku: [{ required: true, message: '请选择是否默认', trigger: 'blur' }],
        images: [{ required: true, message: '请选择图片', trigger: 'blur' }]
      },
      title: '新增SKU',
      drawer: false,
      direction: 'rtl',
      loading: false,
      imageDialogVisible: false
    }
  },
  watch: {
    editSkusIndex: function(newval, oldval) {
      if (newval !== null) {
        this.skuData = this.productData.list[newval]
      }
    }
  },
  created() {
    this.getProductInfo(this.$route.params.productId)
  },
  methods: {
    getProductInfo(productId) {
      api_product.infosku(productId).then((response) => {
        this.productData = response
        if (response.list && response.list.length > 0) {
          this.skuData = response.list[0]
          for (var i = 0; i < response.list.length; i++) {
            if (response.list[i].defaultSku === 0) {
              this.defaultSkus = response.list[i].id
            }
          }
        } else if (response.list && response.list.length === 0) {
          this.skuData = {
            productId: this.productData.medicine.productId,
            images: []
          }
        }
        this.$set(this.skuData, 'titleSpec', this.productData.medicine.titleSpec)
      })
    },
    changeDefault(skuRow) {
      this.changeSku = skuRow

      this.$confirm('切换默认sku为：' + this.changeSku.name, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.defalutSure()
      })
    },
    defalutSure() {
      this.defaultSkus = this.changeSku.id
      this.changeSku.defaultSku = 1
      api_product.updateDefatule(this.changeSku.productId, this.changeSku.id).then((response) => {
        this.productData = response
        if (response.list && response.list.length > 0) {
          this.skuData = response.list[0]
        }
      })
    },
    doSave() {
      const loadingInstance = Loading.service({ fullscreen: true })
      this.skuData.titleSpec = this.productData.medicine.titleSpec || ''
      var sku = Object.assign({}, this.skuData)
      sku.weight = 1
      api_product
        .skuupdate(sku)
        .then((response) => {
          this.productData = response
          if (this.editSkusIndex === null) {
            this.editSkusIndex = this.productData.list.length - 1
            if (this.productData.list[this.productData.list.length - 1].defaultSku === 0) {
              this.defaultSkus = this.productData.list[this.productData.list.length - 1].defaultSku
            }
          }
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          this.drawer = false
          this.getProductInfo(this.productData.medicine.productId)
        })
        .finally(function() {
          loadingInstance.close()
        })
    },
    saveSku(dataForm) {
      this.$refs[dataForm].validate((valid) => {
        if (valid) {
          if (this.skuData.displayPrice !== '' && (this.skuData.salePrice > this.skuData.displayPrice)) {
            this.$confirm('当前销售价格大于展示价格，是否确认保存?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(() => {
                this.doSave()
              })
              .catch(() => {})
          } else {
            if (this.skuData.displayPrice == '') {
              this.skuData.displayPrice = null
            }
            this.doSave()
          }
        }
      })
    },
    // saveSku(dataForm) {
    //   this.$refs[dataForm].validate((valid) => {
    //     if (valid) {
    //       const loadingInstance = Loading.service({ fullscreen: true })
    //       this.skuData.titleSpec = this.productData.medicine.titleSpec || ''
    //       var sku = Object.assign({}, this.skuData)
    //       sku.weight = 1
    //       api_product
    //         .skuupdate(sku)
    //         .then((response) => {
    //           this.productData = response
    //           if (this.editSkusIndex === null) {
    //             this.editSkusIndex = this.productData.list.length - 1
    //             if (this.productData.list[this.productData.list.length - 1].defaultSku === 0) {
    //               this.defaultSkus = this.productData.list[this.productData.list.length - 1].defaultSku
    //             }
    //           }
    //           this.$message({
    //             message: '保存成功',
    //             type: 'success'
    //           })
    //           this.drawer = false
    //           this.getProductInfo(this.productData.medicine.productId)
    //         })
    //         .finally(function() {
    //           loadingInstance.close()
    //         })
    //     }
    //   })
    // },
    changeSkuName() {
      let pName
      if (this.skuData.id) {
        pName =
          (this.productData.medicine.titleSpec ? this.productData.medicine.titleSpec + '*' : '') +
          this.skuData.packingUnitNumber +
          this.productData.medicine.packingUnitLimit +
          '/' +
          this.productData.medicine.packingUnit
      } else {
        pName =
          (this.productData.medicine.titleSpec ? this.productData.medicine.titleSpec + '*' : '') +
          (this.skuData.packingUnitNumber ? this.skuData.packingUnitNumber : '') +
          this.productData.medicine.packingUnitLimit +
          '/' +
          this.productData.medicine.packingUnit
      }
      this.$set(this.skuData, 'packingSpec', pName)
      this.$set(this.skuData, 'name', this.productData.medicine.name + ' ' + pName)
    },
    formatPrice(e) {
      // 通过正则过滤小数点后两位
      e.target.value = e.target.value.match(/^\d*(\.?\d{0,2})/g)[0] || null
      this.skuData.salePrice = e.target.value
    },
    formatPricedisplayPrice(e) {
      e.target.value = e.target.value.match(/^\d*(\.?\d{0,2})/g)[0] || null
      this.skuData.displayPrice = e.target.value
    },
    handleFileChange(event, index) {
      if (!this.skuData.images) {
        this.skuData.images = []
      }

      const param = new FormData() // 创建form对象
      param.append('file', event.target.files[0]) //对应后台接收图片名
      api_product.upload(param).then(
        (response) => {
          //成功回调
          if (index !== undefined) {
            this.skuData.images.splice(index, 1, response)
          } else {
            this.skuData.images.push(response)
            this.$refs['imgfile_' + (this.skuData.images.length - 1)].value = ''
          }
        },
        (error) => {
          if (index !== undefined) {
            this.$refs['imgfile' + index][0].value = ''
          } else {
            this.$refs['imgfile_' + this.skuData.images.length].value = ''
          }
        }
      )
    },
    newSku() {
      this.drawer = true
      this.editSkusIndex = null
      this.skuData = {
        productId: this.productData.medicine.productId,
        images: []
      }
    },
    editProduct(row) {
      console.log(row, 'row')
      this.title = '编辑Sku'
      this.drawer = true
      this.skuData = { ...row }
    },
    delIndex(index) {
      this.dialogIndex = index
      this.$confirm('删除图片？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.delIndexSure()
      })
    },
    delIndexSure() {
      this.skuData.images.splice(this.dialogIndex, 1)
    },
    checkIndex(index) {
      this.dialogIndex = index
      this.$confirm('设置该图片为商品主图？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.checkIndexSure()
      })
    },
    checkIndexSure() {
      this.changeArray(this.dialogIndex, 0)
    },
    changeArray(index, change) {
      this.skuData.images.splice(index, 1, ...this.skuData.images.splice(change, 1, this.skuData.images[index]))
    },
    leftIndex(index) {
      if (index > 1) {
        this.dialogIndex = index
        this.moveDirection = -1
        this.$confirm('图片位置迁移？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.moveSure()
        })
      } else {
        console.log('no index')
      }
    },
    rightIndex(index) {
      if (index < this.skuData.images.length - 1) {
        this.dialogIndex = index
        this.moveDirection = 1
        this.$confirm('图片位置迁移？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.moveSure()
        })
      } else {
        console.log('no index')
      }
    },
    moveSure() {
      if (this.moveDirection === 1) {
        this.changeArray(this.dialogIndex, this.dialogIndex + 1)
      } else if (this.moveDirection === -1) {
        this.changeArray(this.dialogIndex, this.dialogIndex - 1)
      }
    },
    handleClose(done) {
      this.$confirm('确认关闭吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.drawer = false
          this.$nextTick(() => {
            this.$refs['dataForm'].clearValidate()
          })
        })
        .catch((e) => {})
        .finally((res) => {})
    },
    goExample() {
      this.imageDialogVisible = true
    }
  }
}
</script>
<style lang="scss" scoped>
.table {
  width: 100%;
  background-color: #dfe6ec;
  text-align: center;
  margin-bottom: 10px;
  font-size: 14px;
  color: #909399;
}

.table tr {
  height: 50px;
}

.table tr td {
  background-color: #fff;
  padding: 10px;
  color: #333;
}

.bg-FA {
  background: #fafafa !important;
  color: #666 !important;
}

.box-card {
  margin-bottom: 20px;
}

::v-deep .el-drawer__body {
  margin: 0px 20px 20px 20px;
  overflow: auto;
}

.demo-drawer__content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.demo-drawer__content form {
  flex: 1;
}

.demo-drawer__footer {
  display: flex;
  justify-content: flex-end;
}

::v-deep .el-drawer__header > :first-child {
  color: #333;
}

::v-deep .el-drawer__close-btn {
  padding-bottom: 20px;
}

::v-deep .el-drawer__header {
  height: 60px;
  background: #f7f8fa;
  box-shadow: 0px 1px 5px 0px rgba(184, 183, 183, 0.5);
}

.imgfile {
  font-size: 0; /* 为了去掉‘未选择任何文件’这几个字，也可以随便弄到哪里*/
  position: absolute;
  left: -9999px;
}
/* 注意不是直接input > input[type=button] 哦*/
.imgfile::-webkit-file-upload-button {
  background: #efeeee;
  color: #333;
  border: 0;
  padding: 40px 100px;
  border-radius: 5px;
  font-size: 12px;
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.1), 0 0 10px rgba(0, 0, 0, 0.12);
}
.box {
  position: relative;
  margin-bottom: 10px;
  width: 120px;
  height: 120px;
  font-size: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed #ccc;
}
/* 使label充满整个box*/
.box label {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10; /* 这个z-index之后说到*/
}

.labelitem .el-form-item {
  display: flex;
}
.labelitem .el-form-item__content {
  flex: 1;
}

.imgcontent {
  padding: 10px;
}
.imgtips {
  color: #f00;
  text-align: left;
}
.imglist {
  display: flex;
  flex-wrap: wrap;
}
.imglist > div {
  margin-right: 10px;
}
.imgbox .img {
  position: relative;
  height: 120px;
  width: 120px;
  display: table;
  text-align: center;
}
.imgbox .img div {
  display: table-cell;
  vertical-align: middle;
}
.imgbox .img img {
  max-width: 118px;
  max-height: 118px;
}
.imgbox .img i {
  font-size: 22px;
  position: absolute;
  left: 0;
  top: 0;
}
.imgbox .cz {
  display: flex;
}
.imgbox .cz i {
  font-size: 22px;
  margin-right: 2px;
}
.imgadd {
  width: 120px;
  height: 120px;
  font-size: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed #ccc;
}
.start-off {
  color: #ccc;
}
.start-on {
  color: #42b983;
}
.icon-close {
  color: #f00;
}
.fixedradio {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
</style>
