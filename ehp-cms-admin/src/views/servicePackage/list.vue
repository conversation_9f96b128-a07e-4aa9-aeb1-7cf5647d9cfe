<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button
        v-waves
        type="primary"
        icon="el-icon-plus"
        @click="addItem"
      >添加
      </el-button>
      <el-input
        v-model="listQuery.name"
        placeholder="名称"
        clearable
        class="filter-item"
        style="width: 200px;"
        @keyup.enter.native="handleFilter"
      />
      <el-button type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      :row-style="{ height: '42px' }"
      :header-row-style="{ height: '42px' }"
    >
      <el-table-column label="ID" prop="id" align="center" width="50px" />
      <el-table-column label="名称" prop="name" align="center">
        <template slot-scope="{row}">
          <span
            class="service-package-name"
            :title="'点击复制: ' + row.name"
            @click="copyItem(row)"
          >
            {{ row.name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="主图" prop="image" align="center" width="120px">
        <template slot-scope="{row}">
          <el-image
            v-if="row.image"
            style="width: 90px; height: 90px"
            :src="row.image"
            :preview-src-list="[row.image]"
            fit="cover"
          />
          <span v-else style="color: #c0c4cc;">暂无图片</span>
        </template>
      </el-table-column>
      <el-table-column label="原价" prop="originalPrice" align="center" width="100px">
        <template slot-scope="{row}">
          <span v-if="row.originalPrice" style="color: #909399; text-decoration: line-through;">
            ¥{{ parseFloat(row.originalPrice).toFixed(2) }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="优惠价" prop="preferentialPrice" align="center" width="100px">
        <template slot-scope="{row}">
          <span v-if="row.preferentialPrice" style="color: #f56c6c; font-weight: bold;">
            ¥{{ parseFloat(row.preferentialPrice).toFixed(2) }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="归属" prop="affiliation" align="center" width="80px">
        <template slot-scope="{row}">
          <el-tag
            :type="getAffiliationTagType(row.affiliation)"
            size="small"
          >
            {{ getAffiliationText(row.affiliation) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="有效期" prop="validityTime" align="center" width="100px">
        <template slot-scope="{row}">
          {{ formatValidPeriod(row.validityTime) }}
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createdAt" align="center" width="160px">
        <template slot-scope="{row}">
          {{ formatDateTime(row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" align="center" width="250">
        <template slot-scope="{row}">
          <el-button
            v-permission="['programme:list:update']"
            :type="row.status === 1 ? 'warning' : 'primary'"
            size="mini"
            @click="handleStatusChange(row)"
          >{{ row.status === 1 ? '禁用' : '启用' }}</el-button>
          <el-button
            v-permission="['programme:list:update']"
            type="primary"
            size="mini"
            @click="editItem(row)"
          >编辑</el-button>
          <el-button
            v-permission="['programme:list:del']"
            type="danger"
            size="mini"
            @click="deleteItem(row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!-- 使用新的表单组件 -->
    <el-dialog
      :title="dialogProgTitle"
      :visible.sync="dialogTableVisible"
      width="80%"
      top="2vh"
      :close-on-click-modal="false"
    >
      <programme-form
        ref="programmeForm"
        :initial-data="editingProgramme"
        @submit="handleFormSubmit"
        @cancel="dialogTableVisible = false"
      />
    </el-dialog>

    <!-- 复制服务包对话框 -->
    <el-dialog
      title="复制服务包"
      :visible.sync="copyDialogVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <div style="margin-bottom: 20px; padding: 12px; background-color: #f5f7fa; border-radius: 4px;">
        <p style="margin: 0 0 8px 0; color: #606266;">
          即将复制服务包：<strong>{{ copyingItem ? copyingItem.name : '' }}</strong>
        </p>
        <p style="margin: 0; color: #909399; font-size: 12px;">
          原归属：{{ copyingItem ? getAffiliationText(copyingItem.affiliation) : '' }}
        </p>
      </div>
      <el-form :model="copyForm" label-width="80px">
        <el-form-item label="选择归属">
          <el-select v-model="copyForm.affiliation" placeholder="请选择归属" style="width: 100%">
            <el-option label="官方" :value="1" />
            <el-option label="医生" :value="2" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="copyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmCopy">复制</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getList,
  addServicePackage,
  deleteServicePackage as delProg,
  getServicePackageDetail as getProg,
  toggleServicePackageStatus as enableProg
} from '@/api/servicePackage/index'
import waves from '@/directive/waves' // Waves directive
import ProgrammeForm from './components/ProgrammeForm'
export default {
  name: 'Programmelist',

  components: {
    ProgrammeForm
  },

  directives: { waves },
  filters: {},

  data() {
    return {
      dialogTableVisible: false,
      dialogProgTitle: '详情',
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      editingProgramme: null,
      copyDialogVisible: false,
      copyForm: {
        affiliation: undefined
      },
      copyingItem: null
    }
  },

  mounted() {
    this.getList()
    console.log('============mounted=============')
  },

  activated() {
    this.getList()
    console.log('============activated=============')
  },

  methods: {
    // 格式化有效期显示
    formatValidPeriod(period) {
      if (!period) return '-'

      const periodMap = {
        '1': '1个月',
        '3': '3个月',
        '6': '6个月',
        '12': '1年'
      }

      return periodMap[period] || `${period}个月`
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'

      try {
        const date = new Date(dateTime)
        if (isNaN(date.getTime())) return '-'

        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}`
      } catch (error) {
        return '-'
      }
    },

    // 获取归属文本
    getAffiliationText(affiliation) {
      const affiliationMap = {
        1: '官方',
        2: '医生'
      }
      return affiliationMap[affiliation] || '未知'
    },

    // 获取归属标签类型
    getAffiliationTagType(affiliation) {
      const typeMap = {
        1: 'primary', // 官方 - 蓝色
        2: 'success' // 医生 - 绿色
      }
      return typeMap[affiliation] || 'info'
    },

    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },

    handleReset() {
      this.listQuery.name = ''
      this.handleFilter()
    },

    // 获取数据
    getList() {
      console.log('开始获取列表数据，参数：', this.listQuery)
      getList(this.listQuery).then(response => {
        console.log('获取列表数据成功：', response)
        this.list = response.list || []
        this.total = response.totalCount || 0
      }).catch(error => {
        console.error('获取列表数据失败：', error)
        this.$message.error('获取数据失败：' + (error.message || '未知错误'))
        this.list = []
        this.total = 0
      })
    },

    addItem() {
      this.dialogProgTitle = '添加服务包'
      this.editingProgramme = null
      this.dialogTableVisible = true

      // 使用 $nextTick 确保对话框渲染完成后再重置表单
      this.$nextTick(() => {
        if (this.$refs.programmeForm) {
          this.$refs.programmeForm.resetForm()
        }
      })
    },

    deleteItem(item) {
      this.$confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return delProg(item.id)
        })
        .then(() => {
          this.$message.success('删除成功')
          this.getList()
        })
        .catch(error => {
          console.error('删除失败：', error)
          this.$message.error('删除失败：' + (error.message || '未知错误'))
        })
    },
    // 编辑
    editItem(item) {
      this.dialogProgTitle = '编辑服务包'
      this.dialogTableVisible = true

      // 获取方案详情
      getProg(item.id).then(response => {
        console.log('getProg response:', response)

        // 直接使用后端返回的原始数据，让ProgrammeForm组件自己处理转换
        this.editingProgramme = response.data || response

        console.log('Raw data passed to form:', this.editingProgramme)
      }).catch(error => {
        console.error('获取服务包详情失败:', error)
        this.$message.error('获取服务包详情失败')
      })
    },

    // 处理表单提交
    handleFormSubmit(formData) {
      console.log('表单提交成功:', formData)
      // 表单组件内部已经处理了API调用，这里只需要处理成功后的逻辑
      this.getList()
      this.dialogTableVisible = false
    },

    handleStatusChange(row) {
      const newStatus = row.status === 1 ? 0 : 1
      const statusText = newStatus === 1 ? '启用' : '禁用'

      this.$confirm(`确认要${statusText}该服务包吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 使用FormData格式传递数据
          const formData = new FormData()
          formData.append('id', row.id)
          formData.append('enable', newStatus)

          enableProg(formData).then(() => {
            this.$message.success(`${statusText}成功`)
            this.getList()
          }).catch(error => {
            console.error('状态切换失败：', error)
            this.$message.error('状态切换失败：' + (error.message || '未知错误'))
          })
        })
        .catch(() => {})
    },

    // 复制服务包
    copyItem(item) {
      this.copyingItem = item
      this.copyForm.affiliation = undefined
      this.copyDialogVisible = true
    },

    // 确认复制
    confirmCopy() {
      if (this.copyForm.affiliation === undefined) {
        this.$message.error('请选择归属')
        return
      }

      // 获取原服务包详情
      getProg(this.copyingItem.id).then(response => {
        console.log('获取原服务包详情:', response)

        // 构建复制的数据，移除ID相关字段
        const copyData = {
          name: `${response.name}_副本`,
          originalPrice: response.originalPrice,
          preferentialPrice: response.preferentialPrice,
          affiliation: this.copyForm.affiliation, // 使用用户选择的归属
          image: response.image,
          validityTime: response.validityTime,
          skuIds: response.commodityList ? response.commodityList.map(item => item.skuId).join(',') : '',
          textNum: 0,
          videoNum: 0,
          matterParamList: [],
          servicePackageExplain: response.servicePackageInfo ? response.servicePackageInfo.servicePackageExplain : ''
        }

        // 处理问诊权益
        if (response.consultationList && response.consultationList.length > 0) {
          response.consultationList.forEach(consultation => {
            if (consultation.consultationRights === 1) {
              copyData.textNum = consultation.number || 0
            } else if (consultation.consultationRights === 2) {
              copyData.videoNum = consultation.number || 0
            }
          })
        }

        // 处理待办事项
        if (response.matterList && response.matterList.length > 0) {
          copyData.matterParamList = response.matterList.map(matter => ({
            matterTitle: matter.matterTitle,
            matterContent: matter.matterContent,
            matterType: matter.matterType,
            dayTime: matter.dayTime,
            executionTimes: matter.executionTimes
          }))
        }

        console.log('复制数据:', copyData)

        // 调用新增接口
        return addServicePackage(copyData)
      }).then(() => {
        const affiliationText = this.getAffiliationText(this.copyForm.affiliation)
        this.$message.success(`复制成功，新服务包归属为：${affiliationText}`)
        this.copyDialogVisible = false
        this.getList()
      }).catch(error => {
        console.error('复制失败:', error)
        this.$message.error('复制失败: ' + (error.message || '未知错误'))
      })
    }
  }
}
</script>

<style scoped>
.service-package-name {
  color: #409EFF;
  cursor: pointer;
  transition: color 0.3s;
}

.service-package-name:hover {
  color: #66b1ff;
  text-decoration: underline;
}
</style>

<style scoped>
.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #1890ff;
  border-color: #1890ff;
}
.brandinput {
  width: 193px;
}
.tips {
  display: inline-block;
  margin-left: 10px;
}
</style>
