<template>
  <div class="programme-form-wrapper">
    <div class="programme-form-container">
      <el-form
        ref="formRef"
        :model="programmeForm"
        :rules="rules"
        label-position="center"
        label-width="120px"
        class="programme-form"
      >
        <!-- 基本信息卡片 -->
        <el-card class="form-card">
          <div slot="header" class="card-header">
            <span class="card-title">基本信息</span>
          </div>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="名称" prop="name" required>
                <el-input
                  v-model="programmeForm.name"
                  placeholder="请输入名称25个汉字内"
                  maxlength="25"
                  show-word-limit
                  class="full-width"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="原价￥" prop="originalPrice" required>
                <el-input
                  v-model="programmeForm.originalPrice"
                  placeholder="请输入原价"
                  class="full-width"
                  @input="handleOriginalPriceInput"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="优惠价￥" prop="preferentialPrice" required>
                <el-input
                  v-model="programmeForm.preferentialPrice"
                  placeholder="请输入优惠价"
                  class="full-width"
                  @input="handlePreferentialPriceInput"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="归属" prop="affiliation" required>
                <el-select
                  v-model="programmeForm.affiliation"
                  placeholder="请选择归属"
                  class="full-width"
                >
                  <el-option label="官方" :value="1" />
                  <el-option label="医生" :value="2" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="主图" prop="imageUrl" required>
                <div class="image-upload-container">
                  <upload-image
                    :value="programmeForm.imageUrl"
                    action="/storage"
                    list-type="picture-card"
                    :limit="1"
                    :show-file-list="false"
                    accept="image/jpeg, image/jpg"
                    :before-upload="beforeImageUpload"
                    @success="onImageSuccess"
                    @remove="onImageRemove"
                  >
                    <i v-if="!programmeForm.imageUrl.length" class="el-icon-plus"></i>
                    <img v-else :src="programmeForm.imageUrl[0].url" class="preview-image" alt="预览图">
                  </upload-image>
                  <div class="image-tips">
                    <i class="el-icon-info"></i>
                    仅支持JPG格式的图片，比例最好为16：9
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="有效期" prop="validityTime" required>
                <div class="validity-input-container">
                  <el-input-number
                    v-model="programmeForm.validityTime"
                    :min="1"
                    :max="365"
                    placeholder="请输入有效期"
                    style="width: 200px;"
                  />
                  <span class="validity-unit">天</span>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 方案内容卡片 -->
        <el-card class="form-card">
          <div slot="header" class="card-header">
            <span class="card-title"><span class="required-mark">*</span>服务包内容</span>
          </div>

          <!-- 问诊权益 -->
          <div class="section-container">
            <div class="section-header">
              <span class="section-title"><span class="required-mark">*</span>问诊权益</span>
            </div>
            <el-form-item prop="consultationBenefits">
              <div class="consultation-benefits">
                <el-checkbox-group v-model="programmeForm.consultationBenefits" @change="handleConsultationChange">
                  <el-checkbox label="none" class="consultation-option">无</el-checkbox>
                  <el-checkbox label="text" class="consultation-option">
                    图文问诊
                    <el-input
                      v-if="programmeForm.consultationBenefits.includes('text')"
                      v-model="programmeForm.textNum"
                      :min="1"
                      :max="999"
                      size="small"
                      class="consultation-count"
                      @click.native.stop
                    />
                    <span v-if="programmeForm.consultationBenefits.includes('text')" class="count-unit">次</span>
                  </el-checkbox>
                  <el-checkbox label="video" class="consultation-option">
                    视频问诊
                    <el-input
                      v-if="programmeForm.consultationBenefits.includes('video')"
                      v-model="programmeForm.videoNum"
                      :min="1"
                      :max="999"
                      size="small"
                      class="consultation-count"
                      @click.native.stop
                    />
                    <span v-if="programmeForm.consultationBenefits.includes('video')" class="count-unit">次</span>
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </el-form-item>
          </div>

          <!-- 天数 -->
          <div class="section-container">
            <div class="section-header">
              <span class="section-title"><span class="required-mark">*</span>天数</span>
            </div>
            <el-form-item prop="days">
              <div class="days-input-container">
                <el-input-number
                  v-model="programmeForm.days"
                  :min="1"
                  :max="365"
                  placeholder="请输入天数"
                  style="width: 200px;"
                  @change="handleDaysChange"
                />
                <span class="days-unit">天</span>
              </div>
            </el-form-item>
          </div>

          <!-- 待办事项 -->
          <div class="section-container">
            <div class="section-header">
              <span class="section-title"><span class="required-mark">*</span>待办事项</span>
            </div>
            <div class="todo-summary">
              <div class="todo-info">
                <div v-if="programmeForm.days && Object.keys(programmeForm.todoItemsByDay).length > 0" class="todo-stats">
                  <p class="stats-text">
                    已设置 {{ programmeForm.days }} 天的待办事项，
                    共 {{ getTotalTodoCount() }} 个事项
                  </p>
                  <div class="todo-preview">
                    <div
                      v-for="day in Math.min(3, programmeForm.days)"
                      :key="day"
                      class="day-preview"
                    >
                      <span class="day-label">第{{ day }}天:</span>
                      <span class="todo-names">
                        {{ getDayTodoNames(day) }}
                      </span>
                    </div>
                    <div v-if="programmeForm.days > 3" class="more-days">
                      ...还有{{ programmeForm.days - 3 }}天
                    </div>
                  </div>
                </div>
                <div v-else class="empty-todo-info">
                  <i class="el-icon-info"></i>
                  <p>请先设置天数，然后配置待办事项</p>
                </div>
              </div>
              <el-button
                type="primary"
                icon="el-icon-setting"
                :disabled="!programmeForm.days || programmeForm.days <= 0"
                @click="openTodoManager"
              >
                管理待办事项
              </el-button>
            </div>
          </div>

          <!-- 药品选择 -->
          <div class="section-container">
            <div class="section-header">
              <span class="section-title">商品<span class="section-tips">*只能选择非处方药，处方类药品需要医生开具处方后才可购买</span></span>
              <el-button type="primary" size="small" icon="el-icon-plus" @click="showMedicineDialog">选择商品</el-button></div>
            <div v-if="programmeForm.commodityList && programmeForm.commodityList.length > 0" class="product-table">
              <el-table :data="programmeForm.commodityList" border stripe>
                <el-table-column prop="skuId" label="skuId" width="80" />
                <el-table-column prop="skuVO.number" label="商品编码" width="150" />
                <el-table-column prop="skuVO.name" label="名称" width="200" show-overflow-tooltip />
                <el-table-column prop="skuVO.commonName" label="通用名" width="180" show-overflow-tooltip />
                <el-table-column prop="skuVO.agentClassificationName" label="剂型" width="120" />
                <el-table-column prop="skuVO.packingSpec" label="包装规格" width="120" />
                <el-table-column prop="skuVO.salePrice" label="售价" width="100">
                  <template slot-scope="scope">
                    {{ scope.row.skuVO ? (scope.row.skuVO.salePrice / 100).toFixed(2) : '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="skuVO.productionEnterprise" label="生产企业" width="200" show-overflow-tooltip />
                <el-table-column label="操作" width="80" fixed="right">
                  <template slot-scope="scope">
                    <el-button
                      type="danger"
                      size="mini"
                      icon="el-icon-delete"
                      circle
                      @click="handleRemoveCommodity(scope.row)"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-else class="empty-placeholder">
              <i class="el-icon-plus"></i>
              <p>暂未选择商品</p>
            </div>
          </div>

          <!-- 说明 -->
          <div class="section-container">
            <div class="section-header">
              <span class="section-title">说明</span>
            </div>
            <wangEditor ref="serviceEditor" v-model="programmeForm.servicePackageInfo.servicePackageExplain" :height="300" />
          </div>
        </el-card>
      </el-form>

      <!-- 底部按钮 -->
      <div class="form-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </div>
    </div>

    <!-- 选择器弹窗 -->
    <el-dialog
      :title="'选择' + selectorTypeLabels[currentSelectorType]"
      :visible.sync="selectorDialogVisible"
      width="80%"
      append-to-body
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      destroy-on-close
      custom-class="selector-dialog"
    >
      <product-selector
        ref="productSelector"
        :type="currentSelectorType"
        :selected-items="getSelectedItems()"
        @confirm="handleProductSelection"
        @cancel="selectorDialogVisible = false"
      />
    </el-dialog>

    <!-- 待办事项管理器 -->
    <todo-items-manager
      v-model="programmeForm.todoItemsByDay"
      :visible="todoManagerVisible"
      :days="programmeForm.days"
      @confirm="handleTodoConfirm"
      @cancel="handleTodoCancel"
    />
  </div>
</template>

<script>
import { getCategoryList } from '@/api/product/product'
import { addServicePackage, updateServicePackage } from '@/api/servicePackage'
import UploadImage from '@/components/uploadImage'
import WangEditor from '@/components/wangEditor/index-v5'
import ProductSelector from './ProductSelector.vue'
import TodoItemsManager from './TodoItemsManager.vue'
export default {
  name: 'ProgrammeForm',

  components: {
    UploadImage,
    WangEditor,
    ProductSelector,
    TodoItemsManager
  },

  props: {
    initialData: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      // 表单数据
      programmeForm: {
        id: undefined,
        name: '', // 名称
        originalPrice: '', // 原价
        preferentialPrice: '', // 优惠价
        affiliation: undefined, // 归属：1.官方 2.医生
        image: '', // 主图链接
        validityTime: undefined, // 有效期（天数）
        imageUrl: [], // 图片上传组件使用
        commodityList: [], // 商品信息
        consultationList: [], // 问诊咨询
        matterList: [], // 事项信息
        servicePackageInfo: {
          servicePackageExplain: '' // 说明
        },
        // 前端辅助字段
        consultationBenefits: [], // 问诊权益选择
        textNum: 1, // 图文问诊次数
        videoNum: 1, // 视频问诊次数
        days: undefined, // 天数（用于生成待办事项）
        todoItemsByDay: {} // 按天组织的待办事项
      },
      todoManagerVisible: false, // 待办事项管理器弹框显示状态
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        originalPrice: [
          { required: true, message: '请输入原价', trigger: 'blur' }
        ],
        preferentialPrice: [
          { required: true, message: '请输入优惠价', trigger: 'blur' }
        ],
        affiliation: [
          { required: true, message: '请选择归属', trigger: 'change' }
        ],
        validityTime: [
          { required: true, message: '请输入有效期', trigger: 'blur' },
          { type: 'number', message: '有效期必须为数字', trigger: 'blur' }
        ],
        imageUrl: [
          {
            required: true,
            validator: (_, __, callback) => {
              if (!this.programmeForm.imageUrl.length) {
                callback(new Error('请上传主图'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        consultationBenefits: [
          {
            required: true,
            validator: (_, __, callback) => {
              if (!this.programmeForm.consultationBenefits.length) {
                callback(new Error('请选择问诊权益'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        days: [
          { required: true, message: '请输入天数', trigger: 'blur' },
          { type: 'number', message: '天数必须为数字', trigger: 'blur' }
        ]
      },
      selectorDialogVisible: false,
      currentSelectorType: 'medicine',
      selectorTypeLabels: {
        medicine: '药品',
        food: '食品',
        health: '保健品'
      },
      isEdit: false,
      categoryList: []
    }
  },

  watch: {
    // 监听 initialData 的变化
    initialData: {
      handler(newVal, oldVal) {
        // 只有当 initialData 真正变化时才重新初始化表单
        // 避免在新增模式下不必要的重置
        if (newVal !== oldVal && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.initForm()
        }
      },
      deep: true,
      immediate: false
    }
  },

  created() {
    this.initForm()
    this.getCategory()
  },

  methods: {
    // 重置表单为默认值
    resetForm() {
      // 重置表单数据
      this.programmeForm = {
        id: undefined,
        name: '', // 名称
        originalPrice: '', // 原价
        preferentialPrice: '', // 优惠价
        affiliation: undefined, // 归属
        image: '', // 主图链接
        validityTime: undefined, // 有效期
        imageUrl: [], // 图片上传组件使用
        commodityList: [], // 商品信息
        consultationList: [], // 问诊咨询
        matterList: [], // 事项信息
        servicePackageInfo: {
          servicePackageExplain: '' // 说明
        },
        // 前端辅助字段
        consultationBenefits: [], // 问诊权益
        textNum: 1, // 图文问诊次数
        videoNum: 1, // 视频问诊次数
        days: undefined, // 天数
        todoItemsByDay: {} // 按天组织的待办事项
      }
      this.todoManagerVisible = false
      this.isEdit = false

      // 如果表单引用存在，清除验证
      if (this.$refs.formRef) {
        this.$refs.formRef.clearValidate()
      }

      // 如果富文本编辑器存在，清空其内容
      this.$nextTick(() => {
        if (this.$refs.serviceEditor) {
          this.$refs.serviceEditor.setContent('')
        }
      })
    },

    initForm() {
      // 如果是编辑模式，填充数据
      if (this.initialData && this.initialData.id) {
        this.isEdit = true
        // 基础字段映射
        this.programmeForm.id = this.initialData.id
        this.programmeForm.name = this.initialData.name || ''
        this.programmeForm.originalPrice = this.initialData.originalPrice ? String(this.initialData.originalPrice) : ''
        this.programmeForm.preferentialPrice = this.initialData.preferentialPrice ? String(this.initialData.preferentialPrice) : ''
        this.programmeForm.affiliation = this.initialData.affiliation
        this.programmeForm.validityTime = this.initialData.validityTime

        // 处理图片数据格式
        if (this.initialData.image) {
          this.programmeForm.imageUrl = [{ url: this.initialData.image }]
        } else {
          this.programmeForm.imageUrl = []
        }

        // 处理问诊权益
        this.programmeForm.consultationBenefits = []
        if (this.initialData.textNum && this.initialData.textNum > 0) {
          this.programmeForm.consultationBenefits.push('text')
          this.programmeForm.textNum = this.initialData.textNum
        }
        if (this.initialData.videoNum && this.initialData.videoNum > 0) {
          this.programmeForm.consultationBenefits.push('video')
          this.programmeForm.videoNum = this.initialData.videoNum
        }
        // 如果没有问诊权益，设置为"无"
        if (this.programmeForm.consultationBenefits.length === 0) {
          this.programmeForm.consultationBenefits = ['none']
        }

        // 处理商品列表
        this.programmeForm.commodityList = this.initialData.commodityList || []

        // 处理说明内容
        this.programmeForm.servicePackageInfo.servicePackageExplain = this.initialData.servicePackageExplain || ''

        // 处理待办事项数据 - 从matterMap转换为todoItemsByDay
        this.programmeForm.todoItemsByDay = {}
        if (this.initialData.matterMap && Object.keys(this.initialData.matterMap).length > 0) {
          console.log('原始matterMap数据:', this.initialData.matterMap)

          // 计算最大天数
          const dayKeys = Object.keys(this.initialData.matterMap).map(key => parseInt(key))
          this.programmeForm.days = dayKeys.length > 0 ? Math.max(...dayKeys) : undefined

          console.log('计算出的天数:', this.programmeForm.days)
          console.log('天数keys:', dayKeys)

          // 转换待办事项数据
          Object.keys(this.initialData.matterMap).forEach(day => {
            const dayNum = parseInt(day)
            const matters = this.initialData.matterMap[day] || []

            console.log(`第${dayNum}天的原始数据:`, matters)

            this.programmeForm.todoItemsByDay[dayNum] = matters.map(matter => {
              // 根据matterType确定类型
              const typeMap = {
                1: 'normal', // 普通事项
                2: 'input', // 需用户输入
                3: 'upload' // 需用户传图
              }

              const todoItem = {
                type: typeMap[matter.matterType] || 'normal',
                name: matter.matterTitle || '',
                content: matter.matterContent || matter.matterTitle || '',
                executionTimes: matter.executionTimes
                  ? matter.executionTimes.split(',').map(time => ({ time: time.trim() }))
                  : [{ time: '' }]
              }

              console.log(`转换后的待办事项:`, todoItem)
              return todoItem
            })
          })

          console.log('最终的todoItemsByDay:', this.programmeForm.todoItemsByDay)
        } else {
          console.log('matterMap为空或不存在')
          this.programmeForm.days = undefined
        }

        // 设置富文本编辑器内容（延迟执行确保组件已挂载）
        this.$nextTick(() => {
          if (this.$refs.serviceEditor && this.programmeForm.servicePackageInfo.servicePackageExplain) {
            this.$refs.serviceEditor.setContent(this.programmeForm.servicePackageInfo.servicePackageExplain)
          }
        })
      } else {
        // 新增模式，确保isEdit为false
        this.isEdit = false
      }
      console.log(this.programmeForm, '===========init========')
    },

    // 获取分类
    async getCategory() {
      try {
        const params = { pageNo: 1, pageSize: 10, type: 2 }
        const response = await getCategoryList(params)
        this.categoryList = response.list
      } catch (error) {
        console.error('Failed to get classify list:', error)
      }
    },

    // 处理原价输入
    handleOriginalPriceInput(value) {
      // 只允许输入数字和小数点
      const reg = /[^\d.]/g
      this.programmeForm.originalPrice = value.replace(reg, '')

      // 确保只有一个小数点
      if (this.programmeForm.originalPrice.split('.').length > 2) {
        this.programmeForm.originalPrice = this.programmeForm.originalPrice.slice(0, this.programmeForm.originalPrice.lastIndexOf('.')) +
                                  this.programmeForm.originalPrice.slice(this.programmeForm.originalPrice.lastIndexOf('.') + 1)
      }

      // 限制小数点后最多两位
      if (this.programmeForm.originalPrice.includes('.')) {
        const parts = this.programmeForm.originalPrice.split('.')
        if (parts[1].length > 2) {
          this.programmeForm.originalPrice = parts[0] + '.' + parts[1].slice(0, 2)
        }
      }

      // 限制最大值为30000
      if (parseFloat(this.programmeForm.originalPrice) > 30000) {
        this.programmeForm.originalPrice = '30000'
      }
    },

    // 处理优惠价输入
    handlePreferentialPriceInput(value) {
      // 只允许输入数字和小数点
      const reg = /[^\d.]/g
      this.programmeForm.preferentialPrice = value.replace(reg, '')

      // 确保只有一个小数点
      if (this.programmeForm.preferentialPrice.split('.').length > 2) {
        this.programmeForm.preferentialPrice = this.programmeForm.preferentialPrice.slice(0, this.programmeForm.preferentialPrice.lastIndexOf('.')) +
                                  this.programmeForm.preferentialPrice.slice(this.programmeForm.preferentialPrice.lastIndexOf('.') + 1)
      }

      // 限制小数点后最多两位
      if (this.programmeForm.preferentialPrice.includes('.')) {
        const parts = this.programmeForm.preferentialPrice.split('.')
        if (parts[1].length > 2) {
          this.programmeForm.preferentialPrice = parts[0] + '.' + parts[1].slice(0, 2)
        }
      }

      // 限制最大值为30000
      if (parseFloat(this.programmeForm.preferentialPrice) > 30000) {
        this.programmeForm.preferentialPrice = '30000'
      }
    },

    // 处理问诊权益变化
    handleConsultationChange(value) {
      // 如果选择了"无"，则清除其他选项
      if (value.includes('none')) {
        this.programmeForm.consultationBenefits = ['none']
      } else {
        // 如果选择了其他选项，则移除"无"
        const index = this.programmeForm.consultationBenefits.indexOf('none')
        if (index > -1) {
          this.programmeForm.consultationBenefits.splice(index, 1)
        }
      }
    },

    // 处理天数变化
    handleDaysChange(value) {
      console.log('天数变化:', value)
      console.log('当前问诊权益:', this.programmeForm.consultationBenefits)

      if (value && value > 0) {
        // 初始化每天的待办事项，如果不存在的话
        for (let i = 1; i <= value; i++) {
          if (!this.programmeForm.todoItemsByDay[i]) {
            this.$set(this.programmeForm.todoItemsByDay, i, [
              this.createDefaultTodoItem()
            ])
          }
        }

        // 删除超出天数的待办事项
        Object.keys(this.programmeForm.todoItemsByDay).forEach(day => {
          if (parseInt(day) > value) {
            this.$delete(this.programmeForm.todoItemsByDay, day)
          }
        })
      } else {
        this.programmeForm.todoItemsByDay = {}
      }

      console.log('待办事项:', this.programmeForm.todoItemsByDay)
      console.log('变化后问诊权益:', this.programmeForm.consultationBenefits)

      // 强制更新视图
      this.$forceUpdate()
    },

    // 创建默认待办事项
    createDefaultTodoItem() {
      return {
        type: 'normal', // 普通事项、需用户输入、需用户传图
        name: '',
        content: '', // 事项内容
        executionTimes: [{ time: '' }] // 执行时间列表
      }
    },

    // 获取待办事项总数
    getTotalTodoCount() {
      let total = 0
      Object.values(this.programmeForm.todoItemsByDay).forEach(dayTodos => {
        total += dayTodos.length
      })
      return total
    },

    // 获取指定天的待办事项名称
    getDayTodoNames(day) {
      const dayTodos = this.programmeForm.todoItemsByDay[day] || []
      const names = dayTodos.map(todo => todo.name || '未命名').slice(0, 2)
      if (dayTodos.length > 2) {
        names.push(`等${dayTodos.length}项`)
      }
      return names.join('、') || '暂无事项'
    },

    // 打开待办事项管理器
    openTodoManager() {
      this.todoManagerVisible = true
    },

    // 处理待办事项确认
    handleTodoConfirm(todoData) {
      this.programmeForm.todoItemsByDay = todoData
      this.todoManagerVisible = false
      this.$message.success('待办事项已保存')
    },

    // 处理待办事项取消
    handleTodoCancel() {
      this.todoManagerVisible = false
    },

    // 图片上传前的验证
    beforeImageUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/jpg'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('只能上传JPG格式的图片!')
      }

      if (!isLt2M) {
        this.$message.error('图片大小不能超过2MB!')
      }

      return isJPG && isLt2M
    },

    // 图片上传成功
    onImageSuccess(data) {
      this.programmeForm.imageUrl = [{
        url: data.response.data,
        uid: data.uid
      }]
      // 手动触发验证
      this.$nextTick(() => {
        this.$refs.formRef.validateField('imageUrl')
      })
    },

    // 图片删除
    onImageRemove() {
      this.programmeForm.imageUrl = []
      // 手动触发验证
      this.$nextTick(() => {
        this.$refs.formRef.validateField('imageUrl')
      })
    },

    // 显示药品选择弹窗
    showMedicineDialog() {
      this.currentSelectorType = 'medicine'
      this.selectorDialogVisible = true
    },

    // 显示食品选择弹窗
    showFoodDialog() {
      this.currentSelectorType = 'food'
      this.selectorDialogVisible = true
    },

    // 显示保健品选择弹窗
    showHealthProductDialog() {
      this.currentSelectorType = 'health'
      this.selectorDialogVisible = true
    },

    // 获取当前类型的已选择项目
    getSelectedItems() {
      // 将commodityList转换为选择器需要的格式
      return this.programmeForm.commodityList.map(item => ({
        ...item.skuVO,
        skuId: item.skuId
      })) || []
    },

    // 处理商品选择
    handleProductSelection(selectedItems) {
      // 将选择的商品转换为API需要的格式
      this.programmeForm.commodityList = selectedItems.map(item => ({
        skuId: item.skuId,
        skuVO: item
      }))
      this.selectorDialogVisible = false
    },

    // 移除商品
    handleRemoveCommodity(item) {
      this.$confirm('确定要移除该商品吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.programmeForm.commodityList.findIndex(commodity => commodity.skuId === item.skuId)
        if (index !== -1) {
          this.programmeForm.commodityList.splice(index, 1)
          // 同步更新选择器组件中的已选择列表
          if (this.$refs.productSelector) {
            this.$refs.productSelector.handleExternalRemove(item.skuId)
          }
        }
      }).catch(() => {})
    },

    // 提交表单
    submitForm() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          // 验证问诊权益相关字段
          if (this.programmeForm.consultationBenefits.includes('text') && !this.programmeForm.textNum) {
            this.$message.error('请输入图文问诊次数')
            return false
          }
          if (this.programmeForm.consultationBenefits.includes('video') && !this.programmeForm.videoNum) {
            this.$message.error('请输入视频问诊次数')
            return false
          }

          // 验证待办事项
          const todoValidationErrors = []
          Object.keys(this.programmeForm.todoItemsByDay).forEach(day => {
            const dayTodos = this.programmeForm.todoItemsByDay[day]
            dayTodos.forEach((todo, index) => {
              if (!todo.name.trim()) {
                todoValidationErrors.push(`第${day}天事项${index + 1}的名称不能为空`)
              }
              if (todo.type !== 'upload' && todo.executionTimes.length === 0) {
                todoValidationErrors.push(`第${day}天事项${index + 1}需要设置执行时间`)
              }
              if (todo.type !== 'upload') {
                todo.executionTimes.forEach((time, timeIndex) => {
                  if (!time.time) {
                    todoValidationErrors.push(`第${day}天事项${index + 1}的执行时间${timeIndex + 1}不能为空`)
                  }
                })
              }
            })
          })

          if (todoValidationErrors.length > 0) {
            this.$message.error(todoValidationErrors[0])
            return false
          }

          // 转换待办事项数据
          const matterParamList = []
          Object.keys(this.programmeForm.todoItemsByDay).forEach(day => {
            const dayTodos = this.programmeForm.todoItemsByDay[day]
            dayTodos.forEach(todo => {
              const matterTypeMap = {
                'normal': 1,
                'input': 2,
                'upload': 3
              }

              matterParamList.push({
                matterTitle: todo.name,
                matterContent: todo.content || todo.name,
                matterType: matterTypeMap[todo.type] || 1,
                dayTime: day,
                executionTimes: todo.executionTimes ? todo.executionTimes.map(t => t.time).join(',') : ''
              })
            })
          })

          // 转换商品SKU IDs
          const skuIds = this.programmeForm.commodityList.map(item => item.skuId).join(',')

          // 构建提交的数据
          const formData = {
            id: this.programmeForm.id,
            name: this.programmeForm.name,
            originalPrice: Math.round(parseFloat(this.programmeForm.originalPrice || 0)), // 直接使用元值
            preferentialPrice: Math.round(parseFloat(this.programmeForm.preferentialPrice || 0)), // 直接使用元值
            affiliation: this.programmeForm.affiliation,
            image: this.programmeForm.imageUrl.length > 0 ? this.programmeForm.imageUrl[0].url : '',
            validityTime: this.programmeForm.validityTime,
            skuIds: skuIds,
            textNum: this.programmeForm.consultationBenefits.includes('text') ? this.programmeForm.textNum : 0,
            videoNum: this.programmeForm.consultationBenefits.includes('video') ? this.programmeForm.videoNum : 0,
            matterParamList: matterParamList,
            servicePackageExplain: this.programmeForm.servicePackageInfo.servicePackageExplain
          }

          console.log('提交数据:', formData)
          this.saveServicePackage(formData)
        } else {
          return false
        }
      })
    },

    // 保存服务包
    async saveServicePackage(formData) {
      const loading = this.$loading({
        lock: true,
        text: '保存中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        let response
        if (this.isEdit) {
          // 编辑模式
          response = await updateServicePackage(formData)
        } else {
          // 新增模式
          response = await addServicePackage(formData)
        }
        console.log('保存成功:', response)
        this.$message.success(this.isEdit ? '服务包更新成功' : '服务包创建成功')
        this.$emit('submit', formData)
      } catch (error) {
        console.error('保存服务包失败:', error)
        this.$message.error('保存失败，请重试')
      } finally {
        loading.close()
      }
    },

    // 取消表单
    cancel() {
      this.$emit('cancel')
    }
  }}
</script>

<style lang="scss" scoped>
.programme-form-wrapper {
  width: 100%;
  height: 100%;
}

.programme-form-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 120px);
}

.programme-form {
  .form-card {
    margin-bottom: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #ebeef5;
      background-color: #fafafa;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    :deep(.el-form-item) {
      margin-bottom: 22px;

      &:last-child {
        margin-bottom: 0;
      }

      // 增加基本信息卡片中输入框的高度
      .el-input__inner {
        height: 80px;  // 原始高度的两倍
        line-height: 80px;  // 相应调整行高
      }
    }
  }
}

.full-width {
  width: 100%;
}

.order-num-container {
  display: flex;
  align-items: center;

  .tips {
    margin-left: 12px;
    color: #909399;
    font-size: 13px;
  }

  :deep(.el-input-number) {
    width: 160px;
  }
}

.image-upload-container {
  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .image-tips {
    margin-top: 8px;
    color: #909399;
    font-size: 13px;

    .el-icon-info {
      margin-right: 4px;
      color: #409EFF;
    }
  }

  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;

    &:hover {
      border-color: #409EFF;
    }
  }
}

.section-container {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .section-title {
      font-size: 15px;
      font-weight: 500;
      color: #606266;
      .section-tips {
        font-size: 12px;
        color: #909399;
        margin-left: 8px;
      }
    }
  }

  .product-table {
    margin-top: 12px;
    border-radius: 4px;
    overflow: hidden;

    :deep(.el-table) {
      border-radius: 4px;
      th {
        background-color: #f5f7fa;
        color: #606266;
        font-weight: 500;
      }
    }
  }

  .empty-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    background-color: #fafafa;
    border: 1px dashed #dcdfe6;
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      border-color: #c0c4cc;
    }

    i {
      font-size: 32px;
      color: #c0c4cc;
      margin-bottom: 8px;
    }

    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
}

.service-editor {
  margin-bottom: 0;

  :deep(.w-e-text-container) {
    height: 300px !important;
  }
}

.form-footer {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 16px 0;
  text-align: center;
  background-color: #fff;
  box-shadow: 0 -2px 12px 0 rgba(0, 0, 0, 0.05);

  .el-button {
    min-width: 120px;
    margin: 0 10px;
    padding: 12px 20px;
    font-size: 14px;
  }
}

.required-mark {
    color: red;
    margin-right: 4px;
}

.selector-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
    max-height: calc(90vh - 150px);
    overflow-y: auto;
  }

  :deep(.el-dialog) {
    margin-top: 5vh !important;
  }

  :deep(.el-dialog__header) {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;
  }
}

// 问诊权益样式
.consultation-benefits {
  :deep(.el-checkbox-group) {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
  }

  :deep(.el-checkbox) {
    margin-right: 24px;
    margin-bottom: 0;

    &:last-child {
      margin-right: 0;
    }
  }

  :deep(.el-checkbox__label) {
    display: flex;
    align-items: center;
  }

  .consultation-count {
    margin-left: 8px;
    width: 100px;
  }

  .count-unit {
    margin-left: 8px;
    color: #606266;
    font-size: 14px;
  }
}

// 天数输入样式
.days-input-container {
  display: flex;
  align-items: center;

  .days-unit {
    margin-left: 8px;
    color: #606266;
    font-size: 14px;
  }

  :deep(.el-input-number) {
    width: 180px;
  }
}

// 有效期输入样式
.validity-input-container {
  display: flex;
  align-items: center;

  .validity-unit {
    margin-left: 8px;
    color: #606266;
    font-size: 14px;
  }

  :deep(.el-input-number) {
    width: 180px;
  }
}

// 待办事项概览样式
.todo-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;

  .todo-info {
    flex: 1;

    .todo-stats {
      .stats-text {
        margin: 0 0 12px 0;
        color: #606266;
        font-size: 14px;
      }

      .todo-preview {
        .day-preview {
          margin-bottom: 4px;
          font-size: 13px;
          color: #909399;

          .day-label {
            font-weight: 500;
            color: #606266;
          }

          .todo-names {
            margin-left: 8px;
          }
        }

        .more-days {
          font-size: 13px;
          color: #c0c4cc;
          font-style: italic;
        }
      }
    }

    .empty-todo-info {
      display: flex;
      align-items: center;
      color: #909399;

      i {
        margin-right: 8px;
        font-size: 16px;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}
</style>
