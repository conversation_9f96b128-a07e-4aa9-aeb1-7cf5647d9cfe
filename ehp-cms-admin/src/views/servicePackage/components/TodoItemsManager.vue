<template>
  <div class="todo-items-manager">
    <el-dialog
      title="待办事项管理"
      :visible.sync="dialogVisible"
      width="80%"
      :close-on-click-modal="false"
      :before-close="handleClose"
      custom-class="todo-dialog"
      append-to-body
      :modal-append-to-body="true"
      destroy-on-close
    >
      <div class="todo-management">
        <!-- 左侧天数导航 -->
        <div class="todo-days-nav">
          <div
            v-for="day in days"
            :key="day"
            class="day-nav-item"
            :class="{ active: currentEditDay === day }"
            @click="switchEditDay(day)"
          >
            第{{ day }}天待办内容
          </div>
        </div>

        <!-- 右侧待办事项编辑区域 -->
        <div class="todo-edit-area">
          <div v-if="days && currentEditDay" class="todo-editor">
            <!-- 当前编辑天数的待办事项列表 -->
            <div class="todo-items-list">
              <div
                v-for="(item, index) in getCurrentDayTodos()"
                :key="index"
                class="todo-item-card"
              >
                <!-- 删除按钮 -->
                <el-button
                  v-if="index > 0"
                  type="danger"
                  size="mini"
                  class="delete-btn"
                  @click="removeTodoItem(index)"
                >
                  删除
                </el-button>

                <!-- 事项类型选择 -->
                <div class="todo-type-section">
                  <el-radio-group v-model="item.type" @change="handleTodoTypeChange(index)">
                    <el-radio label="normal">普通事项</el-radio>
                    <el-radio label="input">需用户输入</el-radio>
                    <el-radio label="upload">需用户传图</el-radio>
                  </el-radio-group>
                </div>

                <!-- 事项名称输入 -->
                <div class="todo-name-section">
                  <el-input
                    v-model="item.name"
                    placeholder="请输入事项名称"
                    class="todo-name-input"
                  />
                </div>

                <!-- 执行时间设置 (需用户传图类型不显示) -->
                <div v-if="item.type !== 'upload'" class="todo-time-section">
                  <span class="time-label">执行时间</span>
                  <div class="time-list">
                    <div
                      v-for="(time, timeIndex) in item.executionTimes"
                      :key="timeIndex"
                      class="time-item"
                    >
                      <el-time-picker
                        v-model="time.time"
                        format="HH:mm"
                        value-format="HH:mm"
                        placeholder="选择时间"
                        size="small"
                      />
                      <el-button
                        type="danger"
                        size="mini"
                        icon="el-icon-delete"
                        circle
                        @click="removeExecutionTime(index, timeIndex)"
                      />
                    </div>
                    <el-button
                      type="primary"
                      size="small"
                      icon="el-icon-plus"
                      @click="addExecutionTime(index)"
                    >
                      添加时间
                    </el-button>
                  </div>
                </div>
              </div>

              <!-- 添加事项按钮 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-plus"
                class="add-todo-btn"
                @click="addTodoItemToCurrentDay"
              >
                添加事项
              </el-button>
            </div>

            <!-- 底部操作按钮 -->
            <div class="todo-actions">
              <el-button @click="clearAllTodos">清空所有待办</el-button>
              <el-button @click="clearCurrentDayTodos">清空当天待办</el-button>
              <el-button type="primary" @click="applyToNextDay">应用到下一天</el-button>
              <el-button type="primary" @click="applyToAllDays">应用到每天</el-button>
            </div>
          </div>

          <div v-else class="empty-todo-editor">
            <i class="el-icon-info"></i>
            <p>请先设置天数，然后选择要编辑的天数</p>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'TodoItemsManager',

  props: {
    visible: {
      type: Boolean,
      default: false
    },
    days: {
      type: Number,
      default: 0
    },
    value: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      dialogVisible: false,
      currentEditDay: 1,
      todoItemsByDay: {}
    }
  },

  watch: {
    visible: {
      handler(newVal) {
        this.dialogVisible = newVal
        if (newVal) {
          this.initTodoItems()
          // 确保弹窗层级正确
          this.$nextTick(() => {
            // this.ensureDialogZIndex()
          })
        }
      },
      immediate: true
    },

    days: {
      handler(newVal) {
        if (newVal && newVal > 0) {
          this.initTodoItems()
        }
      }
    },

    value: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.todoItemsByDay = JSON.parse(JSON.stringify(newVal))
        }
      },
      deep: true,
      immediate: true
    }
  },

  mounted() {
  },

  methods: {
    // 确保弹窗z-index正确
    ensureDialogZIndex() {
      const dialogElements = document.querySelectorAll('.todo-dialog .el-dialog__wrapper')
      dialogElements.forEach(el => {
        el.style.zIndex = '9999'
      })

      const modalElements = document.querySelectorAll('.v-modal')
      modalElements.forEach(el => {
        if (el.style.zIndex && parseInt(el.style.zIndex) < 9998) {
          el.style.zIndex = '9998'
        }
      })
    },
    // 初始化待办事项
    initTodoItems() {
      if (this.days && this.days > 0) {
        // 初始化每天的待办事项，如果不存在的话
        for (let i = 1; i <= this.days; i++) {
          if (!this.todoItemsByDay[i]) {
            this.$set(this.todoItemsByDay, i, [
              this.createDefaultTodoItem()
            ])
          }
        }

        // 删除超出天数的待办事项
        Object.keys(this.todoItemsByDay).forEach(day => {
          if (parseInt(day) > this.days) {
            this.$delete(this.todoItemsByDay, day)
          }
        })

        // 设置当前编辑天数为第一天
        this.currentEditDay = 1
      }
    },

    // 创建默认待办事项
    createDefaultTodoItem() {
      return {
        type: 'normal', // 普通事项、需用户输入、需用户传图
        name: '',
        executionTimes: [{ time: '' }] // 执行时间列表
      }
    },

    // 获取当前编辑天数的待办事项
    getCurrentDayTodos() {
      return this.todoItemsByDay[this.currentEditDay] || []
    },

    // 切换编辑天数
    switchEditDay(day) {
      this.currentEditDay = day
    },

    // 处理待办事项类型变化
    handleTodoTypeChange(index) {
      const currentDayTodos = this.getCurrentDayTodos()
      const item = currentDayTodos[index]

      // 如果切换到需用户传图，清空执行时间
      if (item.type === 'upload') {
        item.executionTimes = []
      } else if (item.executionTimes.length === 0) {
        // 如果从需用户传图切换到其他类型，添加默认执行时间
        item.executionTimes = [{ time: '' }]
      }
    },

    // 添加执行时间
    addExecutionTime(todoIndex) {
      const currentDayTodos = this.getCurrentDayTodos()
      currentDayTodos[todoIndex].executionTimes.push({ time: '' })
    },

    // 移除执行时间
    removeExecutionTime(todoIndex, timeIndex) {
      const currentDayTodos = this.getCurrentDayTodos()
      currentDayTodos[todoIndex].executionTimes.splice(timeIndex, 1)
    },

    // 添加待办事项到当前天
    addTodoItemToCurrentDay() {
      const currentDayTodos = this.getCurrentDayTodos()
      currentDayTodos.push(this.createDefaultTodoItem())
    },

    // 移除待办事项
    removeTodoItem(index) {
      this.$confirm('确定要删除该待办事项吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const currentDayTodos = this.getCurrentDayTodos()
        currentDayTodos.splice(index, 1)
      }).catch(() => {})
    },

    // 清空所有待办
    clearAllTodos() {
      this.$confirm('确定要清空所有天的待办事项吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重新初始化所有天的待办事项
        for (let i = 1; i <= this.days; i++) {
          this.$set(this.todoItemsByDay, i, [
            this.createDefaultTodoItem()
          ])
        }
      }).catch(() => {})
    },

    // 清空当天待办
    clearCurrentDayTodos() {
      this.$confirm('确定要清空当天的待办事项吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$set(this.todoItemsByDay, this.currentEditDay, [
          this.createDefaultTodoItem()
        ])
      }).catch(() => {})
    },

    // 应用到下一天
    applyToNextDay() {
      if (this.currentEditDay < this.days) {
        const currentTodos = JSON.parse(JSON.stringify(this.getCurrentDayTodos()))
        this.$set(this.todoItemsByDay, this.currentEditDay + 1, currentTodos)
        this.$message.success('已应用到下一天')
      } else {
        this.$message.warning('当前已是最后一天')
      }
    },

    // 应用到每天
    applyToAllDays() {
      this.$confirm('确定要将当前待办事项应用到每一天吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const currentTodos = JSON.parse(JSON.stringify(this.getCurrentDayTodos()))
        for (let i = 1; i <= this.days; i++) {
          this.$set(this.todoItemsByDay, i, JSON.parse(JSON.stringify(currentTodos)))
        }
        this.$message.success('已应用到每一天')
      }).catch(() => {})
    },

    // 验证待办事项
    validateTodoItems() {
      const errors = []
      Object.keys(this.todoItemsByDay).forEach(day => {
        const dayTodos = this.todoItemsByDay[day]
        dayTodos.forEach((todo, index) => {
          if (!todo.name.trim()) {
            errors.push(`第${day}天事项${index + 1}的名称不能为空`)
          }
          if (todo.type !== 'upload' && todo.executionTimes.length === 0) {
            errors.push(`第${day}天事项${index + 1}需要设置执行时间`)
          }
          if (todo.type !== 'upload') {
            todo.executionTimes.forEach((time, timeIndex) => {
              if (!time.time) {
                errors.push(`第${day}天事项${index + 1}的执行时间${timeIndex + 1}不能为空`)
              }
            })
          }
        })
      })
      return errors
    },

    // 处理确定
    handleConfirm() {
      const errors = this.validateTodoItems()
      if (errors.length > 0) {
        this.$message.error(errors[0])
        return
      }

      this.$emit('input', this.todoItemsByDay)
      this.$emit('confirm', this.todoItemsByDay)
      this.dialogVisible = false
    },

    // 处理取消
    handleCancel() {
      this.dialogVisible = false
      this.$emit('cancel')
    },

    // 处理关闭
    handleClose(done) {
      this.$confirm('确认关闭？未保存的更改将丢失。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        done()
        this.$emit('cancel')
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.todo-items-manager {
  :deep(.todo-dialog) {
    .el-dialog__wrapper {
      z-index: 9999 !important;
    }

    .el-dialog {
      margin-top: 5vh !important;
      z-index: 9999 !important;
    }

    .el-dialog__body {
      padding: 20px;
      max-height: calc(90vh - 150px);
      overflow: hidden;
    }
  }
}

// 待办事项管理样式
.todo-management {
  display: flex;
  min-height: 500px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;

  .todo-days-nav {
    width: 200px;
    background-color: #f5f7fa;
    border-right: 1px solid #e4e7ed;

    .day-nav-item {
      padding: 12px 16px;
      cursor: pointer;
      border-bottom: 1px solid #e4e7ed;
      transition: all 0.3s;

      &:hover {
        background-color: #ecf5ff;
      }

      &.active {
        background-color: #409eff;
        color: white;
      }
    }
  }

  .todo-edit-area {
    flex: 1;
    padding: 20px;
    overflow-y: auto;

    .todo-editor {
      .todo-items-list {
        margin-bottom: 20px;

        .todo-item-card {
          position: relative;
          margin-bottom: 20px;
          padding: 16px;
          border: 1px solid #e4e7ed;
          border-radius: 4px;
          background-color: #fff;

          .delete-btn {
            position: absolute;
            top: 8px;
            right: 8px;
          }

          .todo-type-section {
            margin-bottom: 12px;

            :deep(.el-radio-group) {
              display: flex;
              gap: 16px;
            }
          }

          .todo-name-section {
            margin-bottom: 12px;

            .todo-name-input {
              width: 100%;
            }
          }

          .todo-time-section {
            .time-label {
              display: block;
              margin-bottom: 8px;
              font-weight: 500;
              color: #606266;
            }

            .time-list {
              .time-item {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                gap: 8px;

                :deep(.el-time-picker) {
                  width: 120px;
                }
              }
            }
          }
        }

        .add-todo-btn {
          width: 100%;
          margin-top: 16px;
        }
      }

      .todo-actions {
        display: flex;
        gap: 12px;
        padding-top: 20px;
        border-top: 1px solid #e4e7ed;
        flex-wrap: wrap;
      }
    }

    .empty-todo-editor {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 300px;
      color: #909399;

      i {
        font-size: 48px;
        margin-bottom: 16px;
      }

      p {
        margin: 0;
        font-size: 16px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
