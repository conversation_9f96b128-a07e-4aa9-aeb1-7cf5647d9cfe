<template>
  <div class="product-selector">
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="商品名称">
          <el-input v-model="queryParams.name" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="商品编码">
          <el-input v-model="queryParams.code" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
          <el-button type="default" icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="selector-container">
      <div class="left-container">
        <!-- 左侧搜索结果列表 -->
        <div class="result-list">
          <el-table
            ref="productTable"
            :data="productList"
            border
            style="width: 100%"
            height="400"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="number" label="商品编码" width="150" />
            <el-table-column prop="name" label="商品名称" show-overflow-tooltip />
            <el-table-column prop="salePrice" label="售价（元）" width="100" />
            <el-table-column label="操作" width="100">
              <template slot-scope="scope">
                <el-button
                  type="primary"
                  size="mini"
                  :disabled="isSelected(scope.row.skuId)"
                  @click="handleAdd(scope.row)"
                >{{ isSelected(scope.row.skuId) ? '已添加' : '添加' }}</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNo"
            :limit.sync="queryParams.pageSize"
            @pagination="getProductList"
          />
        </div>
      </div>

      <div class="right-container">
        <!-- 右侧已选择列表 -->
        <div class="selected-list">
          <h4>已选择{{ typeLabels[type] }}</h4>
          <el-table
            :data="localSelectedItems"
            border
            style="width: 100%"
            height="400"
          >
            <el-table-column prop="number" label="商品编码" width="150" />
            <el-table-column prop="name" label="商品名称" show-overflow-tooltip />
            <el-table-column prop="quantity" label="数量">
              <template slot-scope="scope">
                <el-input-number
                  v-model="scope.row.quantity"
                  :min="1"
                />
              </template>
            </el-table-column>
            <el-table-column prop="salePrice" label="售价（元）" width="100" />
            <el-table-column label="操作" width="100">
              <template slot-scope="scope">
                <el-button
                  type="danger"
                  size="mini"
                  @click="handleRemove(scope.$index)"
                >移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <div class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>
  </div>
</template>

<script>
import { searchSku } from '@/api/servicePackage/index'
import Pagination from '@/components/Pagination'
export default {
  name: 'ProductSelector',

  components: {
    Pagination
  },

  props: {
    // 选择器类型：medicine-药品, food-食品, health-保健品
    type: {
      type: String,
      required: true,
      validator: value => ['medicine', 'food', 'health'].includes(value)
    },
    selectedItems: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      // 类型标签映射
      typeLabels: {
        medicine: '药品',
        food: '食品',
        health: '保健品'
      },
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: '', // 关键词
        code: '' // sku编码
      },
      // 商品列表
      productList: [],
      // 总记录数
      total: 0,
      // 多选框选中的数据
      multipleSelection: [],
      // 本地已选择的数据
      localSelectedItems: []
    }
  },

  watch: {
    // 监听类型变化，重置选择状态
    type: {
      handler() {
        // 清空已选择的数据
        this.localSelectedItems = [...this.selectedItems]
        console.log(this.localSelectedItems, '外部传入的selectedItems')
        // 重新获取列表数据
        this.getProductList()
        // 清空表格选中状态
        this.$nextTick(() => {
          if (this.$refs.productTable) {
            this.$refs.productTable.clearSelection()
          }
        })
      },
      immediate: false
    },
    // 监听外部传入的selectedItems变化
    selectedItems: {
      handler(newVal) {
        this.localSelectedItems = [...newVal]
        console.log(this.localSelectedItems, '外部传入的selectedItems')
        // 同步表格选中状态
        this.$nextTick(() => {
          if (this.$refs.productTable) {
            this.$refs.productTable.clearSelection()
            this.localSelectedItems.forEach(item => {
              const row = this.productList.find(product => product.skuId === item.skuId)
              if (row) {
                this.$refs.productTable.toggleRowSelection(row, true)
              }
            })
          }
        })
      },
      immediate: false
    }
  },

  created() {
    // 初始化本地已选择的数据
    this.localSelectedItems = [...this.selectedItems]
    this.getProductList()
  },

  methods: {
    // 获取商品列表
    async getProductList() {
      // 这里需要根据type调用不同的API接口
      try {
        const params = {
          ...this.queryParams,
          nmpaType: this.getType() === 1 ? 2 : undefined, // 药品类别：1，处方药；2，OTC药品
          type: this.getType()
        }
        const response = await searchSku(params)

        // 给每个商品添加默认数量值，默认为1
        this.productList = (response.list || []).map(item => ({
          ...item,
          quantity: 1 // 默认数量为1
        }))

        this.total = response.totalCount || 0
        console.log(this.productList, '商品列表')
      } catch (error) {
        console.error('获取商品列表失败:', error)
      }
    },

    getType() {
      return this.type === 'medicine' ? 1 : this.type === 'health' ? 2 : this.type === 'food' ? 4 : 3
    },

    // 搜索按钮点击事件
    handleSearch() {
      this.queryParams.pageNo = 1
      this.getProductList()
    },

    // 重置按钮点击事件
    handleReset() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 10,
        name: '',
        code: ''
      }
      this.getProductList()
    },

    // 表格多选框选中事件
    handleSelectionChange(selection) {
      this.multipleSelection = selection
      // 创建一个新的已选择列表
      const newSelectedItems = [...this.localSelectedItems]

      // 添加新选中的项目
      selection.forEach(item => {
        if (!this.isSelected(item.skuId)) {
          newSelectedItems.push(item)
        }
      })

      // 移除取消选中的项目
      this.localSelectedItems = newSelectedItems.filter(item =>
        selection.some(selected => selected.skuId === item.skuId)
      )
    },

    // 添加商品到已选择列表
    handleAdd(row) {
      // 检查是否已经添加过
      if (!this.isSelected(row.skuId)) {
        // 确保商品有数量属性，默认为1
        const itemToAdd = {
          ...row,
          quantity: row.quantity || 1 // 如果已有quantity属性则使用，否则默认为1
        }
        this.localSelectedItems.push(itemToAdd)
      }
    },

    // 从已选择列表中移除
    handleRemove(index) {
      const removedItem = this.localSelectedItems[index]
      this.localSelectedItems.splice(index, 1)
      // 找到左侧表格中对应的行，取消其选中状态
      const row = this.productList.find(item => item.skuId === removedItem.skuId)
      if (row) {
        this.$refs.productTable.toggleRowSelection(row, false)
      }
    },

    // 处理外部删除商品
    handleExternalRemove(productId) {
      const index = this.localSelectedItems.findIndex(item => item.skuId === productId)
      if (index !== -1) {
        this.localSelectedItems.splice(index, 1)
      }
    },

    // 检查商品是否已在选中列表中
    isSelected(skuId) {
      return this.localSelectedItems.some(item => item.skuId === skuId)
    },

    // 保存选择的数据
    handleSave() {
      this.$emit('confirm', this.localSelectedItems)
    },

    // 取消选择
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped>
.product-selector {
  display: flex;
  flex-direction: column;
}

.filter-container {
  margin-bottom: 20px;
}

.selector-container {
  display: flex;
  flex: 1;
}

.left-container {
  flex: 1;
  margin-right: 10px;
  overflow: auto;
}

.right-container {
  flex: 1;
  overflow: auto;
}

.result-list, .selected-list {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
}

.selected-list h4 {
  margin-top: 0;
  margin-bottom: 10px;
}

.dialog-footer {
  text-align: right;
}

.filter-tips {
  font-size: 14px;
  color: #606266;
  margin-top: 10px;
}
</style>
