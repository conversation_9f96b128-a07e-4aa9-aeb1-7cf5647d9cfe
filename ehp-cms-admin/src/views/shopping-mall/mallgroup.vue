<template>
  <div class="page-warp">
    <el-button style="margin-bottom: 20px;" type="primary" icon="el-icon-plus" @click="handleCreate">新增专题</el-button>
    <pure-table
      v-loading="loading"
      pagination
      :column="specialCols"
      :data="specialList"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    >
    </pure-table>
    <!-- 新增/编辑专题 -->
    <el-dialog width="500px" :title="title" :visible.sync="dialogFormVisible">
      <el-form ref="formRef" :model="specialForm" :rules="rules" label-width="80px">
        <el-row type="flex">
          <el-col :span="24">
            <el-form-item label="排序号">
              <el-input-number size="medium" v-model="specialForm.sort" :min="-999" :max="999" @change="changeSort"></el-input-number>
            </el-form-item>
            <el-form-item label="主标题" prop="mainTitle" class="nr-input">
              <el-input v-model="specialForm.mainTitle" placeholder="主标题不超过5个字" :maxlength="5"></el-input>
            </el-form-item>
            <el-form-item label="副标题" prop="subTitle" class="nr-input">
              <el-input v-model="specialForm.subTitle" placeholder="副标题不超过16个字" :maxlength="16"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="saveSpecial">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import to from 'await-to-js'
import PureTable from '@/components/PureTable'
import { getSpecialList, changeSpecial, delSpecial, saveSpecial } from '@/api/product/product'
export default {
  name: 'MallGroup',
  components: { PureTable },
  data() {
    return {
      loading: false,
      specialList: [],
      specialCols: [
        {
          prop: 'id',
          label: 'ID',
          width: '100px'
        },
        {
          prop: 'mainTitle',
          label: '主标题',
          minWidth: '120px',
          showOverflowTooltip: true
        },
        {
          prop: 'subTitle',
          label: '副标题',
          minWidth: '120px',
          showOverflowTooltip: true
        },
        {
          prop: 'sort',
          label: '排序',
          minWidth: '120px'
        },
        {
          prop: 'createdAt',
          label: '创建时间',
          minWidth: '120px'
        },
        {
          label: '操作',
          width: '250px',
          render: (h, { row }) => {
            // 提取按钮权限
            const hasPermission = this.$store.getters.permissions.includes('wms:section:save')
            if (!hasPermission) {
              return null
            }
            return (
              <span>
                <el-button type="text" onClick={() => this.onEdit(row)}>
                  编辑
                </el-button>
                <el-button type="text" onClick={() => this.onSpecialEdit(row)}>
                  专题管理
                </el-button>
                <el-button type="text" style={{ color: row.status ? 'red' : '' }} onClick={() => this.onToggle(row)}>
                  {row.status ? '停用' : '启用'}
                </el-button>
                <el-button type="text" style={{ color: 'red' }} onClick={() => this.onRemove(row.id)}>
                  删除
                </el-button>
              </span>
            )
          }
        }
      ],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'sort'
      },
      title: '新增专题',
      dialogFormVisible: false,
      specialForm: {
        sort: '', // 排序号
        mainTitle: '', // 主标题
        subTitle: '' // 副标题
      },
      rules: {
        mainTitle: [{ required: true, message: '请输入主标题', trigger: 'blur' }],
        subTitle: [{ required: true, message: '请输入副标题', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      const data = {
        type: 1,
        ...this.listQuery
      }
      const [err, res] = await to(getSpecialList(data))
      if (err) {
        this.specialList = []
        this.loading = false
      } else {
        const { totalCount, list } = res
        this.total = totalCount
        this.specialList = list || []
        this.loading = false
      }
    },
    getMaxValue(arr, key) {
      return arr.reduce((max, item) => Math.max(max, item[key]), -1)
    },
    // 新增标题
    handleCreate() {
      this.specialForm = {}
      const maxValue = this.getMaxValue(this.specialList, 'sort')
      this.specialForm.sort = maxValue + 1
      this.dialogFormVisible = true
    },
    // 编辑
    onEdit(row) {
      this.title = '编辑专题'
      this.specialForm.id = row.id
      this.specialForm.sort = row.sort
      this.specialForm.mainTitle = row.mainTitle
      this.specialForm.subTitle = row.subTitle
      this.dialogFormVisible = true
    },
    // 专题管理
    onSpecialEdit(row) {
      this.$router.push({ path: './mallgroup-edit', query: { id: row.id } })
    },
    // 启用停用
    onToggle(row) {
      const msg = row.status ? '停用' : '启用'
      this.$confirm(`是否确认${msg}该专题商品？`, '提示', { type: 'warning' })
        .then(() => {
          const data = {
            sectionId: row.id,
            status: row.status ? 0 : 1
          }
          changeSpecial(data).then((res) => {
            this.getList()
            this.$message.success(`${msg}成功`)
          })
        })
        .catch(() => {
          return false
        })
    },
    // 删除
    onRemove(id) {
      this.$confirm('是否确认删除该专题？', '提示', { type: 'warning' })
        .then(() => {
          delSpecial(id).then((res) => {
            this.getList()
            this.$message.success('删除成功')
          })
        })
        .catch(() => {
          return false
        })
    },
    saveSpecial() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          const params = {
            type: 1,
            // id: this.specialId ? this.specialId : undefined,
            ...this.specialForm
          }
          const [err, res] = await to(saveSpecial(params))
          if (!err) {
            this.dialogFormVisible = false
            this.$message({
              type: 'success',
              message: '保存成功'
            })
            this.getList()
          }
        } else {
          return false
        }
      })
    },
    changeSort() {
      this.$forceUpdate()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .nr-input .el-input__inner {
  height: 36px;
  line-height: 36px;
  width: 300px;
}
.page-warp {
  padding: 20px;
}
</style>
