<template>
  <!-- 开方购药 -->
  <div class="mallgroup">
    <div class="toptips">
      <i class="el-icon-warning-outline"></i>
      <span>添加至该列表的商品，需完成开方购药咨询流程后才可进行购买</span>
    </div>
    <div class="filter-container">
      <el-input
        v-model="listQuery.productId"
        placeholder="商品ID"
        clearable
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.productNumber"
        placeholder="商品编码"
        clearable
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.productName"
        placeholder="商品名称"
        clearable
        class="filter-item"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-button type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      <el-button
        v-permission="['wms:medicine:update']"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >添加商品</el-button>
    </div>
    <el-table :key="tableKey" :data="list" fit
      highlight-current-row
      :row-style="{ height: '42px' }"
        :header-row-style="{ height: '42px' }"
        :header-cell-style="{
          background: '#F8F9FB'
      }"
    >
      <el-table-column label="商品ID" prop="productId" align="center" />
      <el-table-column label="商品编码" prop="productNumber" align="center" />
      <el-table-column label="商品名称" prop="productName" align="center" />
      <el-table-column label="安全分类" prop="nmpaType" align="center">
        <template slot-scope="{row}">
          <span>{{ row.nmpaType === 1 ? '处方药' : row.nmpaType === 2 ? 'OTC' : '其它' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="商品状态" prop="status" align="center">
        <template slot-scope="{row}">
          <el-tag v-if="row.status === 0" type="danger" size="medium">下架</el-tag>
          <el-tag v-else-if="row.remainQuantity === 0" type="warning" size="medium">无货</el-tag>
          <el-tag v-else size="medium">有货</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="添加时间" prop="createdAt" align="center" />
      <el-table-column label="操作" fixed="right" align="center">
        <template slot-scope="{row}">
          <el-button
            v-permission="['wms:section:save']"
            type="primary"
            @click="editProduct(row)"
          >编辑</el-button>
          <el-button
            v-permission="['wms:section:save']"
            type="danger"
            @click="deleteGoods(row.productId)"
          >移除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog width="500px" :title="createInfo.id?'编辑商品':'添加商品'" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :model="createInfo" :rules="rules">
        <el-form-item label="商品名称" :label-width="formLabelWidth" prop="productId">
          <el-select
            v-model="searchTxt"
            filterable
            remote
            reserve-keyword
            placeholder="商品名称"
            :remote-method="remoteMethod"
            :loading="loading"
            clearable
            value-key="productId"
            class="inputW"
            @change="changeSelect"
            @clear="clearSelect"
          >
            <el-option v-for="item in goodOptions" :key="item.productId" :label="item.productName" :value="item">
              <div>
                <span>{{ item.productName }}</span>
                <span>库存：{{ item.remainQuantity }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品编码" :label-width="formLabelWidth">
          <el-input
            v-model="createInfo.productNumber"
            :disabled="true"
            class="brandinput"
            placeholder=""
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="saveGood">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import api_product from '@/api/product/product'
export default {
  name: 'Purchase',
  filters: {},
  components: {
  },
  data() {
    return {
      tableKey: 0,
      baseoptions: null,
      list: null,
      total: 0,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        productId: '',
        productName: '',
        productNumber: ''
      },
      dialogFormVisible: false,
      formLabelWidth: '25%',
      createInfo: {
        productId: '',
        sort: ''
      },
      rules: {
        productId: [{ required: true, message: '请选择商品', trigger: ['blur', 'change'] }]
      },
      status: '',
      productId: '',
      loading: false,
      searchTxt: '',
      goodOptions: []
    }
  },
  created() {
    this.getList()
  },
  activated() {
    this.getList()
  },
  methods: {
    saveGood() {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          const resultArr = [
            this.createInfo.productId
          ]
          api_product.editPrescription(
            resultArr
          ).then(rs => {
            this.$message.success('操作成功！')
            this.dialogFormVisible = false
            this.getList()
          })
        }
      })
    },
    handleCreate() {
      this.searchTxt = ''
      this.createInfo.id = ''
      this.createInfo.productNumber = ''

      this.createInfo.productId = ''
      this.createInfo.productName = ''
      this.createInfo.productNumber = ''
      this.dialogFormVisible = true
    },
    editProduct(row) {
      this.searchTxt = row.productName
      // 编辑的时候
      this.createInfo.id = row.id
      this.createInfo.productId = row.productId
      this.createInfo.sort = row.sort

      this.createInfo.productName = row.productName
      this.createInfo.productNumber = row.productNumber
      this.dialogFormVisible = true
    },
    changeSelect(val) {
      console.log(val, 'changeSelect')
      this.createInfo.productId = val.productId
      this.createInfo.productName = val.productNumber
      this.createInfo.productNumber = val.productNumber
    },
    clearSelect() {
      this.createInfo.productId = ''
      this.createInfo.productName = ''
      this.createInfo.productNumber = ''
    },
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true
        api_product.getProduct({
          keyword: query,
          pageNo: 1,
          pageSize: 1000
        }).then(res => {
          console.log(res, '商品')
          this.goodOptions = res.list || []
          this.loading = false
        })
      } else {
        this.goodOptions = []
      }
    },
    deleteGoods(id) {
      this.$confirm('是否确认删除该商品？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api_product.delPrescription({ productId: id }).then(() => {
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.getList()
        })
      })
    },
    // 获取数据
    getList() {
      api_product.getPrescriptionList(this.listQuery).then(rs => {
        console.log(rs, '开方')
        this.total = rs.totalCount
        this.list = rs.list
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.productId = ''
      this.listQuery.productNumber = ''
      this.listQuery.productName = ''
      this.handleFilter()
    },
    nextCreate(dataForm) {
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          this.dialogFormVisible = false
          console.log(this.createInfo, 232)
          this.$refs['drugForm'].handleCreate(this.createInfo)
        }
      })
    },
    editSku(productId) {
      this.productId = productId
      this.$router.push({
        path: './sku/' + productId
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        // this.createInfo = {}
        this.$refs['dataForm'].clearValidate()
      })
    }
  }
}
</script>
<style scoped>
.mallgroup {
  padding: 20px;
}
.mallgroup .toptips i{
  color: #1890ff;
  font-size: 16px;
  margin-right: 6px;
}
.mallgroup .toptips {
  width: 100%;
  padding: 10px 10px;
  background: #EDF6FF;
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}
.mallgroup .add-tips{
  margin-left: 20px;
  font-size: 14px;
  color: #666;
}
/*.el-form-item .el-input__inner{width:auto;}*/
.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #1890ff;
  border-color: #1890ff;
}
.brandinput {
  width: 193px;
}
.top {
  display: inline-block;
  margin-left: 10px;
}
.tag{
  width: 80px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #fff;
  background: #1890ff;
  margin-left: 30px;
  border-radius: 4px;
}
.no{
  background: #ff4949;
}
.have{
  background: orange;
}
</style>
