<template>
  <div v-loading="loading" class="mall-edit-warp">
    <div class="detail-nav-top" :class="{ wide: !hideSidebar }">
      <div class="operate">
        <div class="title">专题管理</div>
        <div class="btn">
          <el-button @click="goBack">返 回</el-button>
          <!-- <el-button v-permission="['wms:section:save']" type="primary" @click="handleSave">保 存</el-button> -->
        </div>
      </div>
      <div class="toptips">
        <i class="el-icon-warning-outline"></i>
        <span
          >完善主副标题且至少添加3个商品，即可在患者端首页展示专题营销模块，最多可添加9个专题推荐商品。<span class="example"></span
        ></span>
      </div>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="基本信息" name="first">
        <el-card class="card-warp" style="margin: 10px 20px;">
          <div class="card-title">基本信息</div>
          <el-form ref="formRef" :model="basicForm" :rules="rules" label-width="80px">
            <el-row type="flex">
              <el-col :span="24">
                <el-form-item label="专题配图">
                  <uploadImage
                    :value="basicForm.banner"
                    action="/storage"
                    :limit="1"
                    :fileSize="2048"
                    list-type="picture-card"
                    :show-file-list="false"
                    accept="image/png, image/gif, image/jpeg, image/jpg"
                    @success="onSuccess"
                    @remove="onRemove"
                  >
                  </uploadImage>
                  <span style="font-size: 12px;color: #606266">建议尺寸：678px * 180px</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="专题商品" name="second">
        <el-card class="card-warp" style="margin: 10px 20px; margin-bottom: 20px;">
          <div class="card-title">专题商品</div>
          <div class="filter-container">
            <el-button
              size="medium"
              v-permission="['wms:section:save']"
              type="primary"
              icon="el-icon-plus"
              :disabled="list && list[0] && list.length === 9"
              @click="handleCreate"
              >添加商品({{ list && list[0] ? list.length : 0 }}/9)</el-button
            >
            <span class="add-tips">
              只可添加已上架且有货的商品
            </span>
          </div>
          <pure-table
            :column="goodsCols"
            :data="list"
            :page.sync="listQuery.pageNo"
            :limit.sync="listQuery.pageSize"
            @sort-change="onSortChange"
            @row-click="onEditDrug"
          />
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <el-dialog width="500px" :title="createInfo.id ? '编辑专题商品' : '添加专题商品'" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :model="createInfo" :rules="rulesGood">
        <el-form-item class="nr-input" label="商品名称" :label-width="formLabelWidth" prop="productId">
          <el-select
            v-model="searchTxt"
            filterable
            remote
            reserve-keyword
            placeholder="商品名称"
            :remote-method="remoteMethod"
            :loading="searchLoading"
            clearable
            value-key="productId"
            class="inputW"
            @change="changeSelect"
            @clear="clearSelect"
          >
            <el-option v-for="item in goodOptions" :key="item.productId" :label="item.productName" :value="item">
              <div>
                <span>{{ item.productName }}</span>
                <span>库存：{{ item.remainQuantity }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品编码" :label-width="formLabelWidth" class="nr-input">
          <el-input v-model="createInfo.productNumber" :disabled="true" class="brandinput" />
        </el-form-item>
        <el-form-item label="排序号" :label-width="formLabelWidth" prop="sort">
          <el-input-number v-model="createInfo.sort" :min="-999" :max="999" style="width: 150px;" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="saveGood">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import to from 'await-to-js'
import PureTable from '@/components/PureTable'
import uploadImage from '@/components/uploadImage'
import { getSpecialDetail, saveSpecial, delProduct, getProduct, editSpecialProduct } from '@/api/product/product'
export default {
  name: 'mallgroupEdit',
  components: { PureTable, uploadImage },
  data() {
    return {
      loading: false,
      searchLoading: false,
      basicForm: {
        banner: [] // 配图
      },
      rules: {
        sort: [{ required: true, message: '请输入排序号', trigger: 'blur' }],
        mainTitle: [{ required: true, message: '请输入主标题', trigger: 'blur' }],
        subTitle: [{ required: true, message: '请输入副标题', trigger: 'blur' }]
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      goodsCols: [
        {
          prop: 'productId',
          label: '商品ID',
          width: '100px'
        },
        {
          prop: 'productNumber',
          label: '商品编码',
          minWidth: '150px'
        },
        {
          prop: 'productName',
          label: '商品名称',
          minWidth: '180px',
          showOverflowTooltip: true
        },
        {
          prop: 'typeDescribe',
          label: '商品状态',
          minWidth: '150px',
          render: (h, { row }) => {
            return (
              <el-tag type={row.status === 0 ? 'danger' : row.remainQuantity === 0 ? 'info' : ''} size="medium">
                {row.status === 0 ? '下架' : row.remainQuantity === 0 ? '无货' : '有货'}
              </el-tag>
            )
          }
        },
        {
          prop: 'sort',
          label: '排序',
          width: '100px'
        },
        {
          prop: 'createdAt',
          label: '添加时间',
          width: '200px'
        },
        {
          label: '操作',
          width: '200px',
          render: (h, { row }) => {
            // 提取按钮权限
            const hasPermission = this.$store.getters.permissions.includes('wms:section:save')
            if (!hasPermission) {
              return null
            }

            return (
              <span>
                <el-button type="primary" onClick={() => this.editProduct(row)}>
                  编辑
                </el-button>
                <el-button type="danger" onClick={() => this.deleteGoods(row)}>
                  删除
                </el-button>
              </span>
            )
          }
        }
      ],
      list: [],
      specialId: '',
      dialogFormVisible: false,
      formLabelWidth: '25%',
      createInfo: {
        productId: '',
        sort: ''
      },
      rulesGood: {
        productId: [{ required: true, message: '请选择商品', trigger: ['blur', 'change'] }],
        sort: [{ required: true, message: '请输入排序号', trigger: ['blur', 'change'] }]
      },
      searchTxt: '',
      goodOptions: [],
      activeName: 'first'
    }
  },
  computed: {
    hideSidebar() {
      return this.$store.state.app.sidebar.opened
    }
  },
  created() {
    this.specialId = this.$route.query.id
    this.basicForm.sort = this.$route.query.sort ? this.$route.query.sort : ''
    if (this.specialId) this.getDetail()
  },
  mounted() {},
  methods: {
    // 获取数据
    async getDetail() {
      this.loading = true
      const [err, res] = await to(getSpecialDetail(this.specialId))
      if (res) {
        this.basicForm.banner = res.banner ? [{ url: res.banner }] : []
        this.list = res.products
        this.createInfo.sort = res.nextSort
        setTimeout(() => {
          this.loading = false
        }, 500)
      }
    },
    editProduct(row) {
      this.searchTxt = row.productName
      // 编辑的时候
      this.createInfo.id = row.id
      this.createInfo.productId = row.productId
      this.createInfo.sort = row.sort

      this.createInfo.productName = row.productName
      this.createInfo.productNumber = row.productNumber
      this.dialogFormVisible = true
    },
    onSortChange() {},
    onEditDrug() {},
    // 图片上传成功回调
    async onSuccess(data) {
      this.basicForm.banner.push({
        url: data.response.data,
        uid: data.uid
      })
      const params = {
        type: 1,
        id: this.specialId,
        banner: this.basicForm.banner.map((item) => item.url).join()
      }
      const [err, res] = await to(saveSpecial(params))
      if (err) {
        this.$message({
          type: 'error',
          message: '上传失败'
        })
      }
    },
    // 图片删除回调
    async onRemove(data) {
      const [err, res] = await to(saveSpecial({ type: 1, id: this.specialId, banner: '' }))
      if (!err) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        this.basicForm.banner = []
      }
    },
    // 保存专题
    handleSave() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          this.basicForm.banner = this.bannerUploadList && this.bannerUploadList.length > 0 ? this.bannerUploadList[0].url : ''
          const params = {
            type: 1,
            ...this.basicForm
          }
          const [err, res] = await to(saveSpecial(params))
          if (!err) {
            this.$message({
              type: 'success',
              message: '保存成功'
            })
          }
        } else {
          return false
        }
      })
    },
    handleCreate() {
      this.searchTxt = ''
      this.createInfo.id = ''
      this.createInfo.productNumber = ''
      this.createInfo.productId = ''
      this.createInfo.productName = ''
      this.createInfo.productNumber = ''
      this.dialogFormVisible = true
    },
    saveGood() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const resultArr = [
            {
              id: this.createInfo.id || '',
              productId: this.createInfo.productId,
              sort: this.createInfo.sort
            }
          ]
          editSpecialProduct(resultArr, this.specialId).then((rs) => {
            this.$message.success('操作成功！')
            this.dialogFormVisible = false
            this.getDetail()
          })
        }
      })
    },
    deleteGoods(row) {
      this.$confirm('是否确认删除该专题商品？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delProduct({ id: row.id }).then(() => {
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.getDetail()
        })
      })
    },
    changeSelect(val) {
      this.createInfo.productId = val.productId
      this.createInfo.productName = val.productNumber
      this.createInfo.productNumber = val.productNumber
    },
    clearSelect() {
      this.createInfo.productId = ''
      this.createInfo.productName = ''
      this.createInfo.productNumber = ''
    },
    remoteMethod(query) {
      if (query !== '') {
        this.searchLoading = true
        getProduct({
          keyword: query,
          pageNo: 1,
          pageSize: 1000
        }).then((res) => {
          this.goodOptions = res.list || []
          this.searchLoading = false
        })
      } else {
        this.goodOptions = []
      }
    },
    goBack() {
      this.$router.push({ path: './mallgroup' })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 355px;
}

::v-deep .nr-input .el-input__inner {
  height: 36px;
  line-height: 36px;
  width: 300px;
}

::v-deep .el-form-item--mini .el-form-item__label {
  line-height: 36px;
}

::v-deep .el-tabs__nav {
  left: 20px;
}

.mall-edit-warp {
  position: relative;
  width: 100%;
  padding-top: 130px;
  .card-title {
    margin-bottom: 20px;
  }
  .add-tips {
    margin-left: 20px;
    font-size: 14px;
    color: #666;
  }
}
</style>
