<template>
  <div v-loading="loading" class="mall-edit-warp">
    <div class="detail-nav-top" :class="{ wide: !hideSidebar }">
      <div class="operate" style="margin-bottom: 0;">
        <div class="title">专区管理</div>
        <div class="btn">
          <el-button @click="goBack">返 回</el-button>
          <!-- <el-button v-permission="['wms:section:save']" type="primary" @click="handleSave">保 存</el-button> -->
        </div>
      </div>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="专区简介" name="first">
        <el-card class="card-warp" style="margin: 10px 20px;">
          <div class="card-title">专区简介</div>
          <el-form ref="formRef" :model="basicForm" :rules="rules" label-width="80px">
            <el-row type="flex">
              <el-col :span="12">
                <el-form-item label="专区配图">
                  <uploadImage
                    :value="basicForm.banner"
                    action="/storage"
                    :limit="1"
                    :fileSize="2048"
                    list-type="picture-card"
                    :show-file-list="false"
                    accept="image/png, image/gif, image/jpeg, image/jpg"
                    @success="onSuccess"
                    @remove="onRemove"
                  >
                  </uploadImage>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item v-if="basicForm.banner.length > 0" prop="title" label="标题" class="nr-input">
                  <el-input v-model="basicForm.title" placeholder="请输入标题"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row type="flex">
              <el-col :span="24">
                <el-form-item v-if="basicForm.banner.length > 0" label="内容" prop="content">
                  <wangEditor ref="editor" :text-value.sync="basicForm.content" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div style="text-align: right;margin: 10px 0;">
           <el-button v-permission="['wms:section:save']" type="primary" @click="handleSave">保 存</el-button>
          </div>
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="专区商品" name="second">
        <el-card class="card-warp" style="margin: 10px 20px; margin-bottom: 20px;">
          <div class="card-title">商品管理</div>
          <div class="filter-container">
            <el-input
              v-model="listQuery.productId"
              placeholder="商品ID"
              clearable
              class="filter-item"
              style="width: 200px;"
              @keyup.enter.native="handleFilter"
            />
            <el-input
              v-model="listQuery.productName"
              placeholder="商品名称"
              clearable
              class="filter-item"
              style="width: 200px;"
              @keyup.enter.native="handleFilter"
            />
            <el-button size="medium" style="margin-left: 10px;" type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
            <el-button size="medium" type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button size="medium" type="primary" icon="el-icon-plus" @click="handleCreate">添加商品</el-button>
          </div>
          <pure-table
            :column="goodsCols"
            :data="list"
            :total="total"
            :page.sync="listQuery.pageNo"
            pagination
            :limit.sync="listQuery.pageSize"
            @pagination="handleFilter"
          />
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <el-dialog width="500px" :title="createInfo.id ? '编辑专区商品' : '添加专区商品'" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :model="createInfo" :rules="rulesGood">
        <el-form-item class="nr-input" label="商品名称" :label-width="formLabelWidth" prop="productId">
          <el-select
            v-model="searchTxt"
            filterable
            remote
            reserve-keyword
            placeholder="商品名称"
            :remote-method="remoteMethod"
            :loading="searchLoading"
            clearable
            value-key="productId"
            class="inputW"
            @change="changeSelect"
            @clear="clearSelect"
          >
            <el-option v-for="item in goodOptions" :key="item.productId" :label="item.productName" :value="item">
              <div>
                <span>{{ item.productName }}</span>
                <span>库存：{{ item.remainQuantity }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品编码" :label-width="formLabelWidth" class="nr-input">
          <el-input v-model="createInfo.productNumber" :disabled="true" class="brandinput" />
        </el-form-item>
        <el-form-item label="排序号" :label-width="formLabelWidth" prop="sort">
          <el-input-number v-model="createInfo.sort" :min="-999" :max="999" style="width: 150px;" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="saveGood">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import to from 'await-to-js'
import _ from 'lodash'
import PureTable from '@/components/PureTable'
import uploadImage from '@/components/uploadImage'
import wangEditor from '@/components/wangEditor'
import {
  getSpecialDetail,
  saveSpecial,
  delProduct,
  editSpecialArticle,
  getProduct,
  editSpecialProduct,
  zoneDetail,
  getSpecialProductList
} from '@/api/product/product'
export default {
  name: 'ZoneEdit',
  components: { PureTable, uploadImage, wangEditor },
  data() {
    return {
      loading: false,
      activeName: 'first',
      searchLoading: false,
      basicForm: {
        title: '', // 标题
        content: '', // 内容
        banner: [] // 配图
      },
      rules: {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }]
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        productId: '',
        productName: ''
      },
      total: 0,
      oldData: null,
      isMute: false,
      timer: null, // 定时器
      isBrowserBack: false, // 是否点击的浏览器后退键
      isPreventBrowserBack: false,
      isAbortLeave: false,
      goodsCols: [
        {
          prop: 'productId',
          label: '商品ID',
          width: '100px'
        },
        {
          prop: 'productNumber',
          label: '商品编码',
          minWidth: '150px'
        },
        {
          prop: 'productName',
          label: '商品名称',
          minWidth: '180px',
          showOverflowTooltip: true
        },
        {
          prop: 'typeDescribe',
          label: '商品状态',
          minWidth: '150px',
          render: (h, { row }) => {
            return (
              <el-tag type={row.status === 0 ? 'danger' : row.remainQuantity === 0 ? 'info' : ''} size="medium">
                {row.status === 0 ? '下架' : row.remainQuantity === 0 ? '无货' : '有货'}
              </el-tag>
            )
          }
        },
        {
          prop: 'sort',
          label: '排序',
          width: '100px'
        },
        {
          prop: 'createdAt',
          label: '添加时间',
          width: '200px'
        },
        {
          label: '操作',
          width: '200px',
          render: (h, { row }) => {
            // 提取按钮权限
            const hasPermission = this.$store.getters.permissions.includes('wms:section:save')
            if (!hasPermission) {
              return null
            }

            return (
              <span>
                <el-button type="primary" onClick={() => this.editProduct(row)}>
                  编辑
                </el-button>
                <el-button type="danger" onClick={() => this.deleteGoods(row)}>
                  删除
                </el-button>
              </span>
            )
          }
        }
      ],
      list: [],
      specialId: '',
      dialogFormVisible: false,
      formLabelWidth: '25%',
      createInfo: {
        productId: '',
        sort: ''
      },
      rulesGood: {
        productId: [{ required: true, message: '请选择商品', trigger: ['blur', 'change'] }],
        sort: [{ required: true, message: '请输入排序号', trigger: ['blur', 'change'] }]
      },
      searchTxt: '',
      goodOptions: []
    }
  },
  computed: {
    hideSidebar() {
      return this.$store.state.app.sidebar.opened
    }
  },
  watch: {
    // 弹框监听，当弹框显示的时候，pushState添加一个历史，供返回键使用
    isBrowserBack: {
      handler(newVal, oldVal) {
        if (newVal === true) {
          window.history.pushState(null, null, document.URL)
        }
      },
      deep: true
    }
  },
  created() {
    this.specialId = this.$route.query.id
    if (this.specialId) {
      this.getDetail()
      this.handleFilter()
    }
  },
  mounted() {
    if (window.history && window.history.pushState) {
      // 向历史记录中插入了当前页
      history.pushState(null, null, document.URL)
      window.addEventListener('popstate', this.goBack, false)
    }
  },
  destroyed() {
    window.removeEventListener('popstate', this.goBack, false)
  },
  methods: {
    // 获取数据
    async getDetail() {
      this.loading = true
      const [err, res] = await to(zoneDetail(this.specialId))
      if (res) {
        this.basicForm.title = res.title || ''
        this.basicForm.content = res.content || ''
        this.basicForm.banner = res.banner ? [{ url: res.banner }] : []
        // 保存初始数据
        this.oldData = _.cloneDeep(this.basicForm)
        setTimeout(() => {
          this.loading = false
        }, 500)
      }
    },
    handleReset() {
      this.listQuery.productId = ''
      this.listQuery.productName = ''
      this.listQuery.pageNo = 1
      this.handleFilter()
    },
    async handleFilter() {
      const params = { ...this.listQuery }
      const [err, res] = await to(getSpecialProductList(params, this.specialId))
      if (err) {
        this.list = []
      } else {
        const { list, totalCount, nextSort } = res
        this.list = list
        this.total = totalCount
        this.createInfo.sort = nextSort
      }
    },
    editProduct(row) {
      this.searchTxt = row.productName
      // 编辑的时候
      this.createInfo.id = row.id
      this.createInfo.productId = row.productId
      this.createInfo.sort = row.sort

      this.createInfo.productName = row.productName
      this.createInfo.productNumber = row.productNumber
      this.dialogFormVisible = true
    },
    // 图片上传成功回调
    async onSuccess(data) {
      this.basicForm.banner.push({
        url: data.response.data
      })
      const params = {
        type: 2,
        id: this.specialId,
        banner: this.basicForm.banner.map((item) => item.url).join()
      }
      const [err, res] = await to(saveSpecial(params))
      if (err) {
        this.$message({
          type: 'error',
          message: '上传失败'
        })
      }
    },
    // 图片删除回调
    async onRemove(data) {
      const [err, res] = await to(saveSpecial({ type: 2, id: this.specialId, banner: '' }))
      if (!err) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        this.basicForm.banner = []
      }
    },
    // 保存专区
    handleSave() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          const params = {
            sectionId: this.specialId,
            title: this.basicForm.title,
            content: this.basicForm.content
          }
          const [err, res] = await to(editSpecialArticle(params))
          if (!err) {
            this.$message({
              type: 'success',
              message: '保存成功'
            })
          }
          this.getDetail()
        } else {
          return false
        }
      })
    },
    handleCreate() {
      this.searchTxt = ''
      this.createInfo.id = ''
      this.createInfo.productNumber = ''
      this.createInfo.productId = ''
      this.createInfo.productName = ''
      this.createInfo.productNumber = ''
      this.dialogFormVisible = true
    },
    saveGood() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          const resultArr = [
            {
              id: this.createInfo.id || '',
              productId: this.createInfo.productId,
              sort: this.createInfo.sort
            }
          ]
          editSpecialProduct(resultArr, this.specialId).then((rs) => {
            this.$message.success('操作成功！')
            this.dialogFormVisible = false
            this.handleFilter()
          })
        }
      })
    },
    deleteGoods(row) {
      this.$confirm('是否确认删除该专区商品？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delProduct({ id: row.id }).then(() => {
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.handleFilter()
        })
      })
    },
    changeSelect(val) {
      this.createInfo.productId = val.productId
      this.createInfo.productName = val.productNumber
      this.createInfo.productNumber = val.productNumber
    },
    clearSelect() {
      this.createInfo.productId = ''
      this.createInfo.productName = ''
      this.createInfo.productNumber = ''
    },
    remoteMethod(query) {
      if (query !== '') {
        this.searchLoading = true
        getProduct({
          keyword: query,
          pageNo: 1,
          pageSize: 1000
        }).then((res) => {
          this.goodOptions = res.list || []
          this.searchLoading = false
        })
      } else {
        this.goodOptions = []
      }
    },
    // 检查数据是否发生了变化而又还没保存
    checkHasChanged(type = 'leave') {
      if (type === 'leave') {
        if (this.isMute) {
          return
        }
        this.isMute = true
      }
      const obj = {
        title: this.basicForm.title || '',
        content: this.basicForm.content || '',
        banner: this.basicForm.banner.length > 0 ? [{ url: this.basicForm.banner[0].url }] : []
      }
      console.log('obj', obj)
      if (JSON.stringify(this.oldData) !== JSON.stringify(obj) && this.oldData !== null) {
        return true
      } else {
        return false
      }
    },
    goBack() {
      this.timer = setTimeout(async () => {
        this.isMute = false
      }, 1000)
      this.isBrowserBack = true
      if (this.checkHasChanged()) {
        this.$confirm('当前编辑的内容尚未保存，是否确认离开？', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消'
        })
          .then(() => {
            if (this.isAbortLeave) {
              window.history.go(-1)
            }
            this.$router.push({
              path: './zone'
            })
          })
          .catch(() => {
            this.isAbortLeave = true
            this.isBrowserBack = false
            this.isPreventBrowserBack = true
          })
      } else {
        this.$router.go(-1)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 355px;
}
::v-deep .nr-input .el-input__inner {
  height: 36px;
  line-height: 36px;
  width: 300px;
}

::v-deep .filter-item .el-input__inner {
  height: 36px;
  line-height: 36px;
}

::v-deep .el-form-item--mini .el-form-item__label {
  line-height: 36px;
}

::v-deep .el-tabs__nav {
  left: 20px;
}

::v-deep .el-form-item {
  margin-bottom: 20px;
}

.mall-edit-warp {
  position: relative;
  width: 100%;
  padding-top: 80px;
  .card-title {
    margin-bottom: 20px;
  }
  .add-tips {
    margin-left: 20px;
    font-size: 14px;
    color: #666;
  }
}
</style>
