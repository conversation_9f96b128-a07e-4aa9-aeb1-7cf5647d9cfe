<template>
  <div class="classify">
    <div class="filter-container">
      <el-button v-permission="['wms:category:save']" type="primary" icon="el-icon-plus" @click="handleCreate">新增分类</el-button>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      row-key="id"
      :tree-props="{ children: 'children' }"
      highlight-current-row
      :row-style="{ height: '42px' }"
      :header-row-style="{ height: '42px' }"
      :header-cell-style="{
        background: '#F8F9FB'
      }"
    >
      <el-table-column label="分类名称" prop="name" align="center" />
      <el-table-column label="分类图片" prop="icon" align="center" width="200px">
        <template slot-scope="{ row }">
          <el-image v-if="row.icon" :src="row.icon">
            <!-- <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div> -->
          </el-image>
          <!-- <img v-else src="@/assets/images/<EMAIL>" alt="" /> -->
        </template>
      </el-table-column>
      <el-table-column label="药品数量" prop="pnumber" align="center" width="180px" />
      <el-table-column label="排序" prop="sort" align="center" width="180px" />
      <el-table-column label="创建时间" prop="createdAt" align="center" />
      <el-table-column label="操作" fixed="right" align="center">
        <!-- v-permission="['wms:medicine:update']" -->
        <template slot-scope="{ row }">
          <el-button v-permission="['wms:category:save']" type="primary" @click="editProduct(row)">编辑</el-button>
          <el-button v-permission="['wms:category:save']" type="danger" @click="deleteRow(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageNo" :limit.sync="listQuery.pageSize" @pagination="getList" />

    <el-dialog width="550px" :title="createInfo.id ? '编辑展示分类' : '新增展示分类'" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :model="createInfo" :rules="rules">
        <el-form-item label="类型" :label-width="formLabelWidth">
          <el-radio-group v-model="type" prop="type" @input="radioChange">
            <el-radio :label="1">一级分类</el-radio>
            <el-radio :label="2">二级分类</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item class="nr-input" v-if="type === 2" label="上级分类" :label-width="formLabelWidth" prop="parentId">
          <!-- <el-cascader
            v-model="createInfo.parentId"
            :options="materialTypeData"
            :props="props"
            clearable
            :show-all-levels="false"
          /> -->
          <el-select v-model="createInfo.parentId" placeholder="请选择" @change="changeParent">
            <el-option v-for="item in materialTypeData" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分类名称" :label-width="formLabelWidth" prop="name" class="nr-input">
          <el-input v-model="createInfo.name" class="brandinput" placeholder="请输入分类名称" maxlength="6" />
        </el-form-item>
        <el-form-item label="备注" :label-width="formLabelWidth" prop="description">
          <el-input v-model="createInfo.description" type="textarea" :rows="3" class="brandinput" placeholder="请输入备注" maxlength="50" />
        </el-form-item>
        <el-form-item v-if="type === 1" label="分类图片" :label-width="formLabelWidth" prop="imgList">
          <uploadImage
            :value="createInfo.imgList"
            action="/storage"
            list-type="picture-card"
            :show-file-list="false"
            :limit="1"
            size-tips="图片尺寸92*92px；"
            accept="png、jpeg、jpg"
            @success="onSuccess"
            @remove="onRemove"
          >
          </uploadImage>
        </el-form-item>
        <el-form-item label="排序号" :label-width="formLabelWidth" prop="sort">
          <el-input-number size="medium" v-model="createInfo.sort" :min="-999" :max="999" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click="dialogFormVisible = false">取消</el-button>
        <el-button size="medium" type="primary" @click="saveClassify">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import api_product from '@/api/product/product'
import api_base from '@/api/product/base'
import uploadImage from '@/components/uploadImage'
export default {
  name: 'Productlist',
  filters: {},
  components: {
    uploadImage
  },
  data() {
    return {
      tableKey: 0,
      baseoptions: null,
      list: null,
      total: 0,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name',
        checkStrictly: true
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      dialogFormVisible: false,
      formLabelWidth: '22%',
      type: 1,
      createInfo: {
        name: '',
        description: '',
        imgList: [],
        icon: '',
        sort: '',
        parentId: ''
      },
      rules: {
        name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
        imgList: [{ required: true, message: '请上传分类图片', trigger: ['blur', 'change'] }],
        sort: [{ required: true, message: '请输入排序号', trigger: 'blur' }],
        parentId: [{ required: true, message: '请选择上级分类', trigger: 'change' }]
      },
      status: '',
      productId: '',
      nextSort: '',
      materialTypeData: []
    }
  },
  created() {
    // this.handleFilter()
    // this.getBaseData()
    this.getList()
    this.getCategoryList()
  },
  activated() {
    // this.getList()
    console.log('============activated=============')
  },
  methods: {
    // 删除分类
    deleteRow(id) {
      this.$confirm('是否确认删除该分类？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api_product.delCategory({ id: id }).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.handleFilter()
        })
      })
    },
    // 保存分类
    saveClassify() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          api_product.addOrEditCategory(this.createInfo).then((rs) => {
            this.$message.success('操作成功！')
            this.dialogFormVisible = false
            this.handleFilter()
          })
        }
      })
    },
    onSuccess(data) {
      this.createInfo.imgList = this.createInfo.imgList ? this.createInfo.imgList : []
      this.createInfo.imgList.push({
        url: data.response.data,
        uid: data.uid
      })
      this.createInfo.icon = data.response.data
    },
    onRemove(data) {
      this.createInfo.imgList = data
      this.createInfo.icon = ''
    },
    // 获取数据
    getList() {
      api_product.getCategoryList(this.listQuery).then((res) => {
        this.list = res.list
        this.total = res.totalCount
        this.nextSort = res.nextSort
        this.createInfo.sort = this.nextSort
      })
    },
    getBaseData() {
      api_base.pharmacology().then((response) => {
        this.baseoptions = response
      })
    },
    getCategoryList() {
      api_product.categorylist({ parentId: 0 }).then((response) => {
        this.materialTypeData = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.name = ''
      this.listQuery.type = ''
      this.listQuery.classificationId = ''
      this.listQuery.relationSku = ''
      this.listQuery.dataIntegrity = ''
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    },
    handleCreate() {
      this.createInfo.id = ''
      this.createInfo.name = ''
      this.createInfo.imgList = []
      this.createInfo.icon = ''
      this.createInfo.sort = this.nextSort
      this.createInfo.description = ''
      this.createInfo.parentId = ''
      this.type = 1

      this.dialogFormVisible = true
      this.resetTemp()
    },
    editProduct(row) {
      this.dialogFormVisible = true
      this.createInfo.name = row.name
      this.createInfo.sort = row.sort
      this.createInfo.imgList = row.icon
        ? [
            {
              url: row.icon
            }
          ]
        : []
      this.createInfo.icon = row.icon
      this.createInfo.id = row.id
      this.createInfo.description = row.description
      this.type = row.parentId ? 2 : 1
      this.createInfo.parentId = row.parentId
    },
    editSku(productId) {
      this.productId = productId
      this.$router.push({
        path: './sku/' + productId
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        // this.createInfo = {}
        this.$refs['dataForm'].clearValidate()
      })
    },
    changeParent(val) {
      api_product.getCategorySort({ parentId: val }).then((res) => {
        this.createInfo.sort = res
      })
    },
    radioChange(val) {
      if (val == 1) {
        this.createInfo.sort = this.nextSort
      } else {
        this.createInfo.sort = undefined
        this.createInfo.parentId = ''
      }
      this.resetTemp()
    }
  }
}
</script>
<style lang="scss" scoped>
.classify {
  padding: 20px;
}
.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #1890ff;
  border-color: #1890ff;
}
.brandinput {
  width: 300px;
}
.tips {
  display: inline-block;
  margin-left: 10px;
}
::v-deep .el-image img {
  width: 48px;
  height: 48px;
}
img {
  width: 48px;
}
::v-deep .nr-input .el-input__inner {
  height: 36px;
  line-height: 36px;
  width: 300px;
}
</style>
