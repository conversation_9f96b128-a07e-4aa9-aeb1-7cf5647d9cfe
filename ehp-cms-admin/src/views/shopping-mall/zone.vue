<template>
  <div class="page-warp">
    <el-button style="margin-bottom: 20px;" type="primary" icon="el-icon-plus" @click="handleCreate">新增专区</el-button>
    <pure-table
      v-loading="loading"
      pagination
      :column="specialCols"
      :data="specialList"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    >
    </pure-table>
    <!-- 新增/编辑专区 -->
    <el-dialog width="500px" :title="title" :visible.sync="dialogFormVisible" @close="closeDialog">
      <el-form ref="formRef" :model="specialForm" :rules="rules" label-width="100px">
        <el-row type="flex">
          <el-col :span="24">
            <el-form-item label="排序号">
              <el-input-number size="medium" v-model="specialForm.sort" :min="-999" :max="999"></el-input-number>
            </el-form-item>
            <el-form-item label="专区名称" prop="mainTitle" class="nr-input">
              <el-input v-model="specialForm.mainTitle" placeholder="专区名称不超过8个字" :maxlength="8"></el-input>
            </el-form-item>
            <el-form-item label="专区缩略图" prop="thumb">
              <el-upload
                :file-list="specialForm.thumb"
                :action="uploadPath"
                list-type="picture-card"
                :limit="1"
                accept="image/png, image/gif, image/jpeg, image/jpg"
                :on-change="handleFileChange"
                :on-success="handleSuccess"
                :on-exceed="handleExceed"
              >
                <i slot="default" class="el-icon-plus"></i>
                <div slot="file" slot-scope="{ file }">
                  <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                  <span class="el-upload-list__item-actions">
                    <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                      <i class="el-icon-zoom-in"></i>
                    </span>
                    <span class="el-upload-list__item-delete" @click="handleRemove(file)">
                      <i class="el-icon-delete"></i>
                    </span>
                  </span>
                </div>
              </el-upload>
              <div class="el-upload__tip">
                只能上传image/png, image/jpeg, image/jpg文件且小于300KB<br />
                建议尺寸：228 * 160px
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="saveSpecial">确定</el-button>
      </div>
    </el-dialog>

    <!-- 图片预览 -->
    <el-image-viewer v-if="imgViewerVisible" :on-close="closeImgViewer" :url-list="previewImgListTemp" />
  </div>
</template>

<script>
import to from 'await-to-js'
import PureTable from '@/components/PureTable'
import { getSpecialList, changeSpecial, delSpecial, saveSpecial } from '@/api/product/product'
export default {
  name: 'Zone',
  components: { PureTable, 'el-image-viewer': () => import('element-ui/packages/image/src/image-viewer') },
  data() {
    const validateImg = (rule, value, callback) => {
      if (this.specialForm.thumb.length === 0) {
        callback(new Error('请上传图片'))
      } else {
        callback()
      }
    }
    return {
      loading: false,
      specialList: [],
      specialCols: [
        {
          prop: 'id',
          label: 'ID',
          width: '100px'
        },
        {
          prop: 'mainTitle',
          label: '专区名称',
          minWidth: '120px',
          showOverflowTooltip: true
        },
        {
          label: '专区缩略图',
          width: '250px',
          render: (h, { row }) => {
            return h('el-image', {
              attrs: {
                src: row.thumb,
                previewSrcList: [row.thumb]
              },
              style: {
                width: '76px',
                height: '53px'
              }
            })
          }
        },
        {
          prop: 'productCount',
          label: '商品数量',
          minWidth: '120px'
        },
        {
          prop: 'sort',
          label: '排序',
          minWidth: '120px'
        },
        {
          prop: 'createdAt',
          label: '创建时间',
          minWidth: '120px'
        },
        {
          label: '操作',
          width: '250px',
          render: (h, { row }) => {
            // 提取按钮权限
            const hasPermission = this.$store.getters.permissions.includes('wms:section:save')
            if (!hasPermission) {
              return null
            }
            return (
              <span>
                <el-button type="text" onClick={() => this.onEdit(row)}>
                  编辑
                </el-button>
                <el-button type="text" onClick={() => this.onSpecialEdit(row)}>
                  专区管理
                </el-button>
                <el-button type="text" style={{ color: row.status ? 'red' : '' }} onClick={() => this.onToggle(row)}>
                  {row.status ? '停用' : '启用'}
                </el-button>
                <el-button type="text" style={{ color: 'red' }} onClick={() => this.onRemove(row.id)}>
                  删除
                </el-button>
              </span>
            )
          }
        }
      ],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'sort',
      },
      title: '新增专区',
      dialogFormVisible: false,
      specialForm: {
        sort: '', // 排序号
        mainTitle: '', // 专区名称
        thumb: [] // 专区缩略图
      },
      rules: {
        mainTitle: [{ required: true, message: '请输入主标题', trigger: 'blur' }],
        thumb: [{ required: true, validator: validateImg, trigger: 'change' }]
      },
      uploadPath: process.env.VUE_APP_BASE_API + '/storage',
      imgViewerVisible: false,
      previewImgListTemp: []
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      this.loading = true
      const data = {
        type: 2,
        ...this.listQuery
      }
      const [err, res] = await to(getSpecialList(data))
      if (err) {
        this.specialList = []
        this.loading = false
      } else {
        const { totalCount, list } = res
        this.total = totalCount
        this.specialList = list || []
        this.loading = false
      }
    },
    getMaxValue(arr, key) {
      return arr.reduce((max, item) => Math.max(max, item[key]), -1)
    },
    // 新增标题
    handleCreate() {
      this.specialForm = { sort: '', mainTitle: '', thumb: [] }
      const maxValue = this.getMaxValue(this.specialList, 'sort')
      this.specialForm.sort = maxValue + 1
      this.dialogFormVisible = true
    },
    // 编辑
    onEdit(row) {
      this.specialForm.id = row.id
      this.specialForm.sort = row.sort
      this.specialForm.mainTitle = row.mainTitle
      this.specialForm.thumb = row.thumb ? [{ url: row.thumb }] : []
      this.title = '编辑专区'
      this.dialogFormVisible = true
    },
    // 专区管理
    onSpecialEdit(row) {
      this.$router.push({ path: './zone-edit', query: { id: row.id } })
    },
    // 启用停用
    onToggle(row) {
      const msg = row.status ? '停用' : '启用'
      this.$confirm(`是否确认${msg}该专区商品？`, '提示', { type: 'warning' })
        .then(() => {
          const data = {
            sectionId: row.id,
            status: row.status ? 0 : 1
          }
          changeSpecial(data).then((res) => {
            this.getList()
            this.$message.success(`${msg}成功`)
          })
        })
        .catch(() => {
          return false
        })
    },
    // 删除
    onRemove(id) {
      this.$confirm('是否确认删除该专区？', '提示', { type: 'warning' })
        .then(() => {
          delSpecial(id).then((res) => {
            this.getList()
            this.$message.success('删除成功')
          })
        })
        .catch(() => {
          return false
        })
    },
    saveSpecial() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          const thumb = this.specialForm.thumb.map((item) => item.url).join()
          const params = {
            ...this.specialForm,
            type: 2,
            thumb
          }
          const [err, res] = await to(saveSpecial(params))
          if (!err) {
            this.dialogFormVisible = false
            this.$message({
              type: 'success',
              message: '保存成功'
            })
            this.getList()
          }
        } else {
          return false
        }
      })
    },
    closeDialog() {
      this.specialForm.thumb = []
    },
    closeImgViewer() {
      this.previewImgListTemp = []
      this.imgViewerVisible = false
      const m = (e) => {
        e.preventDefault()
      }
      document.body.style.overflow = 'auto'
      document.removeEventListener('touchmove', m, true)
    },
    handlePictureCardPreview(file) {
      this.previewImgListTemp = [file.url]
      this.imgViewerVisible = true
    },
    // 删除图片时触发
    handleRemove(file) {
      this.specialForm.thumb = []
      this.$forceUpdate()
    },
    handleExceed() {
      this.$message.error('最多只能上传1张图片')
    },
    handleFileChange(file, fileList) {
      if (fileList.length > 0) {
        this.$refs.formRef.clearValidate('thumb')
      }
    },
    handleSuccess(file, fileList) {
      if (file.data) {
        this.specialForm.thumb.push({
          url: fileList.response.data
        })
      } else {
        this.$message.error(file.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-image-viewer__wrapper {
  z-index: 2088 !important;
}
::v-deep .nr-input .el-input__inner {
  height: 36px;
  line-height: 36px;
  width: 300px;
}
::v-deep .el-upload-list__item.is-ready,
::v-deep .el-upload-list__item.is-uploading {
  display: none !important;
}
.page-warp {
  padding: 20px;
}
</style>
