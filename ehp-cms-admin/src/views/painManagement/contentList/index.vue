<template>
  <div class="app-container">
    <!-- 搜索条件 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.title"
        placeholder="标题"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        v-permission="['pain:content:create']"
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >
        添加
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        @click="resetSearch"
      >
        重置
      </el-button>
    </div>

    <!-- 表格 -->
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;"
      :row-style="{ height: '42px' }"
      :header-row-style="{ height: '42px' }"
      :header-cell-style="{
        background: '#F8F9FB'
      }"
    >
      <el-table-column label="ID" prop="id" align="center" width="80">
        <template slot-scope="{row}">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标题" min-width="150px">
        <template slot-scope="{row}">
          <span class="link-type" @click="handleUpdate(row)">{{ row.title }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分类" width="120px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.categoryName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="内容" min-width="200px">
        <template slot-scope="{row}">
          <span>{{ getContentPreview(row.content) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="服务包" width="150px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.servicePackageName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" width="150px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.createdAt | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" width="110px" align="center">
        <template slot-scope="{row}">
          <span>{{ row.createdBy || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="230" class-name="small-padding fixed-width">
        <template slot-scope="{row}">
          <el-button
            v-permission="['pain:content:update']"
            type="primary"
            size="mini"
            @click="handleUpdate(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="row.status != 'deleted'"
            v-permission="['pain:content:update']"
            :type="row.status === 'published' ? 'danger' : 'success'"
            size="mini"
            @click="handleModifyStatus(row)"
          >
            {{ row.status === 'published' ? '禁用' : '启用' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="80%">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
        style="width: 100%;"
      >
        <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane label="内容" name="content">
            <el-form-item label="标题" prop="title">
              <el-input v-model="temp.title" placeholder="请输入标题" />
            </el-form-item>
            <el-form-item label="分类" prop="categoryId">
              <el-select v-model="temp.categoryId" placeholder="请选择分类" style="width: 100%">
                <el-option
                  v-for="item in categoryOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="缩略图" prop="image">
              <upload-image
                :value="temp.imageList"
                action="/storage"
                list-type="picture-card"
                :limit="1"
                :show-file-list="false"
                accept="image/jpeg, image/jpg, image/png"
                @success="onImageSuccess"
                @remove="onImageRemove"
              >
                <i v-if="!temp.imageList.length" class="el-icon-plus"></i>
                <img v-else :src="temp.imageList[0].url" class="preview-image" alt="预览图">
              </upload-image>
              <div class="image-tips">
                <i class="el-icon-info"></i>
                支持JPG、PNG格式的图片
              </div>
            </el-form-item>
            <el-form-item label="内容" prop="content">
              <wang-editor ref="editor" v-model="temp.content" :height="400" />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="服务包" name="servicePackage">
            <el-form-item label="关联服务包">
              <el-select v-model="temp.servicePackageId" placeholder="请选择服务包" style="width: 100%">
                <el-option label="无" :value="null" />
                <el-option
                  v-for="item in servicePackageOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData():updateData()">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getList, createPainContent, updatePainContent } from '@/api/painManagement/content'
import { getAllCategories } from '@/api/painManagement/category'
// import { getList as getServicePackageList } from '@/api/servicePackage/index'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import UploadImage from '@/components/uploadImage'
import WangEditor from '@/components/wangEditor/index-v5'

export default {
  name: 'PainContentList',
  components: { Pagination, UploadImage, WangEditor },
  directives: { waves },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        title: undefined
      },
      categoryOptions: [],
      servicePackageOptions: [],
      temp: {
        id: undefined,
        title: '',
        categoryId: undefined,
        content: '',
        imageList: [],
        servicePackageId: undefined
      },
      dialogFormVisible: false,
      dialogStatus: '',
      activeTab: 'content',
      textMap: {
        update: '编辑',
        create: '新增'
      },
      rules: {
        title: [{ required: true, message: '标题是必填项', trigger: 'blur' }],
        categoryId: [{ required: true, message: '请选择分类', trigger: 'change' }],
        content: [{ required: true, message: '内容是必填项', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
    this.getCategoryOptions()
    this.getServicePackageOptions()
  },
  methods: {
    getList() {
      this.listLoading = true
      getList(this.listQuery).then(response => {
        this.list = response.data.items
        this.total = response.data.total
        setTimeout(() => {
          this.listLoading = false
        }, 1.5 * 1000)
      })
    },
    getCategoryOptions() {
      getAllCategories().then(response => {
        this.categoryOptions = response.data || []
      })
    },
    getServicePackageOptions() {
      // TODO: 服务包接口暂时使用模拟数据
      // getServicePackageList({ page: 1, limit: 1000 }).then(response => {
      //   this.servicePackageOptions = response.data.items || []
      // })

      // 模拟数据
      this.servicePackageOptions = [
        { id: 1, name: '春季专属服务包' },
        { id: 2, name: '夏季健康套餐' },
        { id: 3, name: '秋季养生包' }
      ]
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetSearch() {
      this.listQuery = {
        page: 1,
        limit: 20,
        title: undefined
      }
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        title: '',
        categoryId: undefined,
        content: '',
        imageList: [],
        servicePackageId: undefined
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.activeTab = 'content'
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.image = tempData.imageList.length > 0 ? tempData.imageList[0].url : ''
          delete tempData.imageList

          createPainContent(tempData).then(() => {
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)
      this.temp.imageList = row.image ? [{ url: row.image }] : []
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.activeTab = 'content'
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp)
          tempData.image = tempData.imageList.length > 0 ? tempData.imageList[0].url : ''
          delete tempData.imageList

          updatePainContent(tempData).then(() => {
            const index = this.list.findIndex(v => v.id === this.temp.id)
            this.list.splice(index, 1, tempData)
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 2000
            })
            this.getList()
          })
        }
      })
    },
    handleModifyStatus(row) {
      const status = row.status === 'published' ? 'draft' : 'published'
      const statusText = status === 'published' ? '启用' : '禁用'

      this.$confirm(`确定要${statusText}该内容吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updatePainContent({ id: row.id, status }).then(() => {
          row.status = status
          this.$message.success(`${statusText}成功`)
        })
      })
    },
    getContentPreview(content) {
      if (!content) return '-'
      const text = content.replace(/<[^>]*>/g, '')
      return text.length > 50 ? text.substring(0, 50) + '...' : text
    },
    onImageSuccess(data) {
      this.temp.imageList = [{
        url: data.response.data,
        uid: data.uid
      }]
    },
    onImageRemove() {
      this.temp.imageList = []
    }
  }
}
</script>

<style scoped>
.link-type {
  color: #337ab7;
  cursor: pointer;
}

.link-type:hover {
  color: #23527c;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-tips {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
}
</style>
