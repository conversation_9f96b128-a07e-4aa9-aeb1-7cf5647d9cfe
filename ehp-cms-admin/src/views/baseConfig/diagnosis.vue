<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button v-waves type="primary" icon="el-icon-plus" @click="addDiagnosis">添加</el-button>
    </div>

    <el-table
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;"
      :row-style="{ height: '42px' }"
      :header-row-style="{ height: '42px' }"
      :header-cell-style="{
        background: '#F8F9FB'
      }"
    >
      <el-table-column label="ID" prop="id" align="center" width="80" />
      <el-table-column label="诊断内容" prop="name" align="center" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.name || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="对应科室" prop="departments" align="center">
        <template slot-scope="scope">
          <span>{{ formatDepartments(scope.row.departments) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createdAt" align="center" width="180" />
      <el-table-column label="操作" width="300" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.available"
            size="mini"
            type="danger"
            @click="changeStatus(scope.row.id, 0)"
          >停用</el-button>
          <el-button
            v-else
            type="primary"
            size="mini"
            @click="changeStatus(scope.row.id, 1)"
          >启用</el-button>
          <el-button type="primary" size="mini" @click="editDiagnosis(scope.row)">编辑</el-button>
          <el-button type="primary" size="mini" @click="viewDiagnosis(scope.row)">查看</el-button>
          <el-button type="danger" size="mini" @click="delDiagnosis(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="fetchList"
    />

    <!-- 添加/编辑诊断 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogTableVisible" width="40%" @close="handleCancel">
      <el-form ref="dataForm" :model="diagnosisForm" :rules="rules" label-position="right" label-width="100px">
        <el-form-item label="诊断内容" prop="name">
          <el-input v-model.trim="diagnosisForm.name" :maxlength="15" :disabled="viewMode" />
        </el-form-item>
        <el-form-item label="对应科室" prop="departmentIds">
          <el-select
            v-model="diagnosisForm.departmentIds"
            multiple
            filterable
            placeholder="请选择科室"
            style="width: 100%"
            :disabled="viewMode"
          >
            <el-option
              v-for="item in departmentOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="!viewMode" class="btn-group">
          <el-button @click="handleCancel">取 消</el-button>
          <el-button type="primary" @click="submitForm('dataForm')">保 存</el-button>
        </el-form-item>
        <el-form-item v-else>
          <el-button type="primary" @click="handleCancel">关 闭</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getList, editDiagnosis, changeDiagnosisStatus, getDepartmentList, deleteDiagnosis } from '@/api/baseConfig/diagnosis'
import waves from '@/directive/waves'

export default {
  name: 'Diagnosis',
  directives: { waves },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      dialogTitle: '添加诊断',
      dialogTableVisible: false,
      viewMode: false,
      diagnosisForm: {
        name: '',
        departmentIds: []
      },
      departmentOptions: [],
      rules: {
        name: [{ required: true, message: '请输入诊断内容', trigger: 'blur' }],
        departmentIds: [{ required: true, message: '请选择对应科室', trigger: 'change', type: 'array' }]
      }
    }
  },
  created() {
    this.fetchList()
    this.fetchDepartments()
  },
  methods: {
    // 获取列表数据
    async fetchList() {
      try {
        const response = await getList(this.listQuery)
        this.list = response.list || []
        this.total = response.totalCount || 0
      } catch (error) {
        console.error('获取诊断列表失败', error)
      }
    },
    // 获取科室列表
    async fetchDepartments() {
      try {
        const response = await getDepartmentList()
        // 处理树形结构数据，扁平化为选项列表
        this.departmentOptions = this.flattenDepartmentTree(response.data || response.list || response || [])
      } catch (error) {
        console.error('获取科室列表失败', error)
      }
    },
    // 扁平化科室树形数据
    flattenDepartmentTree(tree) {
      const result = []
      const flatten = (nodes) => {
        if (!nodes || !Array.isArray(nodes)) return
        nodes.forEach(node => {
          if (node.id && node.name) {
            result.push({
              id: node.id,
              name: node.name
            })
          }
          if (node.children && Array.isArray(node.children)) {
            flatten(node.children)
          }
        })
      }
      flatten(tree)
      return result
    },
    // 格式化科室显示
    formatDepartments(departments) {
      if (!departments || !departments.length) return '-'
      return departments.map(dept => dept.name).join('、')
    },
    // 搜索
    handleFilter() {
      this.listQuery.pageNo = 1
      this.fetchList()
    },
    // 重置
    handleReset() {
      this.listQuery = {
        name: '',
        pageNo: 1,
        pageSize: 10
      }
      this.fetchList()
    },
    // 添加诊断
    addDiagnosis() {
      this.dialogTitle = '添加诊断'
      this.dialogTableVisible = true
      this.viewMode = false
      this.resetForm()
    },
    // 编辑诊断
    editDiagnosis(row) {
      this.dialogTitle = '编辑诊断'
      this.dialogTableVisible = true
      this.viewMode = false
      this.resetForm()

      this.diagnosisForm = {
        id: row.id,
        name: row.name,
        departmentIds: this.parseDepartmentIds(row)
      }
    },
    // 查看诊断
    viewDiagnosis(row) {
      this.dialogTitle = '查看诊断'
      this.dialogTableVisible = true
      this.viewMode = true
      this.resetForm()

      this.diagnosisForm = {
        id: row.id,
        name: row.name,
        departmentIds: this.parseDepartmentIds(row)
      }
    },
    // 解析科室ID数据，兼容数组对象和逗号分隔字符串两种格式
    parseDepartmentIds(row) {
      // 如果有departments数组对象，提取id
      if (row.departments && Array.isArray(row.departments)) {
        return row.departments.map(dept => dept.id)
      }
      // 如果有departmentIds字符串，分割为数组
      if (row.departmentIds && typeof row.departmentIds === 'string') {
        return row.departmentIds.split(',').filter(id => id.trim())
      }
      // 如果departmentIds已经是数组，直接返回
      if (row.departmentIds && Array.isArray(row.departmentIds)) {
        return row.departmentIds
      }
      return []
    },
    // 删除诊断
    async delDiagnosis(id) {
      try {
        await deleteDiagnosis(id)
        this.$message.success('删除成功')
        this.fetchList()
      } catch (error) {
        console.error('删除诊断失败', error)
      }
    },
    // 重置表单
    resetForm() {
      this.diagnosisForm = {
        name: '',
        departmentIds: []
      }
      // 如果表单已经被渲染，则重置校验结果
      if (this.$refs.dataForm) {
        this.$refs.dataForm.resetFields()
      }
    },
    // 取消/关闭
    handleCancel() {
      this.dialogTableVisible = false
      this.resetForm()
    },
    // 提交表单
    submitForm(formName) {
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
          try {
            // 准备提交数据，将departmentIds数组转换为逗号分隔的字符串
            const submitData = {
              ...this.diagnosisForm,
              departmentIds: Array.isArray(this.diagnosisForm.departmentIds)
                ? this.diagnosisForm.departmentIds.join(',')
                : this.diagnosisForm.departmentIds
            }
            await editDiagnosis(submitData)
            this.$message.success(this.diagnosisForm.id ? '编辑成功' : '添加成功')
            this.dialogTableVisible = false
            this.fetchList()
          } catch (error) {
            console.error('保存诊断失败', error)
          }
        } else {
          return false
        }
      })
    },
    // 修改状态（启用/停用）
    changeStatus(id, status) {
      const statusText = status === 1 ? '启用' : '停用'
      this.$confirm(`确定要${statusText}该诊断吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await changeDiagnosisStatus({ id, status })
          this.$message.success(`${statusText}成功`)
          this.fetchList()
        } catch (error) {
          console.error(`${statusText}失败`, error)
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .btn-group .el-form-item__content {
  text-align: center;
  margin-left: 0!important;
}
</style>
