<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button v-waves type="primary" icon="el-icon-plus" @click="addConsultationFee">添加</el-button>
    </div>

    <el-table
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;"
      :row-style="{ height: '42px' }"
      :header-row-style="{ height: '42px' }"
      :header-cell-style="{
        background: '#F8F9FB'
      }"
    >
      <el-table-column label="ID" prop="id" align="center" width="80" />
      <el-table-column label="职称" prop="rank" align="center">
        <template slot-scope="scope">
          <span>{{ formatRank(scope.row.rank) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="图文诊费" prop="pictureTextConsultationFee" align="center">
        <template slot-scope="scope">
          <span>{{ formatFee(scope.row.pictureTextConsultationFee) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="视频诊费" prop="videoConsultationFee" align="center">
        <template slot-scope="scope">
          <span>{{ formatFee(scope.row.videoConsultationFee) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createdAt" align="center" />
      <el-table-column label="创建人" prop="createdBy" align="center" />
      <el-table-column label="操作" width="280" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.available"
            size="mini"
            type="danger"
            @click="changeStatus(scope.row.id, 0)"
          >停用</el-button>
          <el-button
            v-else
            type="primary"
            size="mini"
            @click="changeStatus(scope.row.id, 1)"
          >启用</el-button>
          <el-button type="primary" size="mini" @click="editConsultationFee(scope.row)">编辑</el-button>
          <el-button type="primary" size="mini" @click="viewConsultationFee(scope.row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="fetchList"
    />

    <!-- 添加/编辑诊费 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogTableVisible" width="40%" @close="handleCancel">
      <el-form ref="dataForm" :model="consultationFeeForm" :rules="rules" label-position="right" label-width="100px">
        <el-form-item label="职称" prop="rank">
          <el-select
            v-model="consultationFeeForm.rank"
            placeholder="请选择职称"
            :disabled="viewMode"
            style="width: 100%"
          >
            <el-option
              v-for="item in rankOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="图文诊费" prop="pictureTextConsultationFee">
          <el-input-number
            v-model="consultationFeeForm.pictureTextConsultationFee"
            :precision="2"
            :min="0"
            :max="9999.99"
            :disabled="viewMode"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="视频诊费" prop="videoConsultationFee">
          <el-input-number
            v-model="consultationFeeForm.videoConsultationFee"
            :precision="2"
            :min="0"
            :max="9999.99"
            :disabled="viewMode"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item v-if="!viewMode" style="text-align: center;">
          <el-button @click="handleCancel">取 消</el-button>
          <el-button type="primary" @click="submitForm('dataForm')">保 存</el-button>
        </el-form-item>
        <el-form-item v-else style="text-align: center;">
          <el-button type="primary" @click="handleCancel">关 闭</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getList, editConsultationFee, changeConsultationFeeStatus } from '@/api/baseConfig/consultationFee'
import waves from '@/directive/waves'

export default {
  name: 'ConsultationFee',
  directives: { waves },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      rankOptions: [
        { value: '0', label: '医师' },
        { value: '1', label: '副主任医师' },
        { value: '2', label: '主任医师' },
        { value: '3', label: '主治医师' }
      ],
      dialogTitle: '添加职称对应诊费',
      dialogTableVisible: false,
      viewMode: false,
      originalTitleId: null, // 用于记录编辑前的职称ID
      consultationFeeForm: {
        rank: null,
        pictureTextConsultationFee: null,
        videoConsultationFee: null
      },
      rules: {
        rank: [
          { required: true, message: '请选择职称', trigger: 'change' }
          // { validator: validateTitle, trigger: 'change' }
        ],
        pictureTextConsultationFee: [{ required: true, message: '请输入图文诊费', trigger: 'blur' }],
        videoConsultationFee: [{ required: true, message: '请输入视频诊费', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.fetchList()
  },
  methods: {
    // 获取列表数据
    async fetchList() {
      try {
        const response = await getList(this.listQuery)
        this.list = response || []
        this.total = response ? response.length : 0
      } catch (error) {
        console.error('获取诊费列表失败', error)
      }
    },
    // 格式化费用显示
    formatFee(fee) {
      if (fee === null || fee === undefined) return '-'
      return `¥${parseFloat(fee).toFixed(2)}`
    },
    // 格式化职称显示
    formatRank(rank) {
      const rankMap = {
        '0': '医师',
        '1': '副主任医师',
        '2': '主任医师',
        '3': '主治医师'
      }
      return rankMap[rank] || '-'
    },
    // 添加诊费
    addConsultationFee() {
      this.dialogTitle = '添加职称对应诊费'
      this.dialogTableVisible = true
      this.viewMode = false
      this.resetForm()
    },
    // 编辑诊费
    editConsultationFee(row) {
      this.dialogTitle = '编辑职称对应诊费'
      this.dialogTableVisible = true
      this.viewMode = false
      this.resetForm()

      this.originalTitleId = row.rank
      this.consultationFeeForm = {
        id: row.id,
        rank: row.rank, // 保持字符串类型
        pictureTextConsultationFee: row.pictureTextConsultationFee,
        videoConsultationFee: row.videoConsultationFee
      }
    },
    // 查看诊费
    viewConsultationFee(row) {
      this.dialogTitle = '查看职称对应诊费'
      this.dialogTableVisible = true
      this.viewMode = true
      this.resetForm()

      this.consultationFeeForm = {
        id: row.id,
        rank: row.rank, // 保持字符串类型
        pictureTextConsultationFee: row.pictureTextConsultationFee,
        videoConsultationFee: row.videoConsultationFee
      }
    },
    // 重置表单
    resetForm() {
      this.originalTitleId = null
      this.consultationFeeForm = {
        rank: null,
        pictureTextConsultationFee: null,
        videoConsultationFee: null
      }
      // 如果表单已经被渲染，则重置校验结果
      if (this.$refs.dataForm) {
        this.$refs.dataForm.resetFields()
      }
    },
    // 取消/关闭
    handleCancel() {
      this.dialogTableVisible = false
      this.resetForm()
    },
    // 检查职称是否重复
    checkTitleExists(rank, excludeId = null) {
      return this.list.some(item => {
        // 如果是编辑模式，排除当前编辑的记录
        if (excludeId && item.id === excludeId) {
          return false
        }
        return item.rank === rank
      })
    },
    // 提交表单
    async submitForm(formName) {
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
          try {
            // 检查职称是否已存在
            const titleExists = this.checkTitleExists(
              this.consultationFeeForm.rank,
              this.consultationFeeForm.id
            )

            if (titleExists) {
              this.$message.warning('已有相同职称对应诊费，请选择其他职称')
              return false
            }

            await editConsultationFee(this.consultationFeeForm)
            this.$message.success(this.consultationFeeForm.id ? '编辑成功' : '添加成功')
            this.dialogTableVisible = false
            this.fetchList()
          } catch (error) {
            console.error('保存诊费失败', error)
          }
        } else {
          this.$message.warning('请完善必填项')
          return false
        }
      })
    },
    // 修改状态（启用/停用）
    changeStatus(id, status) {
      const statusText = status === 1 ? '启用' : '停用'
      this.$confirm(`确定要${statusText}该诊费配置吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await changeConsultationFeeStatus({ id, status })
          this.$message.success(`${statusText}成功`)
          this.fetchList()
        } catch (error) {
          console.error(`${statusText}失败`, error)
        }
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
