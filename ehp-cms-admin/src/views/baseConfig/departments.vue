<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.id" clearable placeholder="科室ID" style="width: 150px;" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.name" clearable placeholder="科室名称" style="width: 150px;" @keyup.enter.native="handleFilter" />
      <el-cascader
        v-model="listQuery.parentId"
        :options="departmentData"
        :props="depProps"
        placeholder="上级科室"
        clearable
        @change="handleFilter"
      />
      <!-- <el-input
        v-model="listQuery.code"
        clearable
        placeholder="科室编码"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      /> -->
      <el-select v-model="listQuery.available" clearable style="width: 150px;" placeholder="是否停用" @change="handleFilter">
        <el-option v-for="item in status" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      <el-button type="primary" @click="addDepartments">添加科室 </el-button>
    </div>
    <el-table row-key="id" :data="list" fit highlight-current-row style="width: 100%;">
      <el-table-column label="ID" fixed prop="id" width="90" align="center" />
      <el-table-column label="科室名称" prop="name" width="150" align="center" />
      <!-- <el-table-column
        label="科室编码"
        prop="code"
        width="150"
        align="center"
      /> -->
      <el-table-column label="上级科室" prop="parentName" width="150" align="center" />
      <el-table-column label="科室状态" prop="available" width="150" align="center">
        <template slot-scope="{ row }">
          <el-tag size="small" :type="row.available ? '' : 'danger'">{{ row.available ? '启用' : '停用' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="诊疗科目" prop="standardName" align="center" width="150" />
      <el-table-column label="创建人" prop="createdBy" align="center" />
      <el-table-column label="创建时间" prop="createdAt" align="center" width="155px" />
      <el-table-column label="修改人" prop="changedBy" align="center" />
      <el-table-column label="修改时间" prop="changedAt" align="center" width="155px" />
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button v-if="scope.row.available" size="mini" type="danger" @click="changeStatus(scope.row.id, 0)">停用</el-button>
          <el-button v-else type="primary" size="mini" @click="changeStatus(scope.row.id, 1)">启用</el-button>
          <el-button type="primary" size="mini" @click="record(scope.row)">编辑信息 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageNo" :limit.sync="listQuery.pageSize" @pagination="getList" />
    <el-dialog :title="dialogDepartmentsTitle" :visible.sync="dialogTableVisible" width="50%" top="2vh">
      <el-form ref="dataForm" :model="departments" :rules="rules" label-position="right" label-width="130px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="科室名称" prop="name">
              <el-input v-model="departments.name" maxlength="45" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上级科室:" prop="parentId">
              <el-cascader
                v-model="departments.parentId"
                :options="departmentData"
                :props="props"
                clearable
                :show-all-levels="false"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <!-- <el-form-item label="科室编码" prop="code">
              <el-input v-model="departments.code" :maxlength="20" />
            </el-form-item> -->
            <el-form-item label="诊疗科目" prop="code">
              <el-select v-model="departments.code" clearable filterable style="width: 100%;" placeholder="请选择">
                <el-option v-for="item in dicts" :key="item.code" :label="item.name" :value="item.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="启用状态" prop="available">
              <el-select v-model="departments.available" clearable style="width: 100%;" placeholder="请选择">
                <el-option v-for="item in available" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="departments.parentId == -1" :gutter="20">
          <el-col :span="12">
            <el-form-item label="封面图">
              <uploadImage
                :value="departments.icon"
                action="/notice/message/upload"
                list-type="picture-card"
                :show-file-list="false"
                accept="image/png, image/jpeg, image/jpg"
                @success="onSuccess"
                @remove="onRemove"
              >
              </uploadImage>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24" style="text-align: center;">
            <el-button v-waves type="primary" @click="submit('dataForm')">提交 </el-button>
            <el-button v-waves type="primary" @click="close">返回</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { addDepartment, changedepartmentStatus, getDicts, getListTree } from '@/api/baseConfig/departments'
import { department } from '@/api/user/doctor'
import waves from '@/directive/waves' // Waves directive
import uploadImage from '@/components/uploadImage'

export default {
  name: 'DoctorTable',
  directives: { waves },
  filters: {},
  components: {
    uploadImage
  },
  data() {
    return {
      dialogTableVisible: false,
      dialogDepartmentsTitle: '科室详情',
      tableKey: 0,
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderBy: 'desc'
        // id: '',
        // name: '',
        // parentId: '',
        // code: '',
        // available: ''
      },
      dialogFormVisible: false,
      departmentData: [],
      dialogStatus: '',
      status: [
        {
          value: true,
          label: '是'
        },
        {
          value: false,
          label: '否'
        }
      ],
      available: [
        {
          value: true,
          label: '启用'
        },
        {
          value: false,
          label: '停用'
        }
      ],
      dicts: [],
      departments: {
        icon: []
      },
      props: {
        expandTrigger: 'hover',
        checkStrictly: false,
        emitPath: false,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      depProps: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      rules: {
        name: [{ required: true, message: '请输入科室名称', trigger: 'blur' }],
        code: [{ required: true, message: '请选择诊疗科目', trigger: 'change' }],
        available: [{ required: true, message: '请选择启用状态', trigger: 'blur' }],
        parentId: [{ required: true, message: '请选择上级科室', trigger: 'blur' }]
      },
      showUpload: false
    }
  },
  created() {
    this.handleFilter()
    this.department()
    this.fetchDicts()
  },
  methods: {
    // 获取数据
    getList() {
      getListTree(this.listQuery).then((response) => {
        console.log(response, 289)
        this.list = response
        // this.total = response.totalCount
      })
    },
    // 获取诊疗科目
    fetchDicts() {
      const params = {
        type: 2,
        name: ''
      }
      getDicts(params).then((response) => {
        this.dicts = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageNo: 1,
        pageSize: 10,
        orderBy: 'desc'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.$nextTick(() => {
        const files = document.querySelectorAll('.imgfile')
        if (files.length > 0) {
          for (let i = 0; i < files.length; i++) {
            files[i].value = null
          }
        }
        this.$refs['dataForm'].clearValidate()
      })
    },
    addDepartments() {
      this.departments.id = null
      this.dialogDepartmentsTitle = '添加科室'
      this.dialogTableVisible = true
      this.resetTemp()
      this.departments = {}
    },
    submit(dataForm) {
      this.$refs[dataForm].validate((valid) => {
        if (valid) {
          if (this.departments.parentId == '-1' && !this.departments.icon) {
            this.$message.error('请上传封面图')
            return
          }
          const params = Object.assign({}, this.departments)
          params.parentId = params.parentId === -1 ? 0 : params.parentId
          params.icon = this.departments.icon ? this.departments.icon.map((item) => item.url).join() : ''
          console.log(params, this.departments, 356)
          addDepartment(params).then((res) => {
            this.$message({
              message: this.departments.id ? '修改成功' : '添加成功',
              type: 'success',
              duration: 5 * 1000
            })
            this.department()
            this.getList()
            this.dialogTableVisible = false
          })
        }
      })
    },
    close() {
      this.dialogTableVisible = false
    },
    record(item) {
      this.dialogDepartmentsTitle = '编辑科室'
      this.dialogTableVisible = true
      this.resetTemp()
      item.parentId = item.parentId === 0 ? -1 : item.parentId
      this.departments = Object.assign({}, item)
      this.departments.icon = item.icon ? [{ url: item.icon }] : []
      if (item.parentId === -1) {
        this.showUpload = true
      } else {
        this.showUpload = false
      }
    },
    changeStatus(id, status) {
      if (status === 1) {
        this.$confirm('是否启用科室？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.sureStatus(id, status)
        })
      } else {
        this.$confirm('是否停用科室？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.sureStatus(id, status)
        })
      }
    },
    // 修改药师状态
    sureStatus(id, status) {
      changedepartmentStatus({
        id: id,
        status: status
      }).then((res) => {
        this.$message({
          message: '修改成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.getList()
      })
    },
    department() {
      department({ type: 0 }).then((response) => {
        this.departmentData = response
      })
    },
    onSuccess(data) {
      console.log(this.departments, 472)
      this.departments.icon = this.departments.icon ? this.departments.icon : []
      this.departments.icon.push({
        url: data.response.data,
        uid: data.uid
      })
      console.log(this.departments.icon, 'onSuccess', 278)
    },
    onRemove(data) {
      this.departments.icon = data
      console.log(this.departments.icon, 'onRemove', 301)
    },
    handleChange(val) {
      console.log(val)
    }
  }
}
</script>
