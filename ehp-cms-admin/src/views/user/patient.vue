<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.patientChannelNames"
        clearable
        placeholder="渠道名称"
        class="filter-item"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.id"
        clearable
        placeholder="患者ID"
        class="filter-item"
        style="width: 150px"
        onkeyup="value=value.replace(/[^\d]/g,'')"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.name"
        clearable
        placeholder="患者姓名"
        class="filter-item"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.phone"
        clearable
        placeholder="手机号"
        class="filter-item"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <DatePicker
        ref="datePickerRef"
        :query-model="listQuery"
        class="filter-item"
        style="width: 230px"
        @change="handleFilter"
      />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
    </div>
    <div>
      <el-button v-waves type="primary" @click="handleDownload">导出</el-button>
      <el-upload
        style="display: inline-block;"
        :action="uploadPath"
        :headers="headers"
        :on-success="handleExcelSuccess"
        :on-error="handleExcelError"
        :on-remove="handleRemoveExcel"
        :file-list="fileList"
        :limit="1"
        :show-file-list="false"
        accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      >
        <el-button
          type="primary"
        >批量修改渠道</el-button>
        <div
          slot="tip"
          class="el-upload__tip"
        ></div>
      </el-upload>
    </div>
    <el-table :key="tableKey" v-loading="loading" :data="list" fit highlight-current-row style="width: 100%">
      <el-table-column label="ID" prop="id" width="80px" align="center" />
      <el-table-column label="姓名" prop="name" width="100px" align="center">
        <template slot-scope="{row}">
          <PatientDetails
            :patient-id="row.id"
            :patient-name="row.name"
            :patient-nick-name="row.nickName"
          />
        </template>
      </el-table-column>
      <el-table-column label="头像" prop="headUrl" width="80px" align="center">
        <template slot-scope="{row}">
          <el-image :src="row.headUrl">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="昵称" prop="nickName" width="170px" align="center" />
      <!-- <el-table-column label="类型" prop="typeDescribe" width="60px" align="center" /> -->
      <el-table-column label="手机" prop="phone" width="110px" align="center">
        <template slot-scope="{ row }">
          <Desensitization v-model="row.phone" :data="row" :type="'phone'" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="婚姻状况" width="80px" align="center">
        <template slot-scope="{row}">
          <el-tag v-if="row.maritalStatus === 1">已婚</el-tag>
          <el-tag v-else-if="row.maritalStatus === 0">未婚</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="性别" prop="genderDescribe" width="60px" align="center" />
      <!-- <el-table-column label="认证状态" prop="statusDescribe" width="100px" align="center">
        <template slot-scope="{row}">
          <el-tag v-if="row.status === 0" size="small">{{ row.statusDescribe }}</el-tag>
          <el-tag v-if="row.status === 1" size="small" type="success">{{ row.statusDescribe }}</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="城市" prop="cityName" width="100px" align="center" />
      <el-table-column label="服务号关注状态" prop="cancelDescribe" align="center">
        <template slot-scope="{row}">
          <el-tag v-if="row.cancel === 0" size="small" type="warning">{{ row.cancelDescribe }}</el-tag>
          <el-tag v-if="row.cancel === 1" size="small" type="success">{{ row.cancelDescribe }}</el-tag>
          <el-tag v-if="row.cancel === 2" size="small" type="danger">{{ row.cancelDescribe }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="注册时间" prop="createdAt" width="140px" align="center" />
      <el-table-column label="分享人ID" align="center">
        <template slot-scope="{row}">
          <span>{{ row.channel && row.channel.userId || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分享人姓名" align="center">
        <template slot-scope="{row}">
          <span>{{ row.channel && row.channel.userName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="绑定时间" align="center">
        <template slot-scope="{row}">
          <span>{{ row.channel && row.channel.createdAt || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属渠道" prop="patientChannelName" align="center" />
      <el-table-column label="操作" fixed="right" align="center" width="150">
        <template slot-scope="{ row }">
          <el-button
            type="primary"
            @click="handleDetail(row)"
          >修改渠道</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog
      title="修改渠道"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      top="2vh"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-form-item label="渠道名称:" prop="patientChannelId">
          <el-select
            v-model="form.patientChannelId"
            placeholder="请选择渠道"
          >
            <el-option
              v-for="item in channelList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="updateData()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getList, exportData, excelURL, tempUrl, changeChannel } from '@/api/user/patient'
import { getSelectList as getChannelList } from '@/api/channel/patient'
import waves from '@/directive/waves' // Waves directive
import PatientDetails from './components/patientDetails'
import DatePicker from '@/components/DatePicker'
import Desensitization from '@/components/Desensitization'
import { getToken, getTokenName } from '@/utils/auth'
export default {
  name: 'Userpatient',
  directives: { waves },
  filters: {},
  components: {
    PatientDetails,
    DatePicker,
    Desensitization
  },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'id',
        orderBy: 'desc',
        id: '',
        name: '',
        phone: ''
      },
      textMap: {
        update: '更新',
        create: '新增'
      },
      form: {
        patientId: '',
        patientChannelId: ''
      },
      rules: {
        patientChannelId: [{ required: true, message: '请选择渠道', trigger: 'blur' }]
      },
      dialogFormVisible: false,
      channelList: [],
      uploadPath: '',
      headers: {},
      tempPath: '',
      fileList: [],
      loading: false
    }
  },
  created() {
    // this.handleFilter()
  },
  activated() {
    this.init()
    this.getList()
    console.log('============activated=============')
  },
  methods: {
    init() {
      this.uploadPath = excelURL()
      this.headers[getTokenName()] = getToken()
      this.tempUrl()
      getChannelList().then(data => {
        if (data) {
          this.channelList = data
        }
      })
    },
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.id = ''
      this.listQuery.name = ''
      this.listQuery.phone = ''
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    },
    // 编辑 查看
    handleDetail(row) {
      this.form.patientChannelId = row.patientChannelId
      this.patientId = row.id
      this.dialogFormVisible = true
    },
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          changeChannel(this.patientId, this.form.patientChannelId).then(() => {
            this.dialogFormVisible = false
            this.$message.success('保存成功')
            this.getList()
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    handleDownload() {
      const params = Object.assign({}, this.listQuery)
      params[getTokenName()] = getToken()
      this.downloadUrl = exportData(params)
      window.location.href = this.downloadUrl
    },
    handleExcelError() {
      this.fileList = []
    },
    handleExcelSuccess(response, file, fileList) {
      this.fileList = []
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.getList()
        this.$message.success(response.msg)
      }
    },
    handleRemoveExcel(file, fileList) {},
    tempUrl() {
      tempUrl().then((response) => {
        this.tempPath = response
      })
    }
  }
}
</script>
