<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        clearable
        placeholder="患者ID"
        class="filter-item"
        style="width: 150px"
        onkeyup="value=value.replace(/[^\d]/g,'')"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.name"
        clearable
        placeholder="患者姓名"
        class="filter-item"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.phone"
        clearable
        placeholder="手机号"
        class="filter-item"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <DatePicker
        ref="datePickerRef"
        :query-model="listQuery"
        class="filter-item"
        style="width: 230px"
        @change="handleFilter"
      />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
    </div>

    <el-table :key="tableKey" :data="list" fit highlight-current-row style="width: 100%">
      <el-table-column label="ID" prop="id" width="80px" align="center" />
      <el-table-column label="姓名" prop="name" width="100px" align="center">
        <template slot-scope="{row}">
          <PatientDetails
            :patient-id="row.id"
            :patient-name="row.name"
            :patient-nick-name="row.nickName"
          />
        </template>
      </el-table-column>
      <el-table-column label="头像" prop="headUrl" width="80px" align="center">
        <template slot-scope="{row}">
          <el-image :src="row.headUrl">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline" />
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="昵称" prop="nickName" width="170px" align="center" />
      <!-- <el-table-column label="类型" prop="typeDescribe" width="60px" align="center" /> -->
      <el-table-column label="手机" prop="phone" width="110px" align="center">
        <template slot-scope="{ row }">
          <Desensitization v-model="row.phone" :data="row" :type="'phone'" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="婚姻状况" width="80px" align="center">
        <template slot-scope="{row}">
          <el-tag v-if="row.maritalStatus === 1">已婚</el-tag>
          <el-tag v-else-if="row.maritalStatus === 0">未婚</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="性别" prop="genderDescribe" width="60px" align="center" />
      <!-- <el-table-column label="认证状态" prop="statusDescribe" width="100px" align="center">
        <template slot-scope="{row}">
          <el-tag v-if="row.status === 0" size="small">{{ row.statusDescribe }}</el-tag>
          <el-tag v-if="row.status === 1" size="small" type="success">{{ row.statusDescribe }}</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column label="城市" prop="cityName" width="100px" align="center" />
      <el-table-column label="服务号关注状态" prop="cancelDescribe" align="center">
        <template slot-scope="{row}">
          <el-tag v-if="row.cancel === 0" size="small" type="warning">{{ row.cancelDescribe }}</el-tag>
          <el-tag v-if="row.cancel === 1" size="small" type="success">{{ row.cancelDescribe }}</el-tag>
          <el-tag v-if="row.cancel === 2" size="small" type="danger">{{ row.cancelDescribe }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="注册时间" prop="createdAt" width="140px" align="center" />
      <el-table-column label="分享人ID" align="center">
        <template slot-scope="{row}">
          <span>{{ row.channel && row.channel.userId || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分享人姓名" align="center">
        <template slot-scope="{row}">
          <span>{{ row.channel && row.channel.userName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="绑定时间" align="center">
        <template slot-scope="{row}">
          <span>{{ row.channel && row.channel.createdAt || '-' }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="上次登录地址" prop="loginAddr" align="center" /> -->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getList } from '@/api/user/patient'
import waves from '@/directive/waves' // Waves directive
import PatientDetails from './components/patientDetails'
import DatePicker from '@/components/DatePicker'
import Desensitization from '@/components/Desensitization'
export default {
  name: 'Userpatient',
  directives: { waves },
  filters: {},
  components: {
    PatientDetails,
    DatePicker,
    Desensitization
  },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'id',
        orderBy: 'desc',
        id: '',
        name: '',
        phone: ''
      },
      textMap: {
        update: '更新',
        create: '新增'
      }
    }
  },
  created() {
    // this.handleFilter()
  },
  activated() {
    this.getList()
    console.log('============activated=============')
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.id = ''
      this.listQuery.name = ''
      this.listQuery.phone = ''
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    }
  }
}
</script>
