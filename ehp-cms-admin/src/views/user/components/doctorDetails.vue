<template>
  <div>
    <el-dialog
      title="医生详情"
      :visible.sync="dialogInfoVisible"
      width="80%"
      top="2vh"
      append-to-body
      @close="handleCloseDetails"
    >
      <el-tabs v-model="activeTabs" style="min-height: 300px;" type="card">
        <el-tab-pane label="资质信息" name="tab_1">
          <el-form ref="dataForm" :model="doctor" label-position="right" label-width="150px">
            <el-row>
              <el-col :span="13">
                <el-form-item label="认证状态:" prop="status">
                  <div v-if="doctor.baseInfo.status === 2" style="color: green;">{{ doctor.baseInfo.statusDescribe }}
                  </div>
                  <div v-else style="color: red;">{{ doctor.baseInfo.statusDescribe }}</div>
                </el-form-item>
                <el-form-item label="所属经纪人:" prop="agentName">
                  <el-input v-model="doctor.licences.agentName" readonly />
                </el-form-item>
                <el-form-item label="ID:" prop="id">
                  <el-input v-model="doctor.baseInfo.id" readonly />
                </el-form-item>

                <el-form-item label="手机号:" prop="phone">
                  <!-- <el-input v-model="doctor.baseInfo.phone" readonly /> -->
                  <Desensitization v-model="doctor.baseInfo.phone" :data="doctor.baseInfo" :type="'phone'" :view="'input'" />
                </el-form-item>
                <el-form-item label="邮箱:" prop="email">
                  <!-- <el-input v-model="doctor.baseInfo.email" readonly /> -->
                  <Desensitization v-model="doctor.baseInfo.email" :data="doctor.baseInfo" :type="'email'" :view="'input'" />
                </el-form-item>
                <el-form-item label="姓名:" prop="name">
                  <el-input v-model="doctor.baseInfo.name" readonly />
                </el-form-item>
                <!-- <FilterInputView
                  label="手机号:"
                  type="phone"
                  :value.sync="doctor.baseInfo.phone"
                />
                <FilterInputView
                  label="邮箱:"
                  type="email"
                  :value.sync="doctor.baseInfo.email"
                />
                <FilterInputView
                  label="姓名:"
                  type="name"
                  :value.sync="doctor.baseInfo.name"
                /> -->
                <el-form-item label="科室:" prop="departmentName">
                  <el-input v-model="doctor.baseInfo.departmentName" readonly />
                </el-form-item>
                <el-form-item label="职称:" prop="titleId">
                  <el-input v-model="doctor.baseInfo.titleIdDescribe" readonly />
                </el-form-item>
                <el-form-item label="执业医院:" prop="hospitalName">
                  <el-input v-model="doctor.baseInfo.hospitalName" readonly />
                </el-form-item>
                <el-form-item label="注册时间:" prop="createdAt">
                  <el-input v-model="doctor.baseInfo.createdAt" readonly />
                </el-form-item>
                <el-form-item label="执业医院等级:" prop="hospitalLevel">
                  <el-input v-model="doctor.baseInfo.hospitalLevel" readonly />
                </el-form-item>
                <el-form-item label="医生所在地:" prop="cityName">
                  <el-input v-model="doctor.baseInfo.cityName" readonly />
                </el-form-item>
                <el-form-item label="擅长:" prop="expertise">
                  <el-input v-model="doctor.baseInfo.expertise" type="textarea" readonly />
                </el-form-item>
                <el-form-item label="个人简介:" prop="introduction">
                  <el-input v-model="doctor.baseInfo.introduction" type="textarea" readonly />
                </el-form-item>
                <el-form-item label="CA签名凭证:" prop="sealCertNumber">
                  <el-input v-model="doctor.baseInfo.sealCertNumber" type="textarea" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="8" :offset="1">
                <el-image ref="previewImgHead" fit="cover" style="width:300px;height:300px" :src="headUrl" :preview-src-list="[headUrl]">
                  <div slot="error" class="image-slot headUrl">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
                <div>
                  <img
                    style="width:30px;height:30px"
                    src="/static/images/download.png"
                    alt=""
                    @click="handleDownload(headUrl)"
                  >
                  <img
                    style="width:30px;height:30px"
                    src="/static/images/enlarge.png"
                    alt=""
                    @click="previewPic('previewImgHead')"
                  >
                  <!-- <i
                    style="font-size:25px"
                    class="el-icon-download"
                    @click="handleDownload(headUrl)"
                  ></i>
                  <i
                    class="el-icon-zoom-in"
                    style="font-size:25px"
                    @click="handlePreview(headUrl)"
                  ></i> -->
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-divider></el-divider>
                <el-form-item label="医师工作证:" prop="emCard">
                  <template v-if="doctor.baseInfo.emCard">
                    <el-row :gutter="20">
                      <el-col v-for="(img, key) in doctor.baseInfo.emCard.urls" :key="key" :span="11">
                        <el-image ref="previewImgCard" style="width:100%;height:300px" fit="cover" :src="img">
                          <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline"></i>
                          </div>
                        </el-image>
                        <div>
                          <img
                            style="width:30px;height:30px"
                            src="/static/images/download.png"
                            alt=""
                            @click="handleDownload(img)"
                          >
                          <img
                            style="width:30px;height:30px"
                            src="/static/images/enlarge.png"
                            alt=""
                            @click="previewPic('previewImgCard')"
                          >
                          <!-- <i
                            style="font-size:25px;z-index:99999"
                            class="el-icon-download"
                            @click="handleDownload(img)"
                          ></i>
                          <i
                            class="el-icon-zoom-in"
                            style="font-size:25px;z-index:9999"
                            @click="handlePreview(img)"
                          ></i> -->
                        </div>
                      </el-col>
                    </el-row>
                  </template>
                  <template v-else>
                    <div>未上传</div>
                  </template>
                </el-form-item>
                <el-form-item style="margin-bottom:0px;margin-top:20px;" label="身份证:" prop="idCard">
                  <div>姓名: {{ doctor.licences.name }}</div>
                  <template v-if="doctor.licences.idCard">
                    <span id="idCard">身份证号: </span>
                    <el-input v-model="doctor.licences.idCard.number" style="width:300px" readonly />
                    <!-- <Desensitization v-model="doctor.licences.idCard.number" style="width:300px" :data="doctor.licences.idCard" :view="'input'" :type="'cardNo'" /> -->
                    <el-row :gutter="20" style="margin-top:10px">
                      <el-col v-for="(img, key) in doctor.licences.idCard.urls" :key="key" :span="11">
                        <el-image :ref="'previewImgIdCard' + key" style="width:100%;height:300px" fit="cover" :src="img" :preview-src-list="[img]">
                          <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline"></i>
                          </div>
                        </el-image>
                        <div>
                          <img
                            style="width:30px;height:30px"
                            src="/static/images/download.png"
                            alt=""
                            @click="handleDownload(img)"
                          >
                          <img
                            style="width:30px;height:30px"
                            src="/static/images/enlarge.png"
                            alt=""
                            @click="previewPicArr('previewImgIdCard' + key)"
                          >
                        </div>
                      </el-col>
                    </el-row>
                  </template>
                  <template v-else>
                    <div>未上传</div>
                  </template>
                </el-form-item>
                <el-divider></el-divider>

                <el-form-item label="医师执业证书:" prop="medical">
                  <template v-if="doctor.baseInfo.medical">
                    <span id="medical">执业证书编号：</span>
                    <el-input v-model="doctor.baseInfo.medical.number" style="width:300px" readonly />
                    <el-button type="primary" size="mini" @click="handleSetPreview(doctor.baseInfo.medical.urls,2)">预览编辑
                    </el-button>
                    <el-row :gutter="20" style="margin-top:10px">
                      <el-col v-for="(img, key) in doctor.baseInfo.medical.urls" :key="key" :span="11">
                        <el-image :ref="'previewImgMed' + key" style="width:100%;height:300px" fit="cover" :src="img" :preview-src-list="[img]">
                          <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline"></i>
                          </div>
                        </el-image>
                        <div>
                          <img
                            style="width:30px;height:30px"
                            src="/static/images/download.png"
                            alt=""
                            @click="handleDownload(img)"
                          >
                          <img
                            style="width:30px;height:30px"
                            src="/static/images/enlarge.png"
                            alt=""
                            @click="previewPicArr('previewImgMed' + key)"
                          >
                          <!-- <i
                            style="font-size:25px;z-index:99999"
                            class="el-icon-download"
                            @click="handleDownload(img)"
                          ></i>
                          <i
                            class="el-icon-zoom-in"
                            style="font-size:25px;z-index:9999"
                            @click="handlePreview(img)"
                          ></i> -->
                        </div>
                      </el-col>
                    </el-row>
                  </template>
                  <template v-else>
                    <div>未上传</div>
                  </template>
                </el-form-item>
                <el-divider></el-divider>
                <el-form-item
                  style="margin-bottom:0px;margin-top:20px;"
                  label="医师资格证书:"
                  prop="qualificationCertificate"
                >
                  <template v-if="doctor.licences.qualificationCertificate">
                    <span id="qualificationCertificate">资格证书编号：</span>
                    <el-input v-model="doctor.licences.qualificationCertificate.number" style="width:300px" readonly />
                    <el-button
                      type="primary"
                      size="mini"
                      @click="handleSetPreview(doctor.licences.qualificationCertificate.urls,3)"
                    >预览编辑</el-button>
                    <el-row :gutter="20" style="margin-top:10px">
                      <el-col v-for="(img, key) in doctor.licences.qualificationCertificate.urls" :key="key" :span="11">
                        <el-image :ref="'previewImgCertificate' +key" style="width:100%;height:300px" fit="cover" :src="img" :preview-src-list="[img]">
                          <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline"></i>
                          </div>
                        </el-image>
                        <div>
                          <!-- <i
                            style="font-size:25px;z-index:99999"
                            class="el-icon-download"
                            @click="handleDownload(img)"
                          ></i>
                          <i
                            class="el-icon-zoom-in"
                            style="font-size:25px;z-index:9999"
                            @click="handlePreview(img)"
                          ></i> -->
                          <img
                            style="width:30px;height:30px"
                            src="/static/images/download.png"
                            alt=""
                            @click="handleDownload(img)"
                          >
                          <img
                            style="width:30px;height:30px"
                            src="/static/images/enlarge.png"
                            alt=""
                            @click="previewPicArr('previewImgCertificate' +key)"
                          >
                        </div>
                      </el-col>
                    </el-row>
                  </template>
                  <template v-else>
                    <div>未上传</div>
                  </template>
                </el-form-item>
                <el-divider></el-divider>
                <el-form-item style="margin-bottom:0px;margin-top:20px;" label="职称证书:" prop="titleCertificate">
                  <template v-if="doctor.licences.titleCertificate">
                    <el-row :gutter="20">
                      <el-col v-for="(img, key) in doctor.licences.titleCertificate.urls" :key="key" :span="11">
                        <el-image :ref="'previewImgCertificate1' + key" style="width:100%;height:300px" fit="cover" :src="img" :preview-src-list="[img]">
                          <div slot="error" class="image-slot">
                            <i class="el-icon-picture-outline"></i>
                          </div>
                        </el-image>
                        <div>
                          <img
                            style="width:30px;height:30px"
                            src="/static/images/download.png"
                            alt=""
                            @click="handleDownload(img)"
                          >
                          <img
                            style="width:30px;height:30px"
                            src="/static/images/enlarge.png"
                            alt=""
                            @click="previewPicArr('previewImgCertificate1' +key)"
                          >
                          <!-- <i
                            style="font-size:25px;z-index:99999"
                            class="el-icon-download"
                            @click="handleDownload(img)"
                          ></i>
                          <i
                            class="el-icon-zoom-in"
                            style="font-size:25px;z-index:9999"
                            @click="handlePreview(img)"
                          ></i> -->
                        </div>
                      </el-col>
                    </el-row>
                  </template>
                  <template v-else>
                    <div>未上传</div>
                  </template>
                </el-form-item>

                <el-divider></el-divider>
                <el-form-item style="margin-bottom:0px;margin-top:20px;" label="签名图片:" prop="sealImage">
                  <template v-if="doctor.baseInfo.sealImage">
                    <el-image
                      fit="cover"
                      :src="doctor.baseInfo.sealImage+'?v='+ new Date().getMilliseconds()"
                      style="max-width: 45%;margin-right:2%;"
                      :preview-src-list="[doctor.baseInfo.sealImage + '?v=' + new Date().getMilliseconds()]"
                      @click="clickImage(5)"
                    >
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </template>
                  <template v-else>
                    <div>未上传</div>
                  </template>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-row :gutter="20">
            <el-col :span="24" style="text-align:right;">
              <!-- <el-button
                v-if="doctor.baseInfo.status === 0 || doctor.baseInfo.status === 3"
                v-permission="['user:doctor:audit']"
                v-waves
                type="primary"
                @click="handleStatus()"
              >认证审核</el-button> -->
              <el-button
                v-if="!checkSwitch() && checkPer(['user:doctor:audit'])"
                v-waves
                :disabled="doctor.baseInfo.recordStatus != 1"
                type="primary"
                @click="handleStatus()"
              >认证审核 </el-button>
              <el-button
                v-if="checkSwitch() && checkPer(['user:doctor:audit'])"
                type="primary"
                size="mini"
                @click="handleRecord(doctor.baseInfo.id)"
              >完善备案信息</el-button>
              <!-- <el-button
                v-show="doctor.baseInfo.status == 2"
                v-permission="['user:doctor:listAudit']"
                :disabled="doctor.baseInfo.recordStatus != 1"
                type="primary"
                size="mini"
                class="mgt"
                @click="handleRecordDefeated(doctor.baseInfo.id)"
              >备案审核</el-button>

              <el-button
                v-show=" doctor.baseInfo.status == 2"
                v-permission="['user:doctor:listAudit']"
                :disabled="doctor.baseInfo.recordStatus == 2"
                type="primary"
                size="mini"
                class="mgt"
                @click="handleRecordDefeated(3, doctor.baseInfo.id)"
              >备案失败</el-button> -->
              <el-button v-permission="['user:doctor:update']" v-waves type="primary" @click="setDoctorInfo()">编辑资料
              </el-button>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24" style="padding-bottom: 10px;">审核记录</el-col>
          </el-row>
          <el-table :key="tableKey" :data="doctor.logs" highlight-current-row fit style="width: 100%">
            <el-table-column label="类型" prop="type" align="center" width="100px">
              <template slot-scope="{row}">
                <span v-if="row.type == 1">认证资料</span>
                <span v-if="row.type == 2">备案审核</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="审核意见" prop="statusDescribe" />
            <el-table-column align="center" label="审核原因" prop="failureReason" />
            <el-table-column align="center" label="审核人" width="250px" prop="createdBy" />
            <el-table-column align="center" label="审核时间" width="155px" prop="createdAt" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="咨询记录" name="tab_3">
          <el-table :key="tableKey" :data="doctor.consultSessionList" highlight-current-row fit style="width: 100%">
            <el-table-column align="center" label="患者ID" prop="patientId" />
            <el-table-column align="center" label="就诊人姓名" prop="inquirerName" />
            <el-table-column align="center" label="陪诊人姓名" prop="patientName" />
            <el-table-column align="center" label="与陪诊人关系" prop="inquirerRelationName" />
            <el-table-column align="center" label="会话ID" width="250px" prop="sessionId" />
            <el-table-column align="center" label="开始时间" width="155px" prop="startTime" />
            <el-table-column align="center" label="结束时间" width="155px" prop="endTime" />
            <el-table-column align="center" label="金额" width="80px" prop="cost" />
            <el-table-column align="center" label="会话关系" prop="relationDescribe" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="评价记录" name="tab_4">
          <div class="dispj">
            <div>好评率：{{ doctor.praiseRate }}</div>
            <div>服务患者次数：{{ doctor.consultSessionList?doctor.consultSessionList.length:0 }}</div>
            <div>服务患者数：{{ doctor.patientNum }}</div>
            <div>患者评论人数：{{ commemttotal }}</div>
          </div>
          <el-table :key="tableKey1" :data="consultCommentList" highlight-current-row fit style="width: 100%">
            <el-table-column align="center" label="患者ID" prop="patientId" />
            <el-table-column align="center" label="患者名称" prop="patientNickName" />
            <el-table-column align="center" label="会话关系" prop="relationDescribe" />
            <el-table-column align="center" label="评论时间" width="155px" prop="createdAt" />
            <el-table-column align="center" label="评分" width="250px" prop="starLevel">
              <template slot-scope="{row}">
                <el-rate v-model="row.starLevel" disabled disabled-void-color="#cccccc" text-color="#ff9900"></el-rate>
              </template>
            </el-table-column>
            <el-table-column align="center" label="评论内容" width="155px" prop="commentContent" />
          </el-table>
          <pagination
            v-show="commemttotal>0"
            :total="commemttotal"
            :page.sync="paramsComment.pageNo"
            :limit.sync="paramsComment.pageSize"
            @pagination="getCommentPage"
          />
        </el-tab-pane>
        <!-- <el-tab-pane label="投诉记录" name="tab_5">
          <div class="dispj">
            <div>服务患者数：{{ doctor.patientNum }}</div>
            <div>患者投诉人数：{{ complainttotal }}</div>
          </div>
          <el-table :key="tableKey2" :data="consultComplaintList" highlight-current-row fit style="width: 100%">
            <el-table-column align="center" label="患者ID" prop="patientId" />
            <el-table-column align="center" label="患者名称" prop="patientNickName" />
            <el-table-column align="center" label="会话关系" prop="relationDescribe" />
            <el-table-column align="center" label="投诉时间" width="155px" prop="createdAt" />
            <el-table-column align="center" label="投诉内容" width="155px" prop="question" />
          </el-table>
          <pagination
            v-show="complainttotal>0"
            :total="complainttotal"
            :page.sync="paramsComment.pageNo"
            :limit.sync="paramsComment.pageSize"
            @pagination="getCommentPage"
          />
        </el-tab-pane> -->
        <el-tab-pane label="诊费价格" name="tab_6">
          <el-row :gutter="20">
            <el-col :span="7">
              <span>图文诊费价格 :</span>
              <el-input
                v-model="doctor.consultCharge"
                clearable
                class="filter-item"
                style="width: 150px"
                :readonly="true"
              />
              <span>元</span>
            </el-col>
            <el-col :span="7">

              <span>视频诊费价格 :</span>
              <el-input
                v-model="doctor.videoConsultCharge"
                clearable
                class="filter-item"
                style="width: 150px"
                :readonly="true"
              />
              <span>元</span>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="药店列表" name="tab_7">
          <el-row :gutter="20">
            <el-col :span="4"><div class="grid-content bg-purple">请选择绑定药店：</div></el-col>
            <el-col :span="12">
              <el-autocomplete
                v-model="search"
                class="inline-input auto_input"
                :fetch-suggestions="querySearch"
                placeholder="按药店名称"
                :trigger-on-focus="false"
                :readonly="doctor.baseInfo.status !== 2"
                @select="handleSelect"
              >
                <template slot-scope="{ item }">
                  <div class="name">{{ item.name }}</div>
                </template>
              </el-autocomplete>
            </el-col>
          </el-row>
          <div class="sub-title">已绑定药店列表：</div>
          <el-table
            :key="tableKey"
            :data="pharmacy.list"
            highlight-current-row
            fit
            style="width: 100%"
          >
            <el-table-column align="center" label="药店ID" width="100px" prop="id" />
            <el-table-column align="center" label="药店名称" width="250px" prop="name" />
            <el-table-column align="center" label="药店地址" prop="address" />
            <el-table-column align="center" label="商品开放数量" width="100px" prop="sumTcmCount" />
            <el-table-column label="操作" width="100px" align="center">
              <template slot-scope="{row}">
                <el-button
                  size="mini"
                  type="danger"
                  @click="delPharmacy(row.id)"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="pharmacy.total>0"
            :total="pharmacy.total"
            :page.sync="pharmacy.listQuery.pageNo"
            :limit.sync="pharmacy.listQuery.pageSize"
            @pagination="pharmacyList"
          />
        </el-tab-pane>
        <el-tab-pane label="医生配置" name="tab_8">
          <el-row :gutter="20">
            <el-col :span="4"><div class="grid-content bg-purple">是否显示中药处方：</div></el-col>
            <el-col :span="12">
              <el-radio-group v-model="tcmFlag">
                <el-radio :label="1">显示</el-radio>
                <el-radio :label="0">不显示</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <div style="margin-top: 40px;">
            <el-button type="primary" @click="saveConfig('dataForm')">保存配置</el-button>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <el-dialog title="认证审核" :visible.sync="dialogStatusInfoVisible" append-to-body>

      <el-form
        ref="userStatusDataForm"
        :model="userStatus"
        :rules="statusRules"
        label-position="right"
        label-width="150px"
        style="width:90%"
      >

        <el-form-item label="审核状态" prop="status">

          <el-select v-model="userStatus.status" placeholder="请选择审核状态">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          认证通过医生即可线上诊疗，请确认监管平台已通过该医生的备案
        </el-form-item>
        <el-form-item v-if="userStatus.status == 3" label="审核原因" prop="failureReason">
          <el-input v-model="userStatus.failureReason" type="textarea" :maxlength="35" :rows="2" placeholder="请输入内容">
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogStatusInfoVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleStatusData()">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="备案审核" :visible.sync="dialogRecordStatusInfoVisible" append-to-body>

      <el-form
        ref="userRecordStatusDataForm"
        :model="userRecordStatus"
        :rules="recordStatusRules"
        label-position="right"
        label-width="150px"
        style="width:90%"
      >
        <el-form-item label="审核状态" prop="status">
          <el-select v-model="userRecordStatus.status" placeholder="请选择审核状态">
            <el-option v-for="item in recordStatusOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="userRecordStatus.status == 3" label="审核原因" prop="failureReason">
          <el-input v-model="userRecordStatus.failureReason" type="textarea" :rows="2" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogRecordStatusInfoVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleRecordStatusData()">确 定</el-button>
      </span>
    </el-dialog>
    <el-link v-if="doctorName" v-waves type="primary" :underline="false" @click="handleDoctorDetailsInfo()">
      {{ doctorName }}</el-link>

    <Doctoredit
      v-if="dialogInfoVisible"
      ref="setDocinfo"
      :doctor-id="doctorId"
      :record-status="doctor.licences.recordStatus"
      :base-status="doctor.baseInfo.status"
      @setDatainfo="onSetDatainfo"
    >
    </Doctoredit>
    <el-dialog :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
    <el-dialog :visible.sync="dialogSetPreview" append-to-body>
      <div class="previewBox" style="margin-bottom:10px">
        <el-input v-model="previewNum" style="width:300px" />
        <!-- <el-button type="primary" @click="handleOCR()">OCR识别</el-button> -->
        <el-button type="primary" @click="handleSave()">保存</el-button>
      </div>
      <el-carousel
        ref="carousel"
        trigger="click"
        :autoplay="false"
        :height="imgHeight + 'px'"
        @change="handlecarouselChange"
      >
        <el-carousel-item v-for="item in previewArr" :key="item">
          <img ref="imgRef" width="100%" :src="item" alt @load="imgLoad" />
        </el-carousel-item>
      </el-carousel>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
  .el-dialog {
    text-align: left;
  }

  .disflex {
    display: flex;
    align-items: center;
  }

  .mgl_20 {
    margin-left: 20px;
  }

  .headUrl {
    width: 300px;
    height: 300px;
  }

  .dispj {
    display: flex;
  }

  .dispj>div {
    margin-right: 5%;
    margin-bottom: 10px;
  }

  .w_long {
    width: 100%;
  }

  .nextBox {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .previewBox-icon {
    font-size: 30px;
  }

  /deep/ .el-carousel__arrow {
    font-size: 30px;
  }

</style>
<script>
import {
  get,
  authAudit,
  recordAudit,
  getImageOCR,
  saveNumber,
  getCommentList,
  getComplaintList,
  pharmacyList,
  delPharmacy,
  querySearchKey,
  addPharmacy,
  setConfig,
  rotate
} from '@/api/user/doctor'
import waves from '@/directive/waves' // Waves directive
import Helper from '@/utils/helper'
import Doctoredit from '../doctoredit.vue'
import {
  Loading
} from 'element-ui'
// import FilterInputView from '@/components/FilterInputView/index'
import Desensitization from '@/components/Desensitization'
export default {
  name: 'DoctorDetails',
  directives: {
    waves
  },
  components: {
    Doctoredit,
    Desensitization
    // FilterInputView
  },
  props: {
    doctorId: {
      type: [String, Number],
      required: false,
      default: ''
    },
    doctorName: {
      type: String,
      required: false,
      default: ''
    },
    handleFilter: {
      type: Function,
      default: null
    },
    switch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      time: Date.now(),
      activeTabs: 'tab_1',
      dialogInfoVisible: false,
      dialogStatusInfoVisible: false,
      dialogRecordStatusInfoVisible: false,
      tableKey: 'doctorConsultSessionTable',
      tableKey1: 'commentTab',
      tableKey2: 'complaintTab',
      doctor: {
        baseInfo: {},
        consultList: [],
        licences: {},
        patientNum: 0,
        praiseRate: null
      },
      headUrl: '',
      userStatus: {
        failureReason: ''
      },
      userRecordStatus: {
        failureReason: ''
      },
      statusOptions: [{
        value: 2,
        label: '审核通过'
      },
      {
        value: 3,
        label: '审核不通过'
      }
      ],
      recordStatusOptions: [{
        value: 2,
        label: '备案成功'
      },
      {
        value: 3,
        label: '备案失败'
      }
      ],
      statusRules: {
        status: [{
          required: true,
          message: '请选择审核状态',
          trigger: 'blur'
        }],
        failureReason: [{
          validator: (rule, value, callback) => {
            if (
              this.userStatus.status === 2 &&
                this.userStatus.failureReason === ''
            ) {
              callback(new Error('请填写审核原因'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }]
      },
      recordStatusRules: {
        status: [{
          required: true,
          message: '请选择审核状态',
          trigger: 'blur'
        }],
        failureReason: [{
          validator: (rule, value, callback) => {
            if (
              this.userRecordStatus.status === 3 &&
                this.userRecordStatus.failureReason === ''
            ) {
              callback(new Error('请填写审核原因'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }]
      },
      paramsComment: {},
      commemttotal: 0,
      consultCommentList: [],
      paramsComplaint: {},
      complainttotal: 0,
      consultComplaintList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      dialogSetPreview: false, //编辑预览
      status: '',
      previewCurrent: 0,
      previewArr: [],
      previewNum: '',
      previewType: 0,
      imgHeight: '',
      pharmacy: {
        listQuery: {
          pageNo: 1,
          pageSize: 10,
          orderByField: 'id',
          orderBy: 'desc'
        },
        list: null,
        total: 0
      },
      search: '',
      tcmFlag: 0,
      merchantOptions: [],
      wrapperElem: null,
      num: null
    }
  },
  methods: {
    resetTemp() {
      this.$nextTick(() => {
        this.doctor = {
          baseInfo: {},
          consultList: [],
          licences: {}
        }
        this.headUrl = ''
      })
    },
    checkSwitch() {
      return this.switch
    },
    handleDoctorDetailsInfo() {
      this.resetTemp()
      this.pharmacyList()
      get(this.doctorId).then((response) => {
        this.doctor = response
        this.headUrl = response.baseInfo.headUrl
        this.tcmFlag = response.baseInfo.tcmFlag
      })

      this.paramsComment.doctorId = this.doctorId
      this.paramsComplaint.doctorId = this.doctorId

      this.getCommentPage()
      this.getComplaintPage()
      this.dialogInfoVisible = true
    },
    getCommentPage() {
      getCommentList(this.paramsComment).then((response) => {
        console.log(response)
        this.consultCommentList = response.list
        this.commemttotal = response.totalCount
      })
    },
    getComplaintPage() {
      getComplaintList(this.paramsComplaint).then((response) => {
        console.log(response)
        this.consultComplaintList = response.list
        this.complainttotal = response.totalCount
      })
    },
    openTabs(tab) {
      this.handleDoctorDetailsInfo()
      this.activeTabs = tab
    },
    handleStatus() {
      this.dialogStatusInfoVisible = true
      this.$nextTick(() => {
        this.$refs['userStatusDataForm'].clearValidate()
        this.userStatus = {
          failureReason: '',
          doctorId: this.doctorId
        }
      })
    },
    handleStatusData() {
      console.log(this.userStatus, 1051)
      if (
        this.userStatus.status === 2 &&
          (!this.doctor.baseInfo.medical.number ||
            !this.doctor.licences.idCard.number ||
            !this.doctor.licences.qualificationCertificate.number)
      ) {
        this.$alert(
          '请完善医生相关证件编号后再通过审核，包括：执业证编号、资格证编号', {
            confirmButtonText: '确定',
            callback: (action) => {
              this.dialogStatusInfoVisible = false
            }
          }
        )
        return
      }
      if (
        this.userStatus.status === 3 &&
          this.userStatus.failureReason === ''
      ) {
        this.$message({
          type: 'warning',
          message: '请填写审核原因～'
        })
        return
      }
      this.$refs['userStatusDataForm'].validate((valid) => {
        if (valid) {
          this.$confirm('此操作将审核用户状态, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            console.log(this.userStatus)
            authAudit(this.userStatus).then(() => {
              this.dialogStatusInfoVisible = false
              this.handleDoctorDetailsInfo()
              this.$message({
                type: 'success',
                message: '操作成功!'
              })
            })
          })
        }
      })
    },
    handleRecordStatus() {
      this.dialogRecordStatusInfoVisible = true
      this.$nextTick(() => {
        this.$refs['userRecordStatusDataForm'].clearValidate()
        this.userRecordStatus = {
          failureReason: '',
          doctorId: this.doctorId
        }
      })
    },
    // 备案通过
    handleRecordStatusData(status, doctorId) {
      if (
        this.userRecordStatus.status === 3 &&
          this.userRecordStatus.failureReason === ''
      ) {
        this.$message({
          type: 'warning',
          message: '请填写审核原因～'
        })
        return
      }
      console.log(this.userRecordStatus, 1118)
      this.$confirm('此操作将审核用户状态, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        recordAudit(this.userRecordStatus).then(() => {
          this.dialogRecordStatusInfoVisible = false
          this.handleDoctorDetailsInfo()
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
        })
      })
    },
    handleCloseDetails() {
      if (this.handleFilter) {
        this.handleFilter()
      }
    },
    tabClick(tab) {
      const that = this
      that.search = ''
      if (tab.index === '5') {
        that.pharmacy.listQuery.pageNo = 1
        that.pharmacyList()
      }
    },
    pharmacyList() {
      pharmacyList(this.doctorId, this.pharmacy.listQuery).then(response => {
        this.pharmacy.list = response.list
        this.pharmacy.total = response.totalCount
      })
    },
    delPharmacy(id) {
      const that = this
      this.$confirm('删除药店, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delPharmacy(id, this.doctorId).then(function() {
          that.pharmacyList()
        })
      }).catch(() => {
      })
    },
    querySearch(queryString, cb) {
      const params = {}
      params.doctorId = this.doctorId
      params.name = queryString
      querySearchKey(params).then(function(res) {
        console.log(res)
        const results = res
        cb(results)
      })
    },
    handleSelect(item) {
      const that = this
      this.$confirm('添加药店, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        addPharmacy(item.id, this.doctorId).then(function() {
          that.pharmacyList()
        })
      }).catch(() => {
        this.search = ''
      })
    },
    saveConfig() {
      const self = this
      const query = `?doctorId=${this.doctorId}&tcmFlag=${this.tcmFlag}`
      setConfig(query).then(function() {
        self.$message({
          message: '操作成功',
          type: 'success'
        })
      })
    },
    handleDownload(imgsrc) {
      Helper.downloadIamge(imgsrc)
    },
    setDoctorInfo() {
      this.$refs['setDocinfo'].initData()
    },
    onSetDatainfo() {
      this.resetTemp()
      get(this.doctorId).then((response) => {
        this.doctor = response
        this.headUrl = response.baseInfo.headUrl
      })
    },
    handlePreview(file) {
      console.log(file)
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    previewPic(imgRef) {
      this.$refs[imgRef].showViewer = true
    },
    previewPicArr(imgRef) {
      this.$refs[imgRef][0].showViewer = true
    },
    handleSetPreview(ImageArr, type) {
      this.previewArr = ImageArr
      this.previewType = type
      switch (type) {
        case 1:
          this.previewNum = this.doctor.licences.idCard.number
          break
        case 2:
          this.previewNum = this.doctor.baseInfo.medical.number
          break
        case 3:
          this.previewNum =
              this.doctor.licences.qualificationCertificate.number
          break
      }
      this.dialogSetPreview = true
    },
    handleRecordDefeated(doctorId) {
      this.status = status
      this.dialogRecordStatusInfoVisible = true
      this.$nextTick(() => {
        this.$refs['userRecordStatusDataForm'].clearValidate()
        this.userRecordStatus = {
          failureReason: '',
          doctorId: doctorId
        }
      })
    },
    handleOCR(url, type) {
      const param = {
        fileUrl: this.previewArr[this.previewCurrent],
        type: this.previewType
      }
      const loadingInstance = Loading.service({
        fullscreen: true
      })
      getImageOCR(param)
        .then(
          (response) => {
            if (response) {
              console.log(response)
              this.$message({
                message: '识别成功',
                type: 'success'
              })
              switch (this.previewType) {
                case 1:
                  this.previewNum = response
                  this.doctor.licences.idCard.number = response
                  break
                case 2:
                  this.previewNum = response
                  this.doctor.baseInfo.medical.number = response
                  break
                case 3:
                  this.previewNum = response
                  this.doctor.licences.qualificationCertificate.number = response
                  break
              }
            } else {
              this.$message({
                message: '识别失败',
                type: 'error'
              })
            }

          },
          (error) => {}
        )
        .finally(function() {
          loadingInstance.close()
        })
    },
    handleSave() {
      if (!this.previewNum) {
        this.$message({
          message: '请输入证件号码',
          type: 'warning'
        })
        return
      }
      const params = {
        doctorId: this.doctorId,
        number: this.previewNum,
        type: this.previewType
      }
      saveNumber(params).then(
        (response) => {
          console.log(response)
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.dialogSetPreview = false
          this.handleDoctorDetailsInfo()
        },
        (error) => {}
      )
    },
    handlecarouselChange() {
      this.previewCurrent = this.$refs.carousel.activeIndex
    },
    imgLoad() {
      this.imgHeight = this.$refs.imgRef[0].height
    },
    // 完善备案信息
    handleRecord(id) {
      // eslint-disable-next-line eqeqeq
      if (this.doctor.baseInfo.status == 0) {
        this.$alert('医生资质未提交，暂时不能备案!', '提示', {
          confirmButtonText: '确定'
        })
        return
      }
      if (!this.doctor.baseInfo.medical.number) {
        this.$alert('请填写完善医师执业证书编号！', '提示', {
          confirmButtonText: '确定',
          callback: action => {
            document.getElementById('medical').scrollIntoView({
              behavior: 'smooth'
            })
          }
        })
        return
      }
      if (!this.doctor.licences.idCard.number) {
        this.$alert('请填写完善医师身份证号！', '提示', {
          confirmButtonText: '确定',
          callback: action => {
            document.getElementById('idCard').scrollIntoView({
              behavior: 'smooth'
            })
          }
        })
        return
      }
      if (!this.doctor.licences.qualificationCertificate.number) {
        this.$alert('请填写完善医师资格证书编号！', '提示', {
          confirmButtonText: '确定',
          callback: action => {
            document.getElementById('qualificationCertificate').scrollIntoView({
              behavior: 'smooth'
            })
          }
        })
        return
      }
      this.dialogInfoVisible = false
      this.$router.push({
        path: './recordDr/' + id
      })
    },
    // el-image 增加保存按钮
    clickImage(idx) {
      this.num = idx
      this.$nextTick(() => {
        const wrapper = document.getElementsByClassName('el-image-viewer__actions__inner')
        const downImg = document.createElement('i')
        downImg.setAttribute('class', 'el-icon-check')
        wrapper[0].appendChild(downImg)
        if (wrapper.length > 0) {
          this.wrapperElem = wrapper[0]
          this.cusClickHandler()
        }
      })
    },
    // 给按钮添加点击事件
    cusClickHandler() {
      this.wrapperElem.addEventListener('click', this.hideCusBtn)
    },
    // 点击事件触发
    hideCusBtn(e) {
      const className = e.target.className
      // 旋转照片
      if (className === 'el-icon-refresh-right' || 'el-icon-refresh-left') {
        var imageElements = document.getElementsByClassName('el-image-viewer__img')
        if (imageElements.length > 0) {
          const transformStyle = imageElements[0].style.transform
          const matchResult = /rotate\((-?\d+)deg\)/.exec(transformStyle)
          console.log(matchResult, 'matchResult')
          if (matchResult) {
            var degree = parseInt(matchResult[1], 10) // 直接获取旋转角度数值
          }
        }
      }
      // 确认修改
      if (className === 'el-icon-check') {
        const params = new FormData()
        params.append('degree', degree)
        rotate(params, this.doctorId).then(resp => {
          this.$message.success('签名修改成功')
          this.handleDoctorDetailsInfo()
        }).catch(err => {
          console.log(err)
        })
        // 隐藏遮罩
        const close = document.querySelector('.el-icon-circle-close')
        close.click()
      }
    }
  }
}

</script>
