<template>
  <div>
    <el-dialog title="患者详情" :visible.sync="dialogInfoVisible" width="80%" top="2vh">
      <el-tabs style="min-height: 300px;" type="card">
        <el-tab-pane label="基本信息">
          <el-form ref="dataForm" :model="patient" label-position="right" label-width="100px">
            <!-- 所属业务员 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所属业务员:" prop="salesmanName">
                  <div style="display: flex;">{{ patient.baseInfo.salesmanName || '-' }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="ID" prop="id">
                  <el-input v-model="patient.baseInfo.id" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="姓名" prop="name">
                  <el-input v-model="patient.baseInfo.name" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="头像" prop="headUrl">
                  <el-image :src="headUrl">
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="昵称" prop="nickName">
                  <el-input v-model="patient.baseInfo.nickName" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="城市" prop="cityId">
                  <el-input v-model="patient.baseInfo.cityName" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="手机" prop="type">
                  <el-input v-model="patient.baseInfo.phone" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="性别" prop="gender">
                  <el-input v-model="patient.baseInfo.genderDescribe" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <!--  <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="详细地址">
                  <el-input v-model="patient.baseInfo.address" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
           <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="紧急联系人姓名">
                  <el-input v-model="patient.baseInfo.contactName" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="紧急联系人电话">
                  <el-input v-model="patient.baseInfo.contactPhone" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="上级医生ID" prop="recomDoctor">
                  <el-input v-model="patient.baseInfo.recomDoctor" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="上级医生姓名" prop="recomDoctorName">
                  <el-input v-model="patient.baseInfo.recomDoctorName" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row> -->
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="AI 自测">
          <div class="ai-test-container">
            <!-- 左侧：自测记录列表 -->
            <div class="ai-test-list">
              <div class="list-header">
                <h4>自测记录</h4>
              </div>
              <div
                v-for="(item, index) in aiTestList"
                :key="item.id"
                class="test-item"
                :class="{ 'active': selectedTestIndex === index }"
                @click="selectTest(index)"
              >
                <div class="test-title">{{ item.title }}</div>
                <div class="test-time">{{ item.createdAt }}</div>
                <div class="view-btn">
                  <el-button type="text" size="small">查看</el-button>
                </div>
              </div>
              <div v-if="aiTestList.length === 0" class="empty-state">
                <i class="el-icon-document"></i>
                <p>暂无自测记录</p>
              </div>
            </div>

            <!-- 右侧：自测详情 -->
            <div class="ai-test-detail">
              <div v-if="selectedTest" class="detail-content">
                <div class="detail-header">
                  <h4>{{ selectedTest.title }}</h4>
                  <span class="close-btn" @click="closeDetail">×</span>
                </div>
                <div class="detail-time">{{ selectedTest.createdAt }}</div>
                <div class="detail-body">
                  <div class="result-summary">
                    <h5>自测结果</h5>
                    <p>{{ selectedTest.result || '后台提供问题内容' }}</p>
                  </div>
                  <div class="test-content">
                    <h5>用户填写的详情况</h5>
                    <div class="content-text">
                      {{ selectedTest.content || 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar sic tempor. Sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Nam fermentum, nulla luctus pharetra vulputate, felis tellus mollis orci, sed rhoncus pronin sapien nunc accuan eget.' }}
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="empty-detail">
                <i class="el-icon-info"></i>
                <p>请选择左侧自测记录查看详情</p>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="关注医生">
          <el-table :data="patient.focusOnDoctors" fit style="width: 100%;">
            <el-table-column label="ID" fixed prop="id" width="90px" align="center" />
            <el-table-column label="姓名" prop="name" width="100px" align="center" />
            <el-table-column label="头像" prop="headUrl" width="80px" align="center">
              <template slot-scope="{ row }">
                <el-image :src="row.headUrl">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="类型" prop="typeDescribe" width="80px" align="center" />
            <el-table-column label="手机" prop="phone" width="110px" align="center" />
            <el-table-column label="执业医院" prop="hospitalName" align="center" />
            <el-table-column label="科室" prop="departmentName" width="130px" align="center" />
            <el-table-column label="医院所在地" prop="hospitalCityName" width="105px" align="center" />
            <el-table-column label="账户状态" prop="accountStatusDescribe" width="80px" align="center" />
            <el-table-column label="认证状态" prop="statusDescribe" align="center" width="110px" />
            <el-table-column label="备案状态" prop="recordStatusDescribe" align="center" width="100px" />
            <el-table-column label="注册时间" prop="createdAt" width="140px" align="center" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="关注咨询师">
          <el-table :data="tableData" style="width: 100%;">
            <el-table-column prop="id" label="ID" align="center"></el-table-column>
            <el-table-column prop="workNumber" label="工号" align="center"></el-table-column>
            <el-table-column prop="name" label="姓名" align="center"></el-table-column>
            <el-table-column prop="positionName" label="岗位" align="center"></el-table-column>
            <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
            <el-table-column prop="bindTime" label="关注时间" align="center"></el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="历史订单">
          <el-table :data="orderList" highlight-current-row fit style="width: 100%;">
            <el-table-column label="订单号" prop="orderSn" width="180px" align="center" />
            <el-table-column label="商品总价" prop="totalAmount" width="80px" align="center" />
            <el-table-column label="邮费" prop="freight" width="80px" align="center" />
            <el-table-column label="优惠券" prop="couponPay" width="80px" align="center" />
            <el-table-column label="实付" prop="realPay" width="80px" align="center" />
            <el-table-column label="订单状态" prop="orderStatusDescribe" width="80px" align="center" />
            <el-table-column label="支付状态" prop="payStatusDescribe" width="80px" align="center" />
            <el-table-column label="订单时间" prop="createdAt" width="140px" align="center" />
            <el-table-column label="支付时间" prop="payTime" width="140px" align="center" />
            <el-table-column label="发货时间" prop="sendTime" width="140px" align="center" />
          </el-table>
          <pagination v-show="total > 0" :total="orderTotal" :page.sync="orderListQuery.pageNo" :limit.sync="orderListQuery.pageSize" @pagination="getOrderList" />
        </el-tab-pane>
        <el-tab-pane label="就诊人成员">
          <el-table :data="inquirerData" highlight-current-row fit style="width: 100%;">
            <el-table-column label="姓名" width="120px" prop="name" align="center" />
            <el-table-column label="证件号码" width="160px" prop="idCard" align="center" />
            <el-table-column label="与患者关系" prop="relationName" align="center" />
            <el-table-column label="性别" prop="gender" align="center">
              <template slot-scope="{ row }">
                {{ genderStr[row.gender] }}
              </template>
            </el-table-column>
            <!-- <el-table-column label="图片" prop="gender" align="center">
              <template slot-scope="{row}">
                <span v-if="row.birthImage" style="color:#1890ff;" @click="showImage(row.birthImage)">查看</span>
              </template>
            </el-table-column> -->
            <el-table-column label="年龄" width="100px" prop="age" align="center">
              <template slot-scope="{ row }">
                {{ !row.ageYear ? '' : row.age }}
              </template>
            </el-table-column>
            <el-table-column label="现居住地址" prop="address" width="180px" align="center" />
            <el-table-column label="紧急联系人" width="120px" prop="contactName" align="center" />
            <el-table-column label="紧急联系人手机号" width="120px" prop="contactPhone" align="center" />
            <el-table-column label="婚姻状况" width="80px" align="center">
              <template slot-scope="{ row }">
                <el-tag v-if="row.maritalStatus === 1">已婚</el-tag>
                <el-tag v-else-if="row.maritalStatus === 0">未婚</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="监护人姓名" width="120px" prop="guardianName" align="center" />
            <el-table-column label="监护人身份证" width="160px" prop="guardianIdCard" align="center" />
            <el-table-column label="监护人手机号" width="120px" prop="guardianPhone" align="center" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="咨询记录">
          <el-table :data="consultSessionList" highlight-current-row fit style="width: 100%;">
            <el-table-column align="center" label="医生ID" prop="doctorId" />
            <el-table-column align="center" label="医生名称" prop="doctorName" />
            <el-table-column align="center" label="就诊人姓名" prop="inquirerName" />
            <el-table-column align="center" label="与陪诊人关系" width="100px" prop="inquirerRelationName" />
            <el-table-column align="center" label="会话ID" prop="sessionId">
              <template slot-scope="{ row }">
                <el-button v-if="row.sessionStatus == 3" type="text" @click="openChatPdf(row)">{{ row.sessionId }}</el-button>
                <span v-else>{{ row.sessionId }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="开始时间" width="155px" prop="startTime" />
            <el-table-column align="center" label="结束时间" width="155px" prop="endTime" />
            <el-table-column align="center" label="金额" width="80px" prop="cost" />
            <el-table-column align="center" label="会话关系" prop="relationDescribe" />
          </el-table>
          <pagination v-show="total > 0" :total="consultTotal" :page.sync="consultSessionQuery.pageNo" :limit.sync="consultSessionQuery.pageSize" @pagination="getConsultSessionList" />
        </el-tab-pane>
        <el-tab-pane label="病历档案">
          <div>
            <el-select v-model="listQuery.inquirerId" clearable placeholder="请选择" @change="handleFilter">
              <el-option v-for="item in inquirerData" :key="item.inquirerId" :label="item.name" :value="item.inquirerId"> </el-option>
            </el-select>
          </div>
          <el-table :data="caseList" highlight-current-row fit style="width: 100%;">
            <el-table-column align="center" label="医生姓名" width="100px" prop="doctorName" />
            <el-table-column align="center" label="就诊医院" prop="hospitalName" />
            <el-table-column align="center" label="就诊科室" width="100px" prop="departmentName" />
            <el-table-column align="center" label="时间" width="155px" prop="finishTimeDate" />
            <el-table-column label="操作" prop="createdAt" width="300px" align="center">
              <template slot-scope="scope">
                <el-button size="mini" @click="viewRecords(scope.row.id, 1, scope.$index)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total > 0" :total="total" :page.sync="listQuery.pageNo" :limit.sync="listQuery.pageSize" @pagination="getCaseList" />
        </el-tab-pane>
        <el-tab-pane label="诊前问卷">
          <el-table :data="formList" highlight-current-row fit style="width: 100%;">
            <el-table-column align="center" label="问卷名称" width="" prop="formName" />
            <el-table-column align="center" label="发起时间" width="200px" prop="createdAt" />
            <el-table-column align="center" label="提交时间" width="200px" prop="submitAt" />
            <el-table-column align="center" label="咨询师姓名" width="200px" prop="counselorName" />
            <el-table-column label="操作" prop="createdAt" width="300px" align="center">
              <template slot-scope="scope">
                <el-button size="mini" @click="handleFollow(scope.row)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="formLTotal > 0" :total="formLTotal" :page.sync="formLQuery.pageNo" :limit.sync="formLQuery.pageSize" @pagination="getFormListFun" />
        </el-tab-pane>
        <el-tab-pane label="服务包">
          <div class="service-package-container">
            <!-- 左侧：服务包列表 -->
            <div class="service-package-list">
              <div class="list-header">
                <h4>服务包列表</h4>
              </div>
              <div
                v-for="(item, index) in servicePackageList"
                :key="item.id"
                class="package-item"
                :class="{ 'active': selectedPackageIndex === index }"
                @click="selectPackage(index)"
              >
                <div class="package-info">
                  <div class="package-name">{{ item.name }}</div>
                  <div class="package-details">
                    <div class="detail-row">
                      <span class="label">购买时间：</span>
                      <span class="value">{{ item.purchaseTime }}</span>
                    </div>
                    <div class="detail-row">
                      <span class="label">有效期：</span>
                      <span class="value">{{ item.validDays }} 天</span>
                    </div>
                    <div class="detail-row">
                      <span class="label">起始日期：</span>
                      <span class="value">{{ item.startDate }} 至 {{ item.endDate }}</span>
                    </div>
                    <div class="detail-row">
                      <span class="label">状态：</span>
                      <span class="value" :class="getStatusClass(item.status)">{{ getStatusText(item.status) }}</span>
                    </div>
                  </div>
                </div>
                <div class="package-actions">
                  <el-button type="text" size="small" @click.stop="viewPackageDetail(item)">查看</el-button>
                  <el-button
                    v-if="item.status === 'active'"
                    type="text"
                    size="small"
                    @click.stop="editPackageTodos(item)"
                  >编辑</el-button>
                </div>
              </div>
              <div v-if="servicePackageList.length === 0" class="empty-state">
                <i class="el-icon-box"></i>
                <p>暂无服务包</p>
              </div>
            </div>

            <!-- 右侧：服务包详情 -->
            <div class="service-package-detail">
              <div v-if="selectedPackage" class="mobile-container">
                <div class="mobile-frame">
                  <div class="mobile-content">
                    <!-- PC端实现的服务包详情 -->
                    <ServicePackageDetail
                      :package-data="selectedPackage"
                      :editable="selectedPackage.status === 'active'"
                      @edit-todo="editPackageTodos"
                    />
                  </div>
                </div>
              </div>
              <div v-else class="empty-detail">
                <i class="el-icon-info"></i>
                <p>请选择左侧服务包查看详情</p>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 待办事项管理弹窗 -->
    <TodoItemsManager
      :visible="todoManagerVisible"
      :days="selectedPackage ? selectedPackage.validDays : 0"
      :value="todoItemsData"
      @close="closeTodoManager"
      @save="saveTodoItems"
    />
    <el-link v-waves type="primary" :underline="false" @click="handlePatientDetailsInfo">{{ patientName || patientNickName }}</el-link>
    <el-dialog title="图片" :visible.sync="dialogVisible">
      <div class="dialog-content">
        <el-image :src="imgUrl" fit="fit"></el-image>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog title="操作日志" :visible.sync="pdfDialogVisible" class="pdf-dialog">
      <div class="dialog-content">
        <iframe ref="pdfIframe" :src="pdfsrc" width="100%" height="800px" style="border: 0;"></iframe>
        <!-- <pdf class="pdfbox" :src="pdfsrc"></pdf> -->
        <h2>操作记录</h2>
        <el-table :data="caseLogList" highlight-current-row fit style="width: 100%;">
          <el-table-column label="ID" fixed prop="id" width="90px" align="center" />
          <el-table-column align="center" label="用户操作">
            <template slot-scope="{ row }">
              {{ types[row.type] }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作人" width="180px" prop="operator" />
          <el-table-column align="center" label="操作时间" width="155px" prop="createdAt" />
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="pdfDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog id="iframeDialog" :close-on-click-modal="false" title="预览" :visible.sync="dialogPreForm" width="400" top="5px">
      <div id="completeDiv">
        <prevform :forms="forms" :is-disabled="isDisabled" :formtitle="formtitle" :describe="describe" :form-data="formsData" />
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  get,
  getCaseList,
  getInquirerList,
  getCaseLogList,
  setCaseLogList,
  getFormList,
  getFollowDetail,
  getCounselorsDetail,
  getOrderList,
  getConsultSessionList,
  getSessionPdf,
  getAiTestList
} from '@/api/user/patient'
import waves from '@/directive/waves' // Waves directive
import Prevform from '@/components/form_preview/prevform'
import TodoItemsManager from '@/views/servicePackage/components/TodoItemsManager'
import ServicePackageDetail from './ServicePackageDetail'
export default {
  name: 'PatientDetails',

  components: {
    Prevform,
    TodoItemsManager,
    ServicePackageDetail
  },

  directives: { waves },

  props: {
    patientId: {
      type: [String, Number],
      required: false,
      default: ''
    },
    patientName: {
      type: String,
      required: false,
      default: ''
    },
    patientNickName: {
      type: String,
      required: false,
      default: ''
    }
  },

  data() {
    return {
      dialogInfoVisible: false,
      dialogPreForm: false,
      tableData: [],
      patient: {
        baseInfo: {},
        focusOnDoctors: [],
        orderList: []
      },
      headUrl: '',
      caseList: [],
      total: 0,
      inquirerData: [],
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      genderStr: ['女', '男', '未知'],
      dialogVisible: false,
      imgUrl: '',
      pdfsrc: '',
      pdfDialogVisible: false,
      caseLogList: [],
      caseLogTotal: 0,
      caseLogListQuery: {
        pageNo: 1,
        pageSize: 10
      },
      types: ['', '下载', '打印'],
      recomId: 0,
      formList: [],
      isDisabled: 'see',
      forms: [],
      isTitle: null,
      formtitle: '',
      describe: '',
      formsData: {},
      orderListQuery: {
        pageNo: 1,
        pageSize: 10
      },
      orderList: [],
      orderTotal: 0,
      consultSessionQuery: {
        pageNo: 1,
        pageSize: 10
      },
      consultSessionList: [],
      consultTotal: 0,
      formLTotal: 0,
      formLQuery: {
        pageNo: 1,
        pageSize: 10
      },
      // AI自测相关数据
      aiTestList: [],
      selectedTestIndex: -1,
      selectedTest: null,
      aiTestQuery: {
        pageNo: 1,
        pageSize: 10
      },
      aiTestTotal: 0,
      // 服务包相关数据
      servicePackageList: [],
      selectedPackageIndex: -1,
      selectedPackage: null,
      servicePackageQuery: {
        pageNo: 1,
        pageSize: 10
      },
      servicePackageTotal: 0,
      todoManagerVisible: false,
      todoItemsData: {}
    }
  },

  mounted() {
    // 监听来自H5的消息
    window.addEventListener('message', this.handleH5Message)
  },

  beforeDestroy() {
    // 移除消息监听
    window.removeEventListener('message', this.handleH5Message)
  },

  methods: {
    handleFollow({ counselorFollowId }) {
      getFollowDetail(counselorFollowId).then((res) => {
        this.formtitle = res.formName
        this.forms = res.designForm.formDrawing
        this.formsData = res.designForm.formData[res.designForm.lists[0].dataId]
        this.dialogPreForm = true
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        this.patient = {
          baseInfo: {},
          focusOnDoctors: [],
          consultList: [],
          orderList: []
        }
        this.tableData = []
        this.headUrl = ''
      })
    },
    handlePatientDetailsInfo() {
      this.resetTemp()
      this.dialogInfoVisible = true
      get(this.patientId).then((response) => {
        console.log('response::>', response)
        this.patient = response
        this.headUrl = response.baseInfo.headUrl
      })
      // 获取AI自测数据
      this.getAiTestList()
      // 获取服务包数据
      this.getServicePackageList()
      this.getCaseList()
      this.getInquirerLists()
      this.getFormListFun()
      this.getCounselorsList()
      this.getOrderList() // 患者订单列表
      this.getConsultSessionList() // 患者咨询会话列表
    },
    getOrderList() {
      this.orderListQuery.patientId = this.patientId
      getOrderList(this.orderListQuery).then((response) => {
        this.orderList = response.list
        this.orderTotal = response.totalCount
      })
    },
    getConsultSessionList() {
      this.consultSessionQuery.patientId = this.patientId
      getConsultSessionList(this.consultSessionQuery).then((response) => {
        this.consultSessionList = response.list
        this.consultTotal = response.totalCount
      })
    },
    getCounselorsList() {
      getCounselorsDetail(this.patientId).then((response) => {
        this.tableData = response
      })
    },
    getFormListFun() {
      this.listQuery.patientId = this.patientId
      getFormList(this.listQuery).then((res) => {
        console.log(res)
        this.formList = res.list
        this.formLTotal = res.totalCount
      })
    },
    getCaseList() {
      this.listQuery.patientId = this.patientId
      getCaseList(this.listQuery).then((response) => {
        this.caseList = response.result
        this.total = response.totalCount
      })
    },
    getInquirerLists() {
      // this.listQuery.patientId = this.patientId
      getInquirerList({ patientId: this.patientId }).then((response) => {
        console.log(response)
        this.inquirerData = response
      })
    },
    handleFilter() {
      console.log('handleFilter')
      this.getCaseList()
    },
    showImage(url) {
      this.imgUrl = url
      this.dialogVisible = true
    },
    getCaseLogList() {
      getCaseLogList(this.recomId, this.caseLogListQuery).then((response) => {
        if (response.pdfUrl === '') {
          this.$message({
            message: '没有病历档案！',
            type: 'error'
          })
          return
        }
        this.caseLogList = response.operatorList.list
        this.caseLogTotal = response.totalCount
        this.pdfDialogVisible = true
        //response.pdfUrl
        this.pdfsrc = '../../plugin/pdf/web/viewer.html?file=' + encodeURIComponent(response.pdfUrl)
        this.$nextTick(() => {
          const iframe = this.$refs.pdfIframe
          iframe.onload = () => {
            const secondaryPrint = iframe.contentWindow.document.querySelector('#secondaryPrint')
            secondaryPrint.addEventListener('click', () => {
              console.log('===secondaryPrint===')
              this.setCaseLogList(2)
            })
            const secondaryDownload = iframe.contentWindow.document.querySelector('#secondaryDownload')
            secondaryDownload.addEventListener('click', () => {
              console.log('===secondaryDownload===')
              this.setCaseLogList(1)
            })
            const print = iframe.contentWindow.document.querySelector('#print')
            print.addEventListener('click', () => {
              console.log('===print===')
              this.setCaseLogList(2)
            })
            const download = iframe.contentWindow.document.querySelector('#download')
            download.addEventListener('click', () => {
              console.log('===download===')
              this.setCaseLogList(1)
            })
            console.log('onload')
          }
          console.log(iframe)
        })
      })
    },
    viewRecords(id) {
      this.recomId = id
      this.getCaseLogList()
    },
    setCaseLogList(type) {
      const params = {}
      params.recordId = this.recomId
      params.type = type
      setCaseLogList(params)
    },
    // 查看聊天记录pdf
    openChatPdf(row) {
      getSessionPdf({ sessionId: row.sessionId }).then(data => {
        if (data) {
          window.open(data)
        }
      })
    },
    // AI自测相关方法
    // 获取AI自测列表
    async getAiTestList() {
      try {
        const params = {
          patientId: this.patientId,
          ...this.aiTestQuery
        }
        const response = await getAiTestList(params)
        this.aiTestList = response.list || []
        this.aiTestTotal = response.totalCount || 0
      } catch (error) {
        console.error('获取AI自测列表失败:', error)
        // 如果API失败，使用模拟数据
        this.aiTestList = [
          {
            id: 1,
            title: 'AI 自测 3',
            createdAt: '2025-06-26 01:21:06',
            result: 'AI自测的结果',
            content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar sic tempor. Sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Nam fermentum, nulla luctus pharetra vulputate, felis tellus mollis orci, sed rhoncus pronin sapien nunc accuan eget.'
          },
          {
            id: 2,
            title: 'AI 自测 2',
            createdAt: '2025-06-26 01:21:06',
            result: 'AI自测的结果',
            content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar sic tempor. Sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Nam fermentum, nulla luctus pharetra vulputate, felis tellus mollis orci, sed rhoncus pronin sapien nunc accuan eget.'
          },
          {
            id: 3,
            title: 'AI 自测 1',
            createdAt: '2025-06-26 01:21:06',
            result: 'AI自测的结果',
            content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin sodales pulvinar sic tempor. Sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Nam fermentum, nulla luctus pharetra vulputate, felis tellus mollis orci, sed rhoncus pronin sapien nunc accuan eget.'
          }
        ]
      }
    },
    // 选择自测记录
    selectTest(index) {
      this.selectedTestIndex = index
      this.selectedTest = this.aiTestList[index]
    },
    // 关闭详情
    closeDetail() {
      this.selectedTestIndex = -1
      this.selectedTest = null
    },

    // 服务包相关方法
    // 获取服务包列表
    async getServicePackageList() {
      try {
        // TODO: 实际API调用
        // const params = {
        //   patientId: this.patientId,
        //   ...this.servicePackageQuery
        // }
        // const response = await getServicePackageList(params)
        // this.servicePackageList = response.list || []
        // this.servicePackageTotal = response.totalCount || 0

        // 模拟数据
        this.servicePackageList = [
          {
            id: 1,
            name: '骨关节服务包',
            purchaseTime: '2025-06-25 22:57:55',
            validDays: 15,
            startDate: '2025-06-25',
            endDate: '2025-07-10',
            status: 'active' // active: 进行中, expired: 已过期, completed: 已结束
          },
          {
            id: 2,
            name: '骨关节服务包',
            purchaseTime: '2025-06-25 22:57:55',
            validDays: 15,
            startDate: '2025-06-25',
            endDate: '2025-07-10',
            status: 'expired'
          }
        ]
      } catch (error) {
        console.error('获取服务包列表失败:', error)
        this.servicePackageList = []
      }
    },

    // 选择服务包
    selectPackage(index) {
      this.selectedPackageIndex = index
      this.selectedPackage = this.servicePackageList[index]
    },

    // 查看服务包详情
    viewPackageDetail(packageItem) {
      const index = this.servicePackageList.findIndex(item => item.id === packageItem.id)
      this.selectPackage(index)
    },

    // 编辑服务包待办事项
    editPackageTodos(packageItem) {
      this.selectedPackage = packageItem
      this.todoItemsData = packageItem.todoItems || {}
      this.todoManagerVisible = true
    },

    // 关闭待办事项管理器
    closeTodoManager() {
      this.todoManagerVisible = false
    },

    // 保存待办事项
    async saveTodoItems(todoData) {
      try {
        // TODO: 调用API保存待办事项
        console.log('保存待办事项:', todoData)

        this.todoManagerVisible = false
        // 刷新服务包列表
        this.getServicePackageList()

        this.$message.success('保存成功')
      } catch (error) {
        console.error('保存待办事项失败:', error)
        this.$message.error('保存失败')
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        active: '进行中',
        expired: '已过期',
        completed: '已结束'
      }
      return statusMap[status] || '未知'
    },

    // 获取状态样式类
    getStatusClass(status) {
      return {
        'status-active': status === 'active',
        'status-expired': status === 'expired',
        'status-completed': status === 'completed'
      }
    }
  }
}
</script>

<style scoped>
.el-dialog {
  text-align: left;
}
.pdf-dialog .dialog-content {
  height: 100%;
}

/* AI自测样式 */
.ai-test-container {
  display: flex;
  height: 500px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.ai-test-list {
  width: 300px;
  border-right: 1px solid #e4e7ed;
  background-color: #fafafa;
}

.list-header {
  padding: 15px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f5f7fa;
}

.list-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  text-align: left;
}

.test-item {
  padding: 15px;
  border-bottom: 1px solid #e4e7ed;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
}

.test-item:hover {
  background-color: #ecf5ff;
}

.test-item.active {
  background-color: #409eff;
  color: white;
}

.test-item.active .test-title,
.test-item.active .test-time {
  color: white;
}

.test-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 5px;
}

.test-time {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.view-btn {
  text-align: right;
}

.view-btn .el-button {
  color: #409eff;
  font-size: 12px;
}

.test-item.active .view-btn .el-button {
  color: white;
}

.empty-state {
  text-align: center;
  padding: 50px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}

.ai-test-detail {
  flex: 1;
  background-color: white;
}

.detail-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.close-btn {
  font-size: 20px;
  color: #909399;
  cursor: pointer;
  line-height: 1;
}

.close-btn:hover {
  color: #409eff;
}

.detail-time {
  padding: 10px 20px;
  font-size: 12px;
  color: #909399;
  border-bottom: 1px solid #e4e7ed;
}

.detail-body {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.result-summary {
  margin-bottom: 30px;
}

.result-summary h5,
.test-content h5 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.result-summary p {
  margin: 0;
  padding: 15px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  color: #409eff;
  font-size: 14px;
  line-height: 1.6;
}

.content-text {
  font-size: 14px;
  line-height: 1.8;
  color: #606266;
  text-align: justify;
}

.empty-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
}

.empty-detail i {
  font-size: 48px;
  margin-bottom: 15px;
}

.empty-detail p {
  margin: 0;
  font-size: 14px;
}

/* 服务包样式 */
.service-package-container {
  display: flex;
  min-height: 600px;
  max-height: 80vh;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.service-package-list {
  width: 350px;
  border-right: 1px solid #e4e7ed;
  background-color: #fafafa;
  overflow-y: auto;
}

.package-item {
  padding: 15px;
  border-bottom: 1px solid #e4e7ed;
  cursor: pointer;
  transition: background-color 0.3s;
  position: relative;
}

.package-item:hover {
  background-color: #ecf5ff;
}

.package-item.active {
  background-color: #409eff;
  color: white;
}

.package-item.active .package-name,
.package-item.active .label,
.package-item.active .value {
  color: white;
}

.package-info {
  margin-bottom: 10px;
}

.package-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 10px;
  text-align: left;
}

.package-details {
  font-size: 12px;
}

.detail-row {
  display: flex;
  margin-bottom: 5px;
  align-items: center;
}

.detail-row .label {
  color: #909399;
  min-width: 70px;
  flex-shrink: 0;
  text-align: left;
}

.detail-row .value {
  color: #606266;
  flex: 1;
  text-align: left;
}

.status-active {
  color: #67c23a !important;
  font-weight: 500;
}

.status-expired {
  color: #f56c6c !important;
  font-weight: 500;
}

.status-completed {
  color: #909399 !important;
  font-weight: 500;
}

.package-actions {
  text-align: right;
}

.package-actions .el-button {
  color: #409eff;
  font-size: 12px;
  margin-left: 5px;
}

.package-item.active .package-actions .el-button {
  color: white;
}

.service-package-detail {
  flex: 1;
  background-color: #f5f5f5;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  min-height: 600px;
}

.mobile-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.mobile-frame {
  width: 375px;
  height: 680px;
  background: white;
  border-radius: 25px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  position: relative;
  border: 2px solid #ddd;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

.mobile-content {
  width: 100%;
  flex: 1;
  background: white;
  border-radius: 10px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

.pc-mode {
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 8px;
}
</style>
