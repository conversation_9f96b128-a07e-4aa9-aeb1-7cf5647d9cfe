<template>
  <div class="course-management">
    <!-- 顶部患者信息 -->
    <div class="patient-header">
      <div class="patient-info">
        <div class="patient-title">
          <div class="patient-icon-wrap">
            <img
              src="https://patient-gjws.crow5.com/images/gjws/<EMAIL>"
              class="patient-icon"
            />
            <span class="patient-name">骨关节3个月全面治疗管理服务包</span>
          </div>
        </div>
        <div class="patient-details">
          <div class="detail-item">
            <span class="label">起止日期：2025.05.15-2026.12.29</span>
          </div>
          <div class="detail-item">
            <span class="label">有效期：3个月</span>
            <div class="patient-status"><span></span>进行中</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 问诊环节 -->
    <div class="consultation-section">
      <div class="section-title">问诊权益</div>
      <div class="consultation-items">
        <div class="consultation-item">
          <div class="item-content">
            <img src="https://patient-gjws.crow5.com/images/gjws/<EMAIL>" class="item-icon" />
            <div class="item-info">
              <div class="flex" style="align-items: center;">
                <div class="item-name">图文问诊</div>
              </div>
              <div class="item-count">
                剩余<span class="count-num">{{ textConsultationCount }}</span
                >次
              </div>
              <div class="item-count">共8次</div>
            </div>
          </div>
        </div>
        <div class="consultation-item">
          <div class="item-content">
            <img src="https://patient-gjws.crow5.com/images/gjws/<EMAIL>" class="item-icon" />
            <div class="item-info">
              <div class="flex" style="align-items: center;">
                <div class="item-name">视频问诊</div>
              </div>
              <div class="item-count">
                剩余<span class="count-num">{{ videoConsultationCount }}</span
                >次
              </div>
              <div class="item-count">共8次</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务完成进度 -->
    <div class="progress-section">
      <div class="progress-header">
        <span class="progress-title">任务完成进度</span>
      </div>
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: '60%' }"></div>
      </div>
      <span class="progress-percent">60%</span>
    </div>

    <!-- 日历部分 -->
    <div class="calendar-section">
      <div class="calendar-header" @click="showDatePicker = true">
        <span class="calendar-title">{{ currentYear }}.{{ String(currentMonth).padStart(2, '0') }}</span>
        <img
          src="https://patient-gjws.crow5.com/images/gjws/<EMAIL>"
          class="calendar-icon"
        />
      </div>

      <!-- 日历网格 - 始终显示星期行 -->
      <div class="calendar-grid">
        <div class="weekdays">
          <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
        </div>

        <!-- 日历内容区域 - 添加动画 -->
        <div class="calendar-content">
          <!-- 当前周显示 -->
          <transition name="calendar-slide" mode="out-in">
            <div v-if="!showCalendar" key="week" class="dates current-week">
              <div
                v-for="date in currentWeekDates"
                :key="date.date || date.index"
                :class="[
                  'date-item',
                  {
                    current: date.isCurrent,
                    selected: date.isSelected,
                    'has-record': date.hasRecord,
                    empty: date.isEmpty,
                    'status-completed': date.completionStatus === 'completed',
                    'status-partial': date.completionStatus === 'partial',
                    'status-none': date.completionStatus === 'none'
                  }
                ]"
                @click="selectDate(date)"
              >
                <span v-if="!date.isEmpty">{{ date.day }}</span>
                <div v-if="date.hasRecord && !date.isEmpty" class="record-dot"></div>
              </div>
            </div>

            <!-- 完整月份显示 -->
            <div v-else key="month" class="dates full-month">
              <div
                v-for="date in calendarDates"
                :key="date.date || date.index"
                :class="[
                  'date-item',
                  {
                    current: date.isCurrent,
                    selected: date.isSelected,
                    'has-record': date.hasRecord,
                    empty: date.isEmpty,
                    'status-completed': date.completionStatus === 'completed',
                    'status-partial': date.completionStatus === 'partial',
                    'status-none': date.completionStatus === 'none'
                  }
                ]"
                @click="selectDate(date)"
              >
                <span v-if="!date.isEmpty">{{ date.day }}</span>
                <div v-if="date.hasRecord && !date.isEmpty" class="record-dot"></div>
              </div>
            </div>
          </transition>
        </div>
      </div>

      <!-- 展开收起按钮 -->
      <div class="calendar-toggle-container">
        <div class="calendar-toggle-btn" @click="toggleCalendar">
          <img
            :src="
              showCalendar
                ? 'https://patient-gjws.crow5.com/images/gjws/<EMAIL>'
                : 'https://patient-gjws.crow5.com/images/gjws/<EMAIL>'
            "
            class="calendar-toggle-icon"
          />
        </div>
      </div>

      <!-- 当日待办任务 -->
      <div v-if="todoList.length > 0" class="todo-section">
        <div class="todo-title">
          当日待办任务：
        </div>

        <div class="todo-items">
          <div v-for="todo in todoList" :key="todo.id" class="todo-item">
            <div class="todo-header">
              <img
                :src="
                  todo.completed
                    ? 'https://patient-gjws.crow5.com/images/gjws/<EMAIL>'
                    : 'https://patient-gjws.crow5.com/images/gjws/<EMAIL>'
                "
                class="checkbox-icon readonly"
              />
              <span :class="['todo-text', { completed: todo.completed }]">
                {{ todo.text }}
              </span>
            </div>

            <!-- 时间标签 -->
            <div v-if="todo.times && todo.times.length" class="time-tags">
              <div v-for="timeItem in todo.times" :key="timeItem.time || timeItem" class="time-tag-box">
                <img
                  class="checkbox-icon readonly"
                  :src="
                    (timeItem.completed !== undefined ? timeItem.completed : todo.completed)
                      ? 'https://patient-gjws.crow5.com/images/gjws/<EMAIL>'
                      : 'https://patient-gjws.crow5.com/images/gjws/<EMAIL>'
                  "
                />
                <span class="time-tag" :class="{ disabled: !canOperateTodo() }">
                  {{ timeItem.time || timeItem }}
                </span>
              </div>
            </div>

            <!-- 输入框 -->
            <div v-if="todo.type === 'input'" class="input-section">
              <van-field
                v-model="todo.inputValue"
                placeholder="请输入内容"
                type="textarea"
                rows="3"
                class="todo-input"
                :disabled="true"
                readonly
              />
            </div>

            <!-- 上传图片 -->
            <div v-if="todo.type === 'upload'" class="upload-section">
              <!-- 已上传的图片 -->
              <div v-if="todo.uploadedImages && todo.uploadedImages.length > 0" class="uploaded-images">
                <div v-for="(image, index) in todo.uploadedImages" :key="index" class="uploaded-image">
                  <img :src="image" class="image-preview" />

                </div>
              </div>
              <!-- 上传按钮 - 只读状态 -->
              <div
                v-if="(!todo.uploadedImages || todo.uploadedImages.length === 0)"
                class="upload-placeholder disabled"
              >
                <img
                  src="https://patient-gjws.crow5.com/images/gjws/<EMAIL>"
                  class="upload-icon"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 说明简介 -->
    <div class="description-section">
      <div class="description-title">说明简介</div>
      <div class="description-content" v-html="descriptionContent"></div>
    </div>

    <!-- 日期选择器 -->
    <van-popup v-model="showDatePicker" round position="bottom" class="service-picker-popup">
      <van-datetime-picker
        v-model="selectedDate"
        type="year-month"
        title="选择日期"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'CourseManagement',
  data() {
    return {
      showCalendar: false, // 默认收起
      showDatePicker: false,
      currentYear: 2025,
      currentMonth: 7,
      selectedDate: new Date(2025, 6, 1), // 2025年7月
      weekdays: ['日', '一', '二', '三', '四', '五', '六'], // 调整为日开始
      calendarDates: [],
      currentWeekDates: [],
      selectedDateStr: '', // 当前选中的日期字符串
      currentDate: new Date(), // 当前日期
      // 问诊次数
      textConsultationCount: 3, // 图文问诊剩余次数
      videoConsultationCount: 3, // 视频问诊剩余次数
      // 所有日期的待办事项数据
      allTodoData: {
        '2025-07-22': [
          {
            id: 101,
            text: '早晨服用降压药（氨氯地平）',
            type: 'normal',
            completed: true,
            times: [
              { time: '8:00', completed: true },
              { time: '20:00', completed: false }
            ],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 102,
            text: '测量血压并记录',
            type: 'normal',
            completed: false,
            times: [
              { time: '9:00', completed: false },
              { time: '21:00', completed: false }
            ],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 103,
            text: '记录今日饮食情况',
            type: 'input',
            completed: true,
            times: [],
            inputValue: '早餐：燕麦粥、鸡蛋、牛奶；午餐：糙米饭、清蒸鱼、青菜；晚餐：小米粥、蒸蛋羹',
            uploadedImages: []
          },
          {
            id: 104,
            text: '上传血压测量仪读数照片',
            type: 'upload',
            completed: false,
            times: [],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 105,
            text: '进行30分钟有氧运动',
            type: 'normal',
            completed: true,
            times: [{ time: '17:00', completed: true }],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 106,
            text: '记录今日身体状况和症状',
            type: 'input',
            completed: false,
            times: [],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 107,
            text: '睡前服用阿司匹林',
            type: 'normal',
            completed: false,
            times: [{ time: '22:00', completed: false }],
            inputValue: '',
            uploadedImages: []
          }
        ],
        '2025-07-21': [
          {
            id: 101,
            text: '早晨服用降压药（氨氯地平）',
            type: 'normal',
            completed: true,
            times: [
              { time: '8:00', completed: true },
              { time: '20:00', completed: false }
            ],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 102,
            text: '测量血压并记录',
            type: 'normal',
            completed: false,
            times: [
              { time: '9:00', completed: false },
              { time: '21:00', completed: false }
            ],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 103,
            text: '记录今日饮食情况',
            type: 'input',
            completed: true,
            times: [],
            inputValue: '早餐：燕麦粥、鸡蛋、牛奶；午餐：糙米饭、清蒸鱼、青菜；晚餐：小米粥、蒸蛋羹',
            uploadedImages: []
          },
          {
            id: 104,
            text: '上传血压测量仪读数照片',
            type: 'upload',
            completed: false,
            times: [],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 105,
            text: '进行30分钟有氧运动',
            type: 'normal',
            completed: true,
            times: [{ time: '17:00', completed: true }],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 106,
            text: '记录今日身体状况和症状',
            type: 'input',
            completed: false,
            times: [],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 107,
            text: '睡前服用阿司匹林',
            type: 'normal',
            completed: false,
            times: [{ time: '22:00', completed: false }],
            inputValue: '',
            uploadedImages: []
          }
        ],
        '2025-07-18': [
          {
            id: 21,
            text: '早晨服用降压药',
            type: 'normal',
            completed: false,
            times: [
              { time: '8:00', completed: false },
              { time: '20:00', completed: false }
            ],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 22,
            text: '记录今日身体状况和症状',
            type: 'input',
            completed: false,
            times: [],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 23,
            text: '上传血压测量照片',
            type: 'upload',
            completed: false,
            times: [],
            inputValue: '',
            uploadedImages: []
          }
        ],
        '2025-07-01': [
          {
            id: 1,
            text: '早上服药一次',
            type: 'normal',
            completed: false,
            times: [],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 2,
            text: '记录一下当前疼痛情况',
            type: 'input',
            completed: false,
            times: [],
            inputValue: '',
            uploadedImages: []
          }
        ],
        '2025-07-02': [
          {
            id: 3,
            text: '高血压用药建议请查看并评分',
            type: 'normal',
            completed: false,
            times: [
              { time: '8:00', completed: false },
              { time: '12:00', completed: false }
            ],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 4,
            text: '上传今日血压测量结果',
            type: 'upload',
            completed: false,
            times: [],
            inputValue: '',
            uploadedImages: []
          }
        ],
        '2025-07-03': [
          {
            id: 5,
            text: '早上服药一次',
            type: 'normal',
            completed: true,
            times: [],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 6,
            text: '记录疼痛情况',
            type: 'input',
            completed: true,
            times: [],
            inputValue: '今天疼痛有所缓解',
            uploadedImages: []
          }
        ],
        '2025-07-04': [
          {
            id: 7,
            text: '早上服药一次',
            type: 'normal',
            completed: true,
            times: [],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 8,
            text: '上传血压测量结果',
            type: 'upload',
            completed: false,
            times: [],
            inputValue: '',
            uploadedImages: []
          }
        ],
        '2025-07-05': [
          {
            id: 9,
            text: '早上服药一次',
            type: 'normal',
            completed: true,
            times: [],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 10,
            text: '上传血压测量结果',
            type: 'upload',
            completed: true,
            times: [],
            inputValue: '',
            uploadedImages: ['/images/gjws/<EMAIL>']
          }
        ],
        // 8月份的待办任务数据
        '2025-08-01': [
          {
            id: 201,
            text: '早晨服用降压药',
            type: 'normal',
            completed: true,
            times: [
              { time: '8:00', completed: true },
              { time: '20:00', completed: true }
            ],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 202,
            text: '记录血压数据',
            type: 'input',
            completed: false,
            times: [],
            inputValue: '',
            uploadedImages: []
          }
        ],
        '2025-08-02': [
          {
            id: 203,
            text: '服用药物',
            type: 'normal',
            completed: false,
            times: [
              { time: '8:00', completed: false },
              { time: '20:00', completed: false }
            ],
            inputValue: '',
            uploadedImages: []
          }
        ],
        '2025-08-03': [
          {
            id: 204,
            text: '康复训练',
            type: 'normal',
            completed: true,
            times: [],
            inputValue: '',
            uploadedImages: []
          },
          {
            id: 205,
            text: '上传训练照片',
            type: 'upload',
            completed: true,
            times: [],
            inputValue: '',
            uploadedImages: ['/images/gjws/<EMAIL>']
          }
        ]
      },
      todoList: [], // 当前选中日期的待办事项
      descriptionContent: `
        <div style="text-align: center; margin-bottom: 16px;">
          <img src="https://patient-gjws.crow5.com/images/gjws/<EMAIL>" style="width: 60px; height: 60px; border-radius: 4px;" />
        </div>
        <p><strong>大学附属医院</strong></p>
        <p><strong>骨关节科主任医师</strong></p>
        <p>大学附属医院骨关节科主任医师，医学博士，教授</p>
        <p>从事骨关节外科临床工作20余年，具有丰富的临床经验</p>
        <p><strong>擅长：</strong>膝关节镜手术、人工关节置换术、关节镜下半月板修复术、前交叉韧带重建术等</p>
        <p>发表SCI论文30余篇，主持国家自然科学基金项目2项，获得省部级科技进步奖3项</p>
        <p>担任中华医学会骨科学分会关节外科学组委员，中国医师协会骨科医师分会关节外科专业委员会委员等学术职务</p>
      `
    }
  },
  mounted() {
    this.generateCalendar()
    this.generateCurrentWeek()
    this.initializeSelectedDate()
  },
  methods: {
    toggleCalendar() {
      this.showCalendar = !this.showCalendar
    },
    generateCalendar() {
      const dates = []
      const year = this.currentYear
      const month = this.currentMonth
      const today = new Date()
      const currentDay = today.getDate()
      const currentMonth = today.getMonth() + 1
      const currentYear = today.getFullYear()

      // 获取当月第一天是星期几（0=周日，1=周一...）
      const firstDay = new Date(year, month - 1, 1).getDay()

      // 获取当月天数
      const daysInMonth = new Date(year, month, 0).getDate()

      // 添加空白日期 - 以周日为一周的第一天
      for (let i = 0; i < firstDay; i++) {
        dates.push({ isEmpty: true, index: i })
      }

      // 添加当月日期
      for (let i = 1; i <= daysInMonth; i++) {
        const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(i).padStart(2, '0')}`
        const completionStatus = this.getDateCompletionStatus(dateStr)

        dates.push({
          date: dateStr,
          day: i,
          isCurrent: i === currentDay && month === currentMonth && year === currentYear,
          isSelected: dateStr === this.selectedDateStr,
          hasRecord: this.allTodoData[dateStr] && this.allTodoData[dateStr].length > 0,
          completionStatus: completionStatus, // 'completed', 'partial', 'none'
          isEmpty: false
        })
      }

      this.calendarDates = dates
    },
    generateCurrentWeek() {
      const today = new Date()
      const currentDay = today.getDay() // 0=周日, 1=周一...
      const startOfWeek = new Date(today)
      startOfWeek.setDate(today.getDate() - currentDay) // 获取本周周日

      const weekDates = []
      for (let i = 0; i < 7; i++) {
        const date = new Date(startOfWeek)
        date.setDate(startOfWeek.getDate() + i)

        const day = date.getDate()
        const month = date.getMonth() + 1
        const year = date.getFullYear()

        const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
        const completionStatus = this.getDateCompletionStatus(dateStr)

        weekDates.push({
          date: dateStr,
          day: day, // 始终显示日期数字，不管是否是当前选择的月份
          isCurrent:
            day === today.getDate() && month === today.getMonth() + 1 && year === today.getFullYear(),
          isSelected: dateStr === this.selectedDateStr,
          hasRecord: this.allTodoData[dateStr] && this.allTodoData[dateStr].length > 0,
          completionStatus: completionStatus,
          isEmpty: false // 当前周的日期都不为空
        })
      }

      this.currentWeekDates = weekDates
    },
    selectDate(date) {
      if (date.isEmpty) return

      console.log('选中日期:', date.date)
      console.log('之前选中的日期:', this.selectedDateStr)

      // 更新选中的日期
      this.selectedDateStr = date.date

      // 更新日历显示
      this.updateCalendarSelection()

      // 加载该日期的待办事项
      this.loadTodoForDate(date.date)
    },
    updateCalendarSelection() {
      const today = new Date()
      const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(
        today.getDate()
      ).padStart(2, '0')}`

      // 更新完整月份日历的选中状态
      this.calendarDates.forEach((d) => {
        this.$set(d, 'isSelected', d.date === this.selectedDateStr)
        // 只有选中的是今天时，今天才保持current类；选中其他日期时，移除今天的current类
        if (d.date === todayStr) {
          this.$set(d, 'isCurrent', d.date === this.selectedDateStr)
        }
      })

      // 更新当前周日历的选中状态
      this.currentWeekDates.forEach((d) => {
        this.$set(d, 'isSelected', d.date === this.selectedDateStr)
        // 只有选中的是今天时，今天才保持current类；选中其他日期时，移除今天的current类
        if (d.date === todayStr) {
          this.$set(d, 'isCurrent', d.date === this.selectedDateStr)
        }
      })
    },
    loadTodoForDate(dateStr) {
      // 加载指定日期的待办事项
      this.todoList = this.allTodoData[dateStr] || []
    },
    initializeSelectedDate() {
      // 初始化选中今天的日期
      const today = new Date()
      const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(
        today.getDate()
      ).padStart(2, '0')}`
      this.selectedDateStr = todayStr
      this.loadTodoForDate(todayStr)
      // 不需要调用updateCalendarSelection，因为generateCalendar会处理选中状态
    },
    getDateCompletionStatus(dateStr) {
      const todos = this.allTodoData[dateStr]
      if (!todos || todos.length === 0) return 'none'

      let completedCount = 0
      const totalCount = todos.length

      todos.forEach((todo) => {
        if (this.isTodoCompleted(todo)) {
          completedCount++
        }
      })

      if (completedCount === totalCount) return 'completed'
      if (completedCount > 0) return 'partial'
      return 'none'
    },
    isTodoCompleted(todo) {
      // 检查单个待办事项是否完成
      if (todo.times && todo.times.length > 0) {
        // 有多个执行时间的事项，需要所有时间都完成
        return todo.times.every((timeItem) => timeItem.completed)
      }
      return todo.completed
    },
    toggleTodo(todo) {
      // 检查是否可以操作（只能操作当天的事项）
      if (!this.canOperateTodo()) {
        this.$toast('只能操作当天的待办事项')
        return
      }

      // 如果要标记为完成，需要验证
      if (!todo.completed) {
        if (!this.validateTodoCompletion(todo)) {
          return // 验证失败，不执行切换
        }
      }

      todo.completed = !todo.completed
      this.updateDateCompletionStatus()
    },
    toggleTodoTime(todo, timeItem) {
      // 检查是否可以操作
      if (!this.canOperateTodo()) {
        this.$toast('只能操作当天的待办事项')
        return
      }

      timeItem.completed = !timeItem.completed

      // 检查是否所有时间都完成了
      if (todo.times.every((t) => t.completed)) {
        todo.completed = true
      } else {
        todo.completed = false
      }

      this.updateDateCompletionStatus()
    },
    validateTodoCompletion(todo) {
      // 验证待办事项是否可以标记为完成
      if (todo.type === 'input') {
        if (!todo.inputValue || todo.inputValue.trim() === '') {
          this.$toast('请输入内容再勾选完成事项')
          return false
        }
      } else if (todo.type === 'upload') {
        if (!todo.uploadedImages || todo.uploadedImages.length === 0) {
          this.$toast('请先上传图片再勾选完成事项')
          return false
        }
      }
      return true
    },
    canOperateTodo() {
      // 在PC端患者详情中，待办任务只能查看，不能操作
      return false
    },
    updateDateCompletionStatus() {
      // 更新日历中日期的完成状态
      this.generateCalendar()
      this.generateCurrentWeek()
    },
    uploadImage(todo) {
      // 模拟上传图片
      if (!this.canOperateTodo()) {
        this.$toast('只能操作当天的待办事项')
        return
      }

      // 检查是否已经有图片，限制只能上传一张
      if (todo.uploadedImages && todo.uploadedImages.length > 0) {
        this.$toast('每个任务只能上传一张图片')
        return
      }

      // 这里应该调用实际的图片上传逻辑
      // 现在模拟添加一张图片
      const mockImageUrl = '/images/gjws/<EMAIL>'
      if (!todo.uploadedImages) {
        todo.uploadedImages = []
      }
      todo.uploadedImages.push(mockImageUrl)

      this.$toast('图片上传成功')
    },
    removeUploadedImage(todo, index) {
      if (!this.canOperateTodo()) {
        this.$toast('只能操作当天的待办事项')
        return
      }

      todo.uploadedImages.splice(index, 1)

      // 如果删除了所有图片且任务已完成，则取消完成状态
      if (todo.uploadedImages.length === 0 && todo.completed) {
        todo.completed = false
        this.updateDateCompletionStatus()
      }

      this.$toast('图片删除成功')
    },
    onDateConfirm(value) {
      this.selectedDate = value
      this.currentYear = value.getFullYear()
      this.currentMonth = value.getMonth() + 1
      this.generateCalendar()
      this.generateCurrentWeek()
      this.initializeSelectedDate() // 重新初始化选中日期
      this.showDatePicker = false
    },

    // 构建查询字符串
    buildQueryString(params) {
      return Object.keys(params)
        .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&')
    }
  }
}
</script>

<style lang="scss" scoped>
.course-management {
  height: 100%;
  background-color: #f8f8f8;
  overflow-y: auto;
}

// 顶部患者信息
.patient-header {
  display: flex;
  justify-content: space-between;
  background: linear-gradient(180deg, rgba(224, 240, 255, 0.99) 0%, rgba(226, 238, 251, 0) 100%);
}

.patient-info {
  flex: 1;
  padding: 14px;
}

.patient-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;

  .patient-icon-wrap {
    display: flex;
    align-items: center;
  }

  .patient-switch-status {
    display: flex;
    align-items: center;
    padding: 4px 7px;
    background: #367dff;
    border-radius: 4px;
    img {
      width: 14px;
      height: 14px;
    }
    span {
      font-weight: bold;
      font-size: 13px;
      color: #ffffff;
    }
  }

  .patient-icon {
    width: 22px;
    height: 22px;
  }

  .patient-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-left: 8px;
  }

  .patient-status {
    font-weight: 500;
    font-size: 13px;
    color: #367dff;
    span {
      display: inline-block;
      width: 12px;
      height: 12px;
      background: #367dff;
      border-radius: 50%;
      margin-right: 12px;
    }
  }
}

.patient-details {
  .detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    font-size: 13px;

    .patient-status {
      font-weight: 500;
      font-size: 13px;
      color: #367dff;
      span {
        display: inline-block;
        width: 12px;
        height: 12px;
        background: #367dff;
        border-radius: 50%;
        margin-right: 12px;
      }
    }

    .label {
      color: #666;
    }

    .value {
      color: #666;
    }
  }
}

.edit-btn {
  border-radius: 16px;
  padding: 6px 16px;
  font-size: 14px;
}

// 问诊环节
.consultation-section {
  background: white;
  border-radius: 8px;
  padding: 10px 14px;
  margin-bottom: 10px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  text-align: left;
}

.consultation-items {
  display: flex;
  gap: 12px;
}

.consultation-item {
  flex: 1;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.98);
    opacity: 0.8;
  }
}

.item-content {
  display: flex;
  align-items: center;

  .item-icon {
    width: 64px;
    height: 64px;
    margin-right: 8px;
  }
}

.item-info {
  .item-name {
    font-size: 14px;
    color: #333;
    font-weight: 500;
    white-space: nowrap;
    margin-right: 8px;
  }

  .item-count {
    font-size: 13px;
    color: #999;
    text-align: left;
    .count-num {
      color: #367dff;
      font-size: 16px;
      font-weight: 500;
      margin: 0 6px;
    }
  }
}

.arrow-icon {
  width: 16px;
  height: 16px;
}

// 任务完成进度
.progress-section {
  background: white;
  border-radius: 8px;
  padding: 10px 14px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .progress-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}

.progress-percent {
  font-size: 16px;
  font-weight: 600;
  color: #367dff;
}

.progress-bar {
  width: 190px;
  height: 14px;
  background: #eee;
  border-radius: 8px;
  overflow: hidden;

  .progress-fill {
    height: 100%;
    background: #367dff;
    border-radius: 8px;
    transition: width 0.3s ease;
  }
}

// 日历部分
.calendar-section {
  background: white;
  border-radius: 8px;
  padding: 10px 14px;
  margin-bottom: 10px;

  // 日历内容区域
  .calendar-content {
    overflow: hidden;
  }

  // 日历切换动画
  .calendar-slide-enter-active,
  .calendar-slide-leave-active {
    transition: all 0.3s ease-in-out;
  }

  .calendar-slide-enter-from {
    opacity: 0;
    transform: translateY(-10px);
  }

  .calendar-slide-leave-to {
    opacity: 0;
    transform: translateY(10px);
  }
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 15px;
  cursor: pointer;

  .calendar-icon {
    width: 20px;
    height: 20px;
  }

  .calendar-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}

.calendar-toggle-container {
  display: flex;
  justify-content: center;
  margin-top: 10px;

  .calendar-toggle-btn {
    width: 30px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    .calendar-toggle-icon {
      width: 100%;
      height: 100%;
    }
  }
}

.calendar-grid {
  .weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 4px;

    .weekday {
      text-align: center;
      font-size: 12px;
      color: #b4b4b4;
      padding: 8px 0;
    }
  }

  .dates {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 6px;

    &.current-week {
      margin-bottom: 0;
    }

    &.full-month {
      margin-top: 16px;
    }

    .date-item {
      position: relative;
      text-align: center;
      padding: 8px 0;
      font-size: 14px;
      font-weight: bold;
      color: #333;
      cursor: pointer;
      border-radius: 4px;
      min-height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &.empty {
        cursor: default;
      }

      // 选中状态 - 最高优先级
      &.selected {
        background: #367dff !important;
        color: white !important;
        font-weight: bold !important;
      }

      // 当前日期样式 - 只在未选中时生效
      &.current:not(.selected) {
        background: #367dff;
        color: white;
      }

      &:not(.empty):hover {
        background: #367dff;
      }

      &.has-record .record-dot {
        position: absolute;
        bottom: 4px;
        left: 50%;
        transform: translateX(-50%);
        width: 6px;
        height: 6px;
        background: #00aa60;
        border-radius: 50%;
      }

      // &.current:not(.selected).has-record .record-dot {
      //   background: white !important; // 当前日期未选中时的小圆点为白色
      // }

      // 选中状态的小圆点根据完成状态显示颜色
      &.selected.status-completed.has-record .record-dot {
        background-color: #00b876 !important; // 选中且全部完成 - 绿色
      }

      &.selected.status-partial.has-record .record-dot {
        background-color: #ff9b3a !important; // 选中且部分完成 - 橙色
      }

      &.selected.status-none.has-record .record-dot {
        background-color: #f05542 !important; // 选中且全部未完成 - 红色
      }

      // 完成状态的小圆点颜色
      &.status-completed.has-record .record-dot {
        background-color: #00b876 !important; // 全部完成 - 绿色
      }

      &.status-partial.has-record .record-dot {
        background-color: #ff9b3a !important; // 部分完成 - 橙色
      }

      &.status-none.has-record .record-dot {
        background-color: #f05542 !important; // 全部未完成 - 红色
      }

      // 选中状态始终保持蓝色背景
      &.selected {
        background-color: #367dff !important;
        color: white !important;
      }
    }
  }
}

// 待办任务
.todo-section {
  margin-top: 10px;
  padding: 0 3px;
}

.todo-title {
  font-size: 12px;
  color: #333;
  margin-bottom: 8px;
  text-align: left;

  .readonly-tip {
    font-size: 10px;
    color: #999;
    font-weight: normal;
  }
}

.todo-items {
  .todo-item {
    background: #f9fbff;
    border-radius: 8px;
    padding: 8px 10px;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.todo-header {
  display: flex;
  align-items: center;

  .checkbox-icon {
    width: 20px;
    height: 20px;
    margin-right: 7px;
    cursor: pointer;

    &.readonly {
      cursor: default;
      opacity: 0.7;
    }
  }

  .todo-text {
    font-size: 14px;
    color: #333;
    font-weight: bold;
    flex: 1;
    text-align: left;

    &.completed {
      color: #333;
      text-decoration: line-through;
    }
  }
}

.time-tags {
  margin-left: 34px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  .time-tag-box {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-right: 41px;
  }
  .checkbox-icon {
    width: 20px;
    height: 20px;
    margin-right: 7px;
    cursor: pointer;

    &.readonly {
      cursor: default;
      opacity: 0.7;
    }
  }

  .time-tag {
    color: #333;
    font-size: 14px;
  }
}

.input-section {
  margin-left: 34px;
  margin-top: 8px;
  margin-bottom: 16px;
  border-radius: 8px;
  border: 1px solid #eef3fc;

  .todo-input {
    border-radius: 8px;

    /deep/ .van-field__control {
      font-size: 14px;
      color: #666;
    }
  }
}

.upload-section {
  margin-left: 34px;
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;

  .upload-placeholder {
    width: 60px;
    height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    cursor: pointer;

    .upload-icon {
      width: 60px;
      height: 60px;
    }

    .upload-text {
      font-size: 10px;
      color: #999;
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .uploaded-images {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 8px;

    .uploaded-image {
      position: relative;
      width: 60px;
      height: 60px;
      margin-right: 8px;
      margin-bottom: 8px;
      border-radius: 8px;
      overflow: hidden;

      .image-preview {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .delete-btn {
        position: absolute;
        top: 2px;
        right: 2px;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
    }
  }
}

// 禁用状态
.todo-text.disabled {
  opacity: 0.5;
  color: #999;
}

// 说明简介
.description-section {
  background: white;
  border-radius: 8px;
  padding: 10px 14px;
}

.description-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  text-align: left;
}

.description-content {
  font-size: 14px;
  color: #666;
  line-height: 1.6;

  /deep/ p {
    margin: 8px 0;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  /deep/ strong {
    color: #333;
    font-weight: 600;
  }

  /deep/ img {
    max-width: 100%;
    height: auto;
    border-radius: 16px;
  }
}

// 移动端适配
@media (max-width: 375px) {
  .course-management {
    padding: 12px;
  }

  .calendar-grid .dates .date-item {
    min-height: 28px;
    font-size: 12px;
  }
}

/deep/ .van-picker__cancel {
  font-weight: 400;
  font-size: 14px;
  color: #666666;
}

/deep/ .van-picker__title {
  font-weight: 600;
  font-size: 16px;
  color: #333333;
}

/deep/ .van-picker__confirm {
  font-weight: 400;
  font-size: 14px;
  color: #367dff;
}
</style>
