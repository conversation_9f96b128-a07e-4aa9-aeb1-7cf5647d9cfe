<template>
  <el-scrollbar ref="scrollbar" style="height: 100%;">
    <div class="page bg-color-gray-light">
      <div class="main bg-color-gray-light">
        <div v-if="!finished && recordList.length" class="font_size_12 color_999 flex_c_m pdt_10 pdb_10" @click="getList">点击查看更多</div>
        <div v-for="item in recordList" :id="'chat_' + item.sendTimestamp" :key="item.id" class="flex mgb_20">
          <template v-if="item.sender != 2">
            <img
              v-if="item.sender == 0"
              :src="item.counselorAvatar ? item.counselorAvatar : require('../../../assets/images/default_avatar.png')"
              class="chat_avatar"
              alt=""
            />
            <img
              v-if="item.sender == 1"
              :src="item.patientAvatar ? item.patientAvatar : require('../../../assets/images/default_avatar.png')"
              class="chat_avatar"
              alt=""
            />
            <div class="chat_content mgl_10 flex1">
              <div class="chat_content_title">
                <span v-if="item.sender == 0" class="font_size_12 color_999">{{ item.counselorName }}</span>
                <span v-if="item.sender == 1" class="font_size_12 color_999">{{ item.patientName }}</span><span class="font_size_12 color_999 mgl_10">{{ item.sendTime }}</span>
              </div>
              <div v-if="item.sendType == 0" class="chat_content_msg" :class="item.sender == 0 ? 'sendtext' : 'receivetext'">
                {{ item.sendContent }}
              </div>
              <div v-if="item.sendType == 1" class="chat_content_img" @click="viewImg(0, item.sendContent, item.sendTimestamp)">
                <img :src="item.sendContent" class="" alt="" />
              </div>
              <div v-if="item.sendType == 2" class="chat_content_ques">
                <img v-if="item.fillingStatus" src="../../../assets/images/<EMAIL>" class="status" alt="" />
                <div class="chat_content_ques-head">
                  <img src="../../../assets/images/<EMAIL>" alt="" />
                  <div class="text">诊前问卷</div>
                </div>
                <div class="chat_content_ques_box">
                  <h3>{{ item.formName }}</h3>
                  <p>{{ item.sendContent }}</p>
                </div>
                <div class="chat_content_ques_foot">
                  查看详情
                </div>
              </div>
            </div>
          </template>
        </div>
        <div v-if="!recordList.length" class="no-data">暂无数据</div>
      </div>
    </div>
  </el-scrollbar>
</template>

<script>
import { ImagePreview } from 'vant'
import { getSessionDetail } from '@/api/user/counselor'
export default {
  name: 'Detail',
  props: {
    id: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      offset: 30,
      error: false,
      loading: false,
      finished: false,
      counselorId: '',
      preConsultId: '',
      detail: {},
      queryData: {
        pageNo: 1,
        pageSize: 1000,
        preConsultId: ''
      },
      recordList: [],
      list: [],
      previewList: [],
      scrollTop: 0,
      refDisabled: false
    }
  },
  computed: {},
  watch: {
    scrollTop(val) {
      if (val === 0) {
        this.refDisabled = false
      } else {
        this.refDisabled = true
      }
    }
  },
  created() {
    // this.counselorId = this.$route.params.counselorId
    // this.queryData.preConsultId = this.$route.params.id
    // this.getDetail()
    // this.getList()
  },
  methods: {
    initScroll() {
      const box = this.$refs.main
      box.addEventListener('scroll', () => {
        this.scrollTop = box.scrollTop
      })
    },
    goToScrollBottom() {
      var that = this
      setTimeout(() => {
        console.log('111111')
        that.$nextTick(() => {
          const scrollElem = that.$refs.chatBoxRef
          console.log(scrollElem, 155)
          scrollElem.scrollTo({
            top: scrollElem.scrollHeight,
            behavior: 'smooth'
          })
        })
      }, 500)
    },
    getList() {
      var _this = this
      if (!this.loading) {
        this.loading = true
        _this.queryData.preConsultId = this.id
        getSessionDetail(_this.queryData)
          .then((res) => {
            console.log(res, 'res')
            console.log('this.queryData', this.queryData)
            const result = res.list.reverse()
            if (res.list) {
              result.forEach((element) => {
                element.timeText = this.calcTimeHeader(element.sendTimestamp)
              })
              if (_this.queryData.pageNo === 1) {
                const scrollId = result[result.length - 1].sendTimestamp
                _this.recordList = result
                console.log('1', `chat_${scrollId}`)
                setTimeout(() => {
                  document.getElementById(`chat_${scrollId}`).scrollIntoView({ behavior: 'smooth' })
                }, 500)
              } else {
                const scrollId = result[result.length - 1].sendTimestamp
                _this.recordList = result.concat(_this.recordList)
                console.log('2', `chat_${scrollId}`)
              }
              _this.queryData.pageNo++
              if (!res.data.hasNext) {
                console.log('没有下一页')
                _this.finished = true
              }
              console.log(res, _this.queryData)
            } else {
              _this.error = true
              _this.$toast(res.msg)
            }
            _this.loading = false
            console.log('_this.recordList', _this.recordList)
          })
          .catch((e) => {
            _this.loading = false
            _this.error = true
            _this.finished = true
          })
          .finally(() => {})
      }
    },
    getDetail() {
      API.counselorInfo({
        counselorId: this.counselorId
      })
        .then((res) => {
          if (res.code === 0) {
            this.detail = res.data
          } else {
            this.$toast(res.msg)
          }
        })
        .catch((error) => {})
    },
    viewImg(index, image, time) {
      ImagePreview({
        loop: false,
        images: [image],
        startPosition: index,
        closeable: true,
        onClose() {
          setTimeout(() => {
            document.getElementById(`chat_${time}`).scrollIntoView({ behavior: 'smooth' })
          }, 500)
        }
      })
    },
    handleFollowDetail(counselorFollowId) {
      this.$router.push({
        path: '/counselorFollowDetail',
        query: {
          counselorFollowId: counselorFollowId
        }
      })
    },
    calcTimeHeader(time) {
      // 格式化传入时间
      const date = new Date(parseInt(time)),
        year = date.getUTCFullYear(),
        month = date.getUTCMonth(),
        day = date.getDate(),
        hour = date.getHours(),
        minute = date.getUTCMinutes()
      // 获取当前时间
      const currentDate = new Date(),
        currentYear = date.getUTCFullYear(),
        currentMonth = date.getUTCMonth(),
        currentDay = currentDate.getDate()
      // 计算是否是同一天
      if (currentYear == year && currentMonth == month && currentDay == day) {
        //同一天直接返回
        if (hour > 12) {
          return `下午 ${hour}:${minute < 10 ? '0' + minute : minute}`
        } else {
          return `上午 ${hour}:${minute < 10 ? '0' + minute : minute}`
        }
      }
      // 计算是否是昨天
      const yesterday = new Date(currentDate - 24 * 3600 * 1000)
      if (year == yesterday.getUTCFullYear() && month == yesterday.getUTCMonth() && day == yesterday.getDate()) {
        //昨天
        return `昨天 ${hour}:${minute < 10 ? '0' + minute : minute}`
      } else {
        return `${year}-${month + 1}-${day} ${hour}:${minute < 10 ? '0' + minute : minute}`
      }
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/ .el-scrollbar__wrap {
  overflow-x: hidden !important;
}
.page {
  height: 100%;
  width: 100%;
  display: block;
}
.main {
  min-height: 100%;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
  // padding-top: 120px;
  min-height: 100%;
}
.main_header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 9999;
}
.doctor_img {
  width: 67px;
  height: 67px;
  border-radius: 50%;
}
.chat_list {
  height: 100%;
  margin: 0 auto;
  padding: 30px;
  box-sizing: border-box;
  padding-top: 120px;
}
.chat_list_box {
  height: 100%;
  overflow: auto;
}
.chat_avatar {
  width: 46px;
  height: 46px;
  border-radius: 50%;
}
.chat_content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.chat_content_msg {
  // max-width: 80%;
  padding: 10px;
  border-radius: 0px 8px 8px 8px;
  margin-top: 5px;
  font-size: 14px;
  word-wrap: break-word;
  word-break: break-all;
  overflow: hidden;
}
.chat_content_img {
  width: 280px;
  height: 280px;
  margin-top: 5px;
}
.sendtext {
  background: #fff !important;
  color: #333 !important;
}
.receivetext {
  background: #2893ff !important;
  color: #fff !important;
}
.chat_content_img img {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}
.chat_content_ques {
  background: #fff;
  // width: 490px;
}
.chat_content_ques-head {
  // width: 490px;
  // height: 80px;
  position: relative;
}
.chat_content_ques-head img {
  display: block;
  width: 100%;
  height: 100%;
}
.chat_content_ques-head .text {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  font-size: 16px;
  font-weight: 500;
  color: #38bf87;
  line-height: 80px;
  padding-left: 20px;
}
.chat_content_ques_box {
  box-sizing: border-box;
  width: 100%;
  padding: 16px 20px 0px;
}
.chat_content_ques_box h3 {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin: 0!important;
  // line-height: 44px;
}
.chat_content_ques_box p {
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  // line-height: 40px;
  margin: 0!important;
  padding-top: 24px;
  width: 100%;
  box-sizing: border-box;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}
.chat_content_ques_foot {
  font-size: 16px;
  font-weight: 400;
  color: #38bf87;
  // line-height: 40px;
  padding-top: 10px;
  padding-bottom: 10px;
  text-align: center;
}
.chat_content_ques {
  position: relative;
}
.chat_content_ques .status {
  position: absolute;
  width: 128px;
  height: 104px;
  right: 0;
  top: 0;
}
.flex {
  display: flex;
}
.mgl_10 {
  margin-left: 10px;
}
.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
}
.mgb_20 {
  margin-bottom: 15px;
}
</style>
