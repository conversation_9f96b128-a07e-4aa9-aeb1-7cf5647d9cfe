<template>
  <div class="counselor-warp">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="咨询师管理" name="first">
        <CounselorManagement />
      </el-tab-pane>
      <el-tab-pane label="咨询会话记录" name="second">
        <CounselorRecord />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import CounselorManagement from './counselorManagement'
import CounselorRecord from './counselorRecord'
export default {
  name: 'Tabs',
  components: {
    CounselorManagement,
    CounselorRecord
  },
  data() {
    return {
      activeName: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event)
    }
  }
}
</script>

<style lang="scss" scoped>
.counselor-warp {
  margin: 20px;
}
</style>
