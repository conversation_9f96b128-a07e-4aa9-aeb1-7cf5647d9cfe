<template>
  <div class="app-container">
    <el-dialog
      append-to-body
      title="编辑信息"
      :visible.sync="setDocinfoVisible"
      width="80%"
      :before-close="handleClose"
    >
      <el-form
        ref="dataForm"
        :model="doctorData"
        :rules="rules"
        label-position="right"
        label-width="150px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item style="text-align: left;" label="ID:">
              <span>{{ doctorData.doctorId }}</span>
            </el-form-item>
            <el-form-item style="text-align: left;" label="手机号:">
              <!-- <span>{{ doctorData.phone }}</span> -->
              <Desensitization v-model="doctorData.phone" :data="doctorData" :type="'phone'" />
            </el-form-item>
            <el-form-item style="text-align: left;" label="姓名:" prop="name">
              <el-input v-model="doctorData.name" style="width:170px" clearable :readonly="baseStatus==2" />
            </el-form-item>

            <el-form-item style="text-align: left;" label="邮箱:">
              <!-- <el-input v-model="doctorData.email" style="width:170px" /> -->
              <Desensitization v-model="doctorData.email" :data="doctorData" :type="'email'" :view="'input'" :readonly="false" />
            </el-form-item>
            <el-form-item label="科室:" style="text-align: left;">
              <el-cascader
                v-model="doctorData.departmentId"
                :options="departmentData"
                :props="props"
                style="width:172px;"
                clearable
              />
            </el-form-item>
            <el-form-item label="职称:" style="text-align: left;">
              <DictSelect
                v-model="doctorData.titleId"
                placeholder="请选择"
                type="doctor-title"
              />
            </el-form-item>
            <el-form-item label="执业医院:" style="text-align: left;">
              <span>{{ hospitalName }}</span>&nbsp;<el-button
                type="primary"
                size="mini"
                plain
                @click="changeHost"
              >选择医院</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item style="margin-bottom: 0;" label="头像:">
              <div class="box">
                <input
                  id="boxtx1"
                  ref="boxtx1"
                  name="boxtx1"
                  class="imgfile"
                  type="file"
                  multiple="false"
                  accept="image/png, image/jpg,image/jpeg"
                  @change="handleFileChange($event, '1')"
                />
                <label for="boxtx1"></label>
                <div v-if="doctorData.headUrl" class="img">
                  <div style="width:100%;height:100%">
                    <!-- <img :src="doctorData.headUrl" /> -->
                    <el-image
                      ref="previewImg"
                      :src="doctorData.headUrl"
                      :preview-src-list="[doctorData.headUrl]"
                    >
                    </el-image>
                  </div>
                </div>
                <div v-if="doctorData.headUrl" class="downloadBox">
                  <img style="width:30px;height:30px" src="/static/images/download.png" alt="" @click.stop="handleDownload(doctorData.headUrl)">
                  <img style="width:30px;height:30px" src="/static/images/enlarge.png" alt="" @click.stop="previewPic('previewImg')">
                  <!-- <i style="font-size:25px" class="el-icon-zoom-in" @click.stop="handlePreview(doctorData.headUrl)"></i>
                  <i style="font-size:25px" class="el-icon-download" @click.stop="handleDownload(doctorData.headUrl)"></i> -->
                </div>

                <i v-else class="el-icon-circle-plus-outline"></i>
              </div>
              <div>只能上传jpg/jpeg/png/文件，且不超过100kb</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="擅长:">
              <el-input
                v-model="doctorData.expertise"
                type="textarea"
                width="50%"
                placeholder="擅长"
                maxlength="1000"
                show-word-limit
              ></el-input>
            </el-form-item>
            <el-form-item label="个人简介:">
              <el-input
                v-model="doctorData.introduction"
                type="textarea"
                width="50%"
                placeholder="请编辑个人简介"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
          </el-col>
          <el-col :span="16">
            <el-form-item label="医师工作证:">
              <div class="box" style="float:left;margin-right:10px;">
                <input
                  id="boxtx2"
                  ref="boxtx2"
                  name="boxtx2"
                  class="imgfile"
                  type="file"
                  multiple="false"
                  accept="image/png, image/gif, image/jpeg"
                  @change="handleFileChange($event, '2')"
                />
                <label for="boxtx2"></label>
                <div
                  v-if="
                    doctorData.emCard &&
                      doctorData.emCard.urls &&
                      doctorData.emCard.urls.length >= 1 &&
                      doctorData.emCard.urls[0]
                  "
                  class="img"
                >
                  <div>
                    <!-- <img :src="doctorData.emCard.urls[0]" /> -->
                    <el-image
                      ref="previewImgCard"
                      :src="doctorData.emCard.urls[0]"
                      :preview-src-list="[doctorData.emCard.urls[0]]"
                    >
                    </el-image>
                  </div>
                </div>
                <div
                  v-if="
                    doctorData.emCard &&
                      doctorData.emCard.urls &&
                      doctorData.emCard.urls.length >= 1 &&
                      doctorData.emCard.urls[0]
                  "
                  class="downloadBox"
                >
                  <img style="width:30px;height:30px" src="/static/images/download.png" alt="" @click.stop="handleDownload(doctorData.emCard.urls[0])">
                  <img style="width:30px;height:30px" src="/static/images/enlarge.png" alt="" @click.stop="previewPic('previewImgCard')">
                  <!-- <i style="font-size:25px" class="el-icon-zoom-in" @click.stop="handlePreview(doctorData.emCard.urls[0])"></i>
                  <i style="font-size:25px" class="el-icon-download" @click.stop="handleDownload(doctorData.emCard.urls[0])"></i> -->
                </div>
                <i v-else class="el-icon-circle-plus-outline"></i>
              </div>
            </el-form-item>
            <div style="margin-left: 150px;margin-bottom:20px">只能上传jpg/jpeg/png/文件，且不超过500kb</div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="执业证取得日期" prop="pracIssue">
              <el-date-picker
                v-model="doctorData.pracIssue"
                clearable
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker> （医生执业证书发证日期）
              <!-- <div style="color:red;white-space:nowrap">* 执业不满3年不允许备案视频复诊服务，请仔细核对医生执业信息后填写</div> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="医师执业证:">
              <el-input
                v-model="doctorData.medical.number"
                class="mboxinput"
                clearable
              />
              <div class="box" style="float:left;margin-right:10px;">
                <input
                  id="boxtx31"
                  ref="boxtx31"
                  name="boxtx31"
                  class="imgfile"
                  type="file"
                  multiple="false"
                  accept="image/png, image/gif, image/jpeg"
                  @change="handleFileChange($event, '3', '1')"
                />
                <label for="boxtx31"></label>
                <div
                  v-if="
                    doctorData.medical &&
                      doctorData.medical.urls &&
                      doctorData.medical.urls.length >= 1 &&
                      doctorData.medical.urls[0]
                  "
                  class="img"
                >
                  <div>
                    <!-- <img :src="doctorData.medical.urls[0]" /> -->
                    <el-image
                      ref="previewImgMed"
                      :src="doctorData.medical.urls[0]"
                      :preview-src-list="[doctorData.medical.urls[0]]"
                    >
                    </el-image>
                  </div>
                </div>
                <div
                  v-if="
                    doctorData.medical &&
                      doctorData.medical.urls &&
                      doctorData.medical.urls.length >= 1 &&
                      doctorData.medical.urls[0]
                  "
                  class="downloadBox"
                >
                  <!-- <i style="font-size:25px" class="el-icon-full-screen" @click.stop="handleOCR(doctorData.medical.urls[0],2)"></i>
                  <i style="font-size:25px" class="el-icon-zoom-in" @click.stop="handlePreview(doctorData.medical.urls[0])"></i>
                  <i style="font-size:25px" class="el-icon-download" @click.stop="handleDownload(doctorData.medical.urls[0])"></i> -->

                  <!-- <img style="width:30px;height:30px" src="/static/images/ocr.png" alt="" @click.stop="handleOCR(doctorData.medical.urls[0],2)"> -->
                  <img style="width:30px;height:30px" src="/static/images/download.png" alt="" @click.stop="handleDownload(doctorData.medical.urls[0])">
                  <img style="width:30px;height:30px" src="/static/images/enlarge.png" alt="" @click.stop="previewPic('previewImgMed')">

                </div>
                <i v-else class="el-icon-circle-plus-outline"></i>
              </div>
              <div class="box" style="float:left;margin-right:10px;">
                <input
                  id="boxtx32"
                  ref="boxtx32"
                  name="boxtx32"
                  class="imgfile"
                  type="file"
                  multiple="false"
                  accept="image/png, image/gif, image/jpeg"
                  @change="handleFileChange($event, '3', '2')"
                />
                <label for="boxtx32"></label>
                <div
                  v-if="
                    doctorData.medical &&
                      doctorData.medical.urls &&
                      doctorData.medical.urls.length == 2 &&
                      doctorData.medical.urls[1]
                  "
                  class="img"
                >
                  <div>
                    <!-- <img :src="doctorData.medical.urls[1]" /> -->
                    <el-image
                      ref="previewImgMed1"
                      :src="doctorData.medical.urls[1]"
                      :preview-src-list="[doctorData.medical.urls[1]]"
                    >
                    </el-image>
                  </div>
                </div>
                <div
                  v-if="
                    doctorData.medical &&
                      doctorData.medical.urls &&
                      doctorData.medical.urls.length == 2 &&
                      doctorData.medical.urls[1]
                  "
                  class="downloadBox"
                >
                  <!-- <i style="font-size:25px" class="el-icon-full-screen" @click.stop="handleOCR(doctorData.medical.urls[1],2)"></i>
                  <i style="font-size:25px" class="el-icon-zoom-in" @click.stop="handlePreview(doctorData.medical.urls[1])"></i>
                  <i style="font-size:25px" class="el-icon-download" @click.stop="handleDownload(doctorData.medical.urls[1])"></i> -->

                  <!-- <img style="width:30px;height:30px" src="/static/images/ocr.png" alt="" @click.stop="handleOCR(doctorData.medical.urls[1],2)"> -->
                  <img style="width:30px;height:30px" src="/static/images/download.png" alt="" @click.stop="handleDownload(doctorData.medical.urls[1])">
                  <img style="width:30px;height:30px" src="/static/images/enlarge.png" alt="" @click.stop="previewPic('previewImgMed1')">

                </div>
                <i v-else class="el-icon-circle-plus-outline"></i>
              </div>

            </el-form-item>
            <div style="margin-left: 150px;margin-bottom:20px">只能上传jpg/jpeg/png/文件，且不超过500kb</div>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item label="职称证书:">
            <div class="box">
              <input
                id="boxtx5"
                ref="boxtx5"
                name="boxtx5"
                class="imgfile"
                type="file"
                multiple="false"
                accept="image/png, image/gif, image/jpeg"
                @change="handleFileChange($event, '5')"
              />
              <label for="boxtx5"></label>
              <div
                v-if="
                  doctorData.titleCertificate &&
                    doctorData.titleCertificate.urls &&
                    doctorData.titleCertificate.urls.length >= 1 &&
                    doctorData.titleCertificate.urls[0]
                "
                class="img"
              >
                <div>
                  <!-- <img :src="doctorData.titleCertificate.urls[0]" /> -->
                  <el-image
                    ref="previewImgCertificate"
                    :src="doctorData.titleCertificate.urls[0]"
                    :preview-src-list="[doctorData.titleCertificate.urls[0]]"
                  >
                  </el-image>
                </div>
              </div>
              <div
                v-if="
                  doctorData.titleCertificate &&
                    doctorData.titleCertificate.urls &&
                    doctorData.titleCertificate.urls.length >= 1 &&
                    doctorData.titleCertificate.urls[0]
                "
                class="downloadBox"
              >
                <img style="width:30px;height:30px" src="/static/images/download.png" alt="" @click.stop="handleDownload(doctorData.titleCertificate.urls[0])">
                <img style="width:30px;height:30px" src="/static/images/enlarge.png" alt="" @click.stop="previewPic('previewImgCertificate')">

                <!-- <i style="font-size:25px" class="el-icon-zoom-in" @click.stop="handlePreview(doctorData.titleCertificate.urls[0])"></i>
                <i style="font-size:25px" class="el-icon-download" @click.stop="handleDownload(doctorData.titleCertificate.urls[0])"></i> -->
              </div>
              <i v-else class="el-icon-circle-plus-outline"></i>
            </div>
          </el-form-item>
          <div style="margin-left: 150px;margin-bottom:20px">只能上传jpg/jpeg/png/文件，且不超过500kb</div>
          <!-- </el-col> -->
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="身份证:" prop="cardNo">
              <el-input
                v-model="doctorData.idCard.number"
                :readonly="baseStatus==2"
                class="mboxinput"
                clearable
              />
              <div class="box" style="float:left;margin-right:10px;">
                <input
                  v-if="baseStatus!=2"
                  id="boxtx41"
                  ref="boxtx41"
                  name="boxtx41"
                  class="imgfile"
                  type="file"
                  multiple="false"
                  accept="image/png, image/gif, image/jpeg"
                  @change="handleFileChange($event, '4', '1')"
                />
                <label for="boxtx41"></label>
                <div
                  v-if="
                    doctorData.idCard &&
                      doctorData.idCard.urls &&
                      doctorData.idCard.urls.length >= 1 &&
                      doctorData.idCard.urls[0]
                  "
                  class="img"
                >
                  <div>
                    <!-- <img :src="doctorData.idCard.urls[0]" /> -->
                    <el-image
                      ref="previewImgidCard"
                      :src="doctorData.idCard.urls[0]"
                      :preview-src-list="[doctorData.idCard.urls[0]]"
                    >
                    </el-image>
                  </div>
                </div>
                <div
                  v-if="
                    doctorData.idCard &&
                      doctorData.idCard.urls &&
                      doctorData.idCard.urls.length >= 1 &&
                      doctorData.idCard.urls[0]
                  "
                  class="downloadBox"
                >

                  <!-- <img v-if="baseStatus!=2" style="width:30px;height:30px" src="/static/images/ocr.png" alt="" @click.stop="handleOCR(doctorData.idCard.urls[0],1)"> -->
                  <img style="width:30px;height:30px" src="/static/images/download.png" alt="" @click.stop="handleDownload(doctorData.idCard.urls[0])">
                  <img style="width:30px;height:30px" src="/static/images/enlarge.png" alt="" @click.stop="previewPic('previewImgidCard')">

                  <!-- <i style="font-size:25px" class="el-icon-full-screen" @click.stop="handleOCR(doctorData.idCard.urls[0],1)"></i>
                  <i style="font-size:25px" class="el-icon-zoom-in" @click.stop="handlePreview(doctorData.idCard.urls[0])"></i>
                  <i style="font-size:25px" class="el-icon-download" @click.stop="handleDownload(doctorData.idCard.urls[0])"></i> -->
                </div>
                <i v-else class="el-icon-circle-plus-outline"></i>
              </div>
              <div class="box" style="float:left;margin-right:10px;">
                <input
                  v-if="baseStatus!=2"
                  id="boxtx42"
                  ref="boxtx42"
                  name="boxtx42"
                  class="imgfile"
                  type="file"
                  multiple="false"
                  accept="image/png, image/gif, image/jpeg"
                  @change="handleFileChange($event, '4', '2')"
                />
                <label for="boxtx42"></label>
                <div
                  v-if="
                    doctorData.idCard &&
                      doctorData.idCard.urls &&
                      doctorData.idCard.urls.length == 2 &&
                      doctorData.idCard.urls[1]
                  "
                  class="img"
                >
                  <div>
                    <!-- <img :src="doctorData.idCard.urls[1]" /> -->
                    <el-image
                      ref="previewImgidCard1"
                      :src="doctorData.idCard.urls[1]"
                      :preview-src-list="[doctorData.idCard.urls[1]]"
                    >
                    </el-image>
                  </div>
                </div>
                <div
                  v-if="
                    doctorData.idCard &&
                      doctorData.idCard.urls &&
                      doctorData.idCard.urls.length == 2 &&
                      doctorData.idCard.urls[1]
                  "
                  class="downloadBox"
                >
                  <!-- <i style="font-size:25px" class="el-icon-full-screen" @click.stop="handleOCR(doctorData.idCard.urls[1],1)"></i>
                  <i style="font-size:25px" class="el-icon-zoom-in" @click.stop="handlePreview(doctorData.idCard.urls[1])"></i>
                  <i style="font-size:25px" class="el-icon-download" @click.stop="handleDownload(doctorData.idCard.urls[1])"></i> -->

                  <!-- <img v-if="baseStatus!=2" style="width:30px;height:30px" src="/static/images/ocr.png" alt="" @click.stop="handleOCR(doctorData.idCard.urls[1],1)"> -->
                  <img style="width:30px;height:30px" src="/static/images/download.png" alt="" @click.stop="handleDownload(doctorData.idCard.urls[1])">
                  <img style="width:30px;height:30px" src="/static/images/enlarge.png" alt="" @click.stop="previewPic('previewImgidCard1')">
                </div>
                <i v-else class="el-icon-circle-plus-outline"></i>
              </div>
            </el-form-item>
            <div style="margin-left: 150px;margin-bottom:20px">只能上传jpg/jpeg/png/文件，且不超过500kb</div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="医师资格证:">
              <el-input
                v-model="doctorData.qualificationCertificate.number"
                class="mboxinput"
                clearable
              />
              <div class="box" style="float:left;margin-right:10px;">
                <input
                  id="boxtx61"
                  ref="boxtx61"
                  name="boxtx61"
                  class="imgfile"
                  type="file"
                  multiple="false"
                  accept="image/png, image/gif, image/jpeg"
                  @change="handleFileChange($event, '6', '1')"
                />
                <label for="boxtx61"></label>
                <div
                  v-if="
                    doctorData.qualificationCertificate &&
                      doctorData.qualificationCertificate.urls &&
                      doctorData.qualificationCertificate.urls.length >= 1 &&
                      doctorData.qualificationCertificate.urls[0]
                  "
                  class="img"
                >
                  <div>
                    <!-- <img :src="doctorData.qualificationCertificate.urls[0]" /> -->
                    <el-image
                      ref="previewImgCertificate"
                      :src="doctorData.qualificationCertificate.urls[0]"
                      :preview-src-list="[doctorData.qualificationCertificate.urls[0]]"
                    >
                    </el-image>
                  </div>
                </div>
                <div
                  v-if="
                    doctorData.qualificationCertificate &&
                      doctorData.qualificationCertificate.urls &&
                      doctorData.qualificationCertificate.urls.length >= 1 &&
                      doctorData.qualificationCertificate.urls[0]
                  "
                  class="downloadBox"
                >
                  <!-- <img style="width:30px;height:30px" src="/static/images/ocr.png" alt="" @click.stop="handleOCR(doctorData.qualificationCertificate.urls[0],3)"> -->
                  <img style="width:30px;height:30px" src="/static/images/download.png" alt="" @click.stop="handleDownload(doctorData.qualificationCertificate.urls[0])">
                  <img style="width:30px;height:30px" src="/static/images/enlarge.png" alt="" @click.stop="previewPic('previewImgCertificate')">
                </div>
                <i v-else class="el-icon-circle-plus-outline"></i>
              </div>
              <div class="box" style="float:left;margin-right:10px;">
                <input
                  id="boxtx62"
                  ref="boxtx62"
                  name="boxtx62"
                  class="imgfile"
                  type="file"
                  multiple="false"
                  accept="image/png, image/gif, image/jpeg"
                  @change="handleFileChange($event, '6', '2')"
                />
                <label for="boxtx62"></label>
                <div
                  v-if="
                    doctorData.qualificationCertificate &&
                      doctorData.qualificationCertificate.urls &&
                      doctorData.qualificationCertificate.urls.length == 2 &&
                      doctorData.qualificationCertificate.urls[1]
                  "
                  class="img"
                >
                  <div>
                    <!-- <img :src="doctorData.qualificationCertificate.urls[1]" /> -->
                    <el-image
                      ref="previewImgCertificate1"
                      :src="doctorData.qualificationCertificate.urls[1]"
                      :preview-src-list="[doctorData.qualificationCertificate.urls[1]]"
                    >
                    </el-image>
                  </div>
                </div>
                <div
                  v-if="
                    doctorData.qualificationCertificate &&
                      doctorData.qualificationCertificate.urls &&
                      doctorData.qualificationCertificate.urls.length == 2 &&
                      doctorData.qualificationCertificate.urls[1]
                  "
                  class="downloadBox"
                >

                  <!-- <i style="font-size:25px" class="el-icon-full-screen" @click.stop="handleOCR(doctorData.qualificationCertificate.urls[1],3)"></i>
                  <i style="font-size:25px" class="el-icon-zoom-in" @click.stop="handlePreview(doctorData.qualificationCertificate.urls[1])"></i>
                  <i style="font-size:25px" class="el-icon-download" @click.stop="handleDownload(doctorData.qualificationCertificate.urls[1])"></i> -->

                  <!-- <img style="width:30px;height:30px" src="/static/images/ocr.png" alt="" @click.stop="handleOCR(doctorData.qualificationCertificate.urls[1],3)"> -->
                  <img style="width:30px;height:30px" src="/static/images/download.png" alt="" @click.stop="handleDownload(doctorData.qualificationCertificate.urls[1])">
                  <img style="width:30px;height:30px" src="/static/images/enlarge.png" alt="" @click.stop="previewPic('previewImgCertificate1')">
                </div>
                <i v-else class="el-icon-circle-plus-outline"></i>
              </div>
            </el-form-item>
            <div style="margin-left: 150px;margin-bottom:20px">只能上传jpg/jpeg/png/文件，且不超过500kb</div>
          </el-col>
        </el-row>
        <div class="btn">
          <el-button
            :loading="loading"
            type="primary"
            @click="saveDoctor('dataForm')"
          >提交</el-button>
        </div>
      </el-form>
      <el-dialog
        title="选择医院"
        :visible.sync="dialogFormVisible"
        width="500px"
        append-to-body
      >
        <el-form :model="form">
          <el-form-item label="省市:">
            <el-cascader
              :options="cityData"
              :props="props"
              style="width: 300px;"
              @change="changeCity"
            ></el-cascader>
          </el-form-item>
          <select
            id=""
            size="5"
            name=""
            style="width: 100%;height:300px;"
            @click="hospitalCk($event)"
          >
            <option
              v-for="(val, key) in hospitalData"
              :key="key"
              :value="val.id"
            >{{ val.name }}</option>
          </select>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="sureHospital">确 定</el-button>
        </div>
      </el-dialog>
    </el-dialog>
    <el-dialog :visible.sync="PreviewVisible" append-to-body>
      <img width="100%" :src="PreviewImageUrl" alt="" />
    </el-dialog>
  </div>
</template>
<style scoped>
.btn {
  text-align: right;
  padding: 10px 50px;
}
.mboxinput {
  margin-bottom: 10px;
}
.imgfile {
  font-size: 0; /* 为了去掉‘未选择任何文件’这几个字，也可以随便弄到哪里*/
  position: absolute;
  left: -9999px;
}
/* 注意不是直接input > input[type=button] 哦*/
.imgfile::-webkit-file-upload-button {
  background: #efeeee;
  color: #333;
  border: 0;
  padding: 40px 100px;
  border-radius: 5px;
  font-size: 12px;
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.1), 0 0 10px rgba(0, 0, 0, 0.12);
}
.box {
  position: relative;
  margin-bottom: 10px;
  width: 180px;
  height: 180px;
  font-size: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed #ccc;
}
/* 使label充满整个box*/
.box label {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10; /* 这个z-index之后说到*/
}
.imgbox .img {
  position: relative;
  height: 120px;
  width: 120px;
  display: table;
  text-align: center;
}
/* .box .img div {
  display: table-cell;
  vertical-align: middle;
} */
.box .img img {
  max-width: 118px;
  max-height: 118px;
}
.box .img .el-image {
  max-width: 118px;
  max-height: 118px;
}
.box .img i {
  font-size: 22px;
  position: absolute;
  left: 0;
  top: 0;
}
.downloadBox {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 1000;
  /* z-index: 999999; */
}
</style>
<script>
import { get } from '@/api/user/doctor'
import {
  uploadImg,
  updateDoctor,
  uploadDoctorSign,
  cityList,
  hospitalList,
  department,
  getImageOCR
} from '@/api/user/doctor'
import DictSelect from '@/components/DictSelect'
import { Loading } from 'element-ui'
import Desensitization from '@/components/Desensitization'
export default {
  name: 'DoctorTable',
  filters: {},
  components: {
    DictSelect,
    Desensitization
  },
  props: {
    doctorId: {
      type: [String, Number],
      required: false,
      default: ''
    },
    baseStatus: {
      type: [String, Number],
      required: false,
      default: ''
    },
    recordStatus: {
      type: [String, Number],
      required: false,
      default: ''
    }
  },
  data() {
    var checkEmail = (rule, value, callback) => {
      const mailReg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/
      if (value) {
        setTimeout(() => {
          if (mailReg.test(value)) {
            callback()
          } else {
            callback(new Error('请输入正确的邮箱格式'))
          }
        }, 100)
      } else {
        callback()
      }
    }
    return {
      time: Date.now(),
      doctor: {},
      form: {},
      doctorData: {
        qualificationCertificate: {
          number: ''
        },
        medical: {
          number: ''
        },
        idCard: {
          number: ''
        },
        modifyType: ''
      },
      rules: {
        name: [{ required: true, message: '请输入医生姓名', trigger: 'blur' }],
        pracIssue: [{ required: true, message: '请填写执业证取得日期' }],
        email: { validator: checkEmail, trigger: 'blur' },
        cardNo: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (!this.doctorData.idCard.number || (this.doctorData.idCard.number.length !== 15 && this.doctorData.idCard.number.length !== 18)) {
                callback(new Error('请输入正确的身份证号'))
              } else {
                callback()
              }
            }
          }
        ]
      },
      dialogFormVisible: false,
      cityData: [],
      departmentData: [],
      hospitalData: [],
      hospitalItem: {},
      hospitalName: null,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name',
        checkStrictly: true
      },
      setDocinfoVisible: false,
      PreviewImageUrl: '',
      PreviewVisible: false,
      loading: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      }
    }
  },
  created() {
    // console.log(this.$route.params.doctorId);
    // this.department();
    // this.get(this.$route.params.doctorId);
    // this.cityList();
  },
  methods: {
    initData() {
      console.log(this.doctorData.modifyType, 694)
      this.resetTemp()
      this.setDocinfoVisible = true
      this.department()
      this.get(this.doctorId)
      this.cityList()
    },
    get(doctorId) {
      get(doctorId).then(response => {
        var spData = {}
        // console.log(response)
        // this.doctor = response
        // this.headUrl = response.baseInfo.headUrl
        spData.doctorId = response.baseInfo.id
        spData.phone = response.baseInfo.phone
        spData.email = response.baseInfo.email
        spData.pracIssue = response.baseInfo.pracIssue
        // spData.headUrl = response.baseInfo.headUrl
        if (response.baseInfo.name) {
          spData.name = response.baseInfo.name
        }
        if (response.baseInfo.departmentId) {
          spData.departmentId = response.baseInfo.departmentId
        }
        if (response.baseInfo.titleId || response.baseInfo.titleId === 0) {
          spData.titleId = response.baseInfo.titleId
        }
        if (response.baseInfo.hospitalId) {
          spData.hospitalId = response.baseInfo.hospitalId
        }
        if (response.baseInfo.headUrl) {
          spData.headUrl = response.baseInfo.headUrl
        }
        if (response.licences.titleCertificate) {
          spData.titleCertificate = response.licences.titleCertificate
        }
        if (response.baseInfo.expertise) {
          spData.expertise = response.baseInfo.expertise
        }
        if (response.baseInfo.introduction) {
          spData.introduction = response.baseInfo.introduction
        }
        if (response.licences.idCard) {
          spData.idCard = response.licences.idCard
        } else {
          spData.idCard = { number: '' }
        }
        if (response.baseInfo.emCard) {
          spData.emCard = response.baseInfo.emCard
        }
        if (response.licences.qualificationCertificate) {
          spData.qualificationCertificate =
            response.licences.qualificationCertificate
        } else {
          spData.qualificationCertificate = { number: '' }
        }
        if (response.baseInfo.medical) {
          spData.medical = response.baseInfo.medical
        } else {
          spData.medical = { number: '' }
        }
        if (response.baseInfo.name) {
          spData.signImgUrl = response.baseInfo.sealImage
        }
        if (response.baseInfo.hospitalId || response.baseInfo.hospitalName === 0) {
          this.doctorData.hospitalId = response.baseInfo.hospitalId
          this.hospitalName = response.baseInfo.hospitalName
        }
        if (response.baseInfo.phoneEnc) {
          spData.phoneEnc = response.baseInfo.phoneEnc
        }
        if (response.baseInfo.emailEnc) {
          spData.emailEnc = response.baseInfo.emailEnc
        }
        this.doctorData = spData
        // console.log(Object.assign(this.doctorData, spData))
        // this.doctorData = Object.assign(this.doctorData, spData)
        // debugger;
        console.log(this.doctorData)
      })
      // this.listQuery.pageNo = 1
    },
    changeHost() {
      this.dialogFormVisible = true
    },
    changeCity(value) {
      console.log(value)
      this.hospitalList(value)
    },
    saveDoctor(dataForm) {
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          if (
            !(
              this.doctorData.idCard &&
              this.doctorData.idCard.urls &&
              (this.doctorData.idCard.urls.length === 1 ||
                (this.doctorData.idCard.urls.length === 2 &&
                  (this.doctorData.idCard.urls[0] === '' ||
                    this.doctorData.idCard.urls[1] === '')))
            )
          ) {
            this.doctorData.modifyType = this.type
            console.log(this.compareTime(this.doctorData.pracIssue), '861')
            if (!this.compareTime(this.doctorData.pracIssue)) {
              this.loading = true
              console.log('this.doctorData', this.doctorData)
              if (this.doctorData.email && this.doctorData.email.includes('*')) delete this.doctorData.email
              if (this.doctorData.phone && this.doctorData.phone.includes('*')) delete this.doctorData.phone
              updateDoctor(this.doctorData).then(
                response => {
                  this.loading = false
                  this.$message({
                    message: '保存成功',
                    type: 'success'
                  })
                  console.log(this.setDocinfoVisible, 1)
                  this.setDocinfoVisible = false
                  this.$emit('setDatainfo')
                },
                error => {
                  this.loading = false
                }
              )
            } else {
              this.$confirm(
                '执业不满3年不允许备案视频复诊业务，是否确认提交',
                {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }
              ).then(() => {
                this.loading = true
                console.log('this.doctorData', this.doctorData)
                if (this.doctorData.email && this.doctorData.email.includes('*')) delete this.doctorData.email
                if (this.doctorData.phone && this.doctorData.phone.includes('*')) delete this.doctorData.phone
                updateDoctor(this.doctorData).then(
                  response => {
                    this.loading = false
                    this.$message({
                      message: '保存成功',
                      type: 'success'
                    })
                    console.log(this.setDocinfoVisible, 1)
                    this.setDocinfoVisible = false
                    this.$emit('setDatainfo')
                  },
                  error => {
                    this.loading = false
                  }
                )
              })
            }

          } else {
            this.$message.error('身份证必须上传正反面')
          }
        } else {
          this.loading = false
          // loadingInstance.close()
        }
      })
    },
    handleSignChange() {
      const param = new FormData() // 创建form对象
      param.append('file', event.target.files[0]) //对应后台接收图片名
      uploadDoctorSign(this.doctorData.doctorId, param).then(
        response => {
          console.log(response)
          this.$set(this.doctorData, 'signImgUrl', response)
        },
        error => {}
      )
    },
    //图片类型验证
    verificationPicFile(file) {
      var fileTypes = ['.jpg', '.png', '.jpeg']
      var filePath = file.value
      console.log(file, 1381)
      //当括号里面的值为0、空字符、false 、null 、undefined的时候就相当于false
      if (filePath) {
        var isNext = false
        var fileEnd = filePath.substring(filePath.indexOf('.'))
        for (var i = 0; i < fileTypes.length; i++) {
          if (fileTypes[i] === fileEnd) {
            isNext = true
            break
          }
        }
        if (!isNext) {
          alert('不接受此文件类型')
          file.value = ''
          return false
        }
      } else {
        return false
      }
    },
    handleFileChange(event, index, index2) {
      const suffixName = event.target.files[0].name.substring(event.target.files[0].name.lastIndexOf('.') + 1) /* 得到文件后缀名 */
      if (suffixName !== 'jpg' && suffixName !== 'png' && suffixName !== 'jpeg') {
        this.$message({
          type: 'warning',
          message: '上传文件只能是 jpg、png、jpeg格式!'
        })
        return
      }
      const isLt = index === '1' ? event.target.files[0].size / 1024 < 100 : event.target.files[0].size / 1024 < 500
      const size = index === '1' ? 100 : 500
      if (!isLt) {
        this.$message({
          type: 'warning',
          message: `请上传小于${size}kb的图片文件`
        })
        return
      }
      const param = new FormData() // 创建form对象
      param.append('file', event.target.files[0]) //对应后台接收图片名
      uploadImg(this.doctorData.doctorId, index, param).then(
        response => {
          console.log(response)
          switch (index) {
            case '1':
              this.$set(this.doctorData, 'headUrl', response) // 改变数组
              break
            case '2':
              if (!this.doctorData.emCard) {
                this.$set(this.doctorData, 'emCard', { urls: [response] }) // 改变数组
              } else if (this.doctorData.emCard.urls) {
                this.$set(this.doctorData.emCard.urls, 0, response) // 改变数组
              } else {
                this.$set(this.doctorData.emCard, 'urls', [response]) // 改变数组
              }
              break
            case '3':
              if (index2 === '1') {
                if (!this.doctorData.medical) {
                  this.$set(this.doctorData, 'medical', { urls: [response] }) // 改变数组
                } else if (this.doctorData.medical.urls) {
                  this.$set(this.doctorData.medical.urls, 0, response) // 改变数组
                } else {
                  this.$set(this.doctorData.medical, 'urls', [response]) // 改变数组
                }
              } else if (index2 === '2') {
                if (!this.doctorData.medical) {
                  this.$set(this.doctorData, 'medical', {
                    urls: ['', response]
                  }) // 改变数组
                } else if (this.doctorData.medical.urls) {
                  this.$set(this.doctorData.medical.urls, 1, response) // 改变数组
                } else {
                  this.$set(this.doctorData.medical, 'urls', ['', response]) // 改变数组
                }
              }
              break
            case '4':
              if (index2 === '1') {
                // this.doctorData.idCard.urls[0] = response
                if (!this.doctorData.idCard) {
                  this.$set(this.doctorData, 'idCard', { urls: [response] }) // 改变数组
                } else if (this.doctorData.idCard.urls) {
                  this.$set(this.doctorData.idCard.urls, 0, response) // 改变数组
                } else {
                  this.$set(this.doctorData.idCard, 'urls', [response]) // 改变数组
                }
              } else if (index2 === '2') {
                // this.doctorData.idCard.urls[1] = response
                if (!this.doctorData.idCard) {
                  this.$set(this.doctorData, 'idCard', {
                    urls: ['', response]
                  }) // 改变数组
                } else if (this.doctorData.idCard.urls) {
                  this.$set(this.doctorData.idCard.urls, 1, response) // 改变数组
                } else {
                  this.doctorData.idCard.urls = ['', response]
                  this.$set(this.doctorData.idCard, 'urls', ['', response]) // 改变数组
                }
              }
              break
            case '5':
              if (!this.doctorData.titleCertificate) {
                this.$set(this.doctorData, 'titleCertificate', {
                  urls: [response]
                }) // 改变数组
              } else if (this.doctorData.titleCertificate.urls) {
                this.$set(this.doctorData.titleCertificate.urls, 0, response) // 改变数组
              } else {
                this.$set(this.doctorData.titleCertificate, 'urls', [response]) // 改变数组
              }
              break
            case '6':
              if (index2 === '1') {
                // this.doctorData.qualificationCertificate.urls[0] = response
                if (!this.doctorData.qualificationCertificate) {
                  this.$set(this.doctorData, 'qualificationCertificate', {
                    urls: [response]
                  }) // 改变数组
                } else if (this.doctorData.qualificationCertificate.urls) {
                  this.$set(
                    this.doctorData.qualificationCertificate.urls,
                    0,
                    response
                  ) // 改变数组
                } else {
                  this.$set(this.doctorData.qualificationCertificate, 'urls', [
                    response
                  ]) // 改变数组
                }
              } else if (index2 === '2') {
                // this.doctorData.qualificationCertificate.urls[1] = response
                if (!this.doctorData.qualificationCertificate) {
                  this.$set(this.doctorData, 'qualificationCertificate', {
                    urls: ['', response]
                  }) // 改变数组
                } else if (this.doctorData.qualificationCertificate.urls) {
                  this.$set(
                    this.doctorData.qualificationCertificate.urls,
                    1,
                    response
                  ) // 改变数组
                } else {
                  this.$set(this.doctorData.qualificationCertificate, 'urls', [
                    '',
                    response
                  ]) // 改变数组
                }
              }
              break
          }
          switch (index) {
            case '1':
              this.$refs['boxtx1'].value = ''
              break
            case '2':
              this.$refs['boxtx2'].value = ''
              break
            case '3':
              if (index2 === '1') {
                this.$refs['boxtx31'].value = ''
              } else if (index2 === '2') {
                this.$refs['boxtx32'].value = ''
              }
              break
            case '4':
              if (index2 === '1') {
                this.$refs['boxtx41'].value = ''
              } else if (index2 === '2') {
                this.$refs['boxtx41'].value = ''
              }
              break
            case '5':
              this.$refs['boxtx5'].value = ''
              break
            case '6':
              if (index2 === '1') {
                this.$refs['boxtx61'].value = ''
              } else if (index2 === '2') {
                this.$refs['boxtx62'].value = ''
              }
              break
          }
        },
        error => {
          // if (index !== undefined) {
          //   this.$refs['imgfile' + index][0].value = ''
          // } else {
          //   this.$refs['imgfile_' + this.doctorData.images.length].value = ''
          // }
          switch (index) {
            case '1':
              this.$refs['boxtx1'].value = ''
              break
            case '2':
              this.$refs['boxtx2'].value = ''
              break
            case '3':
              if (index2 === '1') {
                this.$refs['boxtx31'].value = ''
              } else if (index2 === '2') {
                this.$refs['boxtx32'].value = ''
              }
              break
            case '4':
              if (index2 === '1') {
                this.$refs['boxtx41'].value = ''
              } else if (index2 === '2') {
                this.$refs['boxtx41'].value = ''
              }
              break
            case '5':
              this.$refs['boxtx5'].value = ''
              break
            case '6':
              if (index2 === '1') {
                this.$refs['boxtx61'].value = ''
              } else if (index2 === '2') {
                this.$refs['boxtx62'].value = ''
              }
              break
          }
        }
      )
      // this.$set(this.doctorData,'images',images)
    },
    hospitalCk(event) {
      this.hospitalItem = { id: event.target.value, name: event.target.text }
    },
    sureHospital() {
      if (this.hospitalItem.id) {
        this.dialogFormVisible = false
        this.doctorData.hospitalId = this.hospitalItem.id
        this.hospitalName = this.hospitalItem.name
        this.hospitalItem = {}
      } else {
        this.$message.error('请选择医院')
      }
    },
    cityList() {
      cityList().then(response => {
        this.cityData = response[0].children
      })
    },
    department() {
      department().then(response => {
        this.departmentData = response
      })
    },
    hospitalList(id) {
      hospitalList(id).then(response => {
        this.hospitalData = response
      })
    },
    handleClose() {
      this.setDocinfoVisible = false
      this.$emit('setDatainfo')
    },
    handlePreview(url) {
      this.PreviewImageUrl = url
      this.PreviewVisible = true
    },
    previewPic(imgRef) {
      this.$refs[imgRef].showViewer = true
    },
    handleDownload(url) {
      console.log(url)
      this.downloadIamge(url, new Date().getTime())
    },
    handleOCR(url, type) {
      const param = {
        fileUrl: url,
        type: type
      }
      const loadingInstance = Loading.service({ fullscreen: true })
      getImageOCR(param).then(
        response => {
          console.log(response)
          this.$message({
            message: '识别成功',
            type: 'success'
          })
          if (type === 1) {
            this.doctorData.idCard.number = response
          } else if (type === 2) {
            this.doctorData.medical.number = response
          } else {
            this.doctorData.qualificationCertificate.number = response
          }
        },
        error => {}
      ).finally(function() {
        loadingInstance.close()
      })
    },
    downloadIamge(imgsrc, name) {
      //下载图片地址和图片名
      var image = new Image()
      image.setAttribute('crossOrigin', 'anonymous')
      image.onload = function() {
        var canvas = document.createElement('canvas')
        canvas.width = image.width
        canvas.height = image.height
        var context = canvas.getContext('2d')
        context.drawImage(image, 0, 0, image.width, image.height)
        var url = canvas.toDataURL('image/png') //得到图片的base64编码数据
        var a = document.createElement('a') // 生成一个a元素
        var event = new MouseEvent('click') // 创建一个单击事件
        a.download = name || 'photo' // 设置图片名称
        a.href = url // 将生成的URL设置为a.href属性
        a.dispatchEvent(event) // 触发a的单击事件
      }
      image.src = imgsrc
    },
    resetTemp() {
      this.$nextTick(() => {
        if (this.$refs['dataForm']) {
          this.$refs['dataForm'].clearValidate()
        }
      })
    },
    compareTime(date) {
      const now = new Date().getTime()
      const endDate = new Date(date).getTime()
      const long = 36 * 30 * 24 * 60 * 60 * 1000
      return now - endDate < long
    }
  }
}
</script>
