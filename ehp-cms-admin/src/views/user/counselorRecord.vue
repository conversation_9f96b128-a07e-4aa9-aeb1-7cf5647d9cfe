<template>
  <div>
    <div class="filter-container">
      <el-input
        v-model="listQuery.counselorId"
        clearable
        placeholder="咨询师ID"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.workNumber"
        clearable
        placeholder="咨询师工号"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.counselorName"
        clearable
        placeholder="咨询师姓名"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.patientId"
        clearable
        placeholder="患者ID"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.patientName"
        clearable
        placeholder="患者姓名"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-date-picker
        v-model="lastReplyTime"
        style="width: 320px"
        type="daterange"
        unlink-panels
        :value-format="valueFormat"
        range-separator="至"
        start-placeholder="最后回复开始日期"
        end-placeholder="最后回复结束日期"
        @change="handleFilter"
      />
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >搜索</el-button>
      <el-button
        v-waves
        type="primary"
        icon="el-icon-refresh"
        @click="handleReset"
      >重置</el-button>
    </div>
    <el-table
      :key="tableKey"
      ref="table"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="咨询师ID" fixed prop="counselorId" width="90px" align="center" />
      <el-table-column
        label="工号"
        prop="workNumber"
        width="110px"
        align="center"
      />
      <el-table-column label="咨询师姓名" prop="counselorName" width="110px" align="center" />
      <el-table-column
        label="患者ID"
        prop="patientId"
        align="center"
        width="200"
      />
      <el-table-column
        label="患者姓名"
        prop="patientName"
        align="center"
        width="200"
      />
      <el-table-column
        label="最后回复时间"
        min-width="150"
        prop="lastReplyTime"
        align="center"
      />
      <el-table-column label="操作" width="160" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button
            type="primary"
            size="mini"
            @click="handleView(scope.row.id)"
          >查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <!-- 会话详情 -->
    <el-dialog
      :close-on-click-modal="true"
      title="会话"
      :visible.sync="dialogSessionDetail"
      width="460px"
      top="5vh"
      :destroy-on-close="true"
    >
      <div>
        <counselorDetail :id="id" ref="counselorRef" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSessionList } from '@/api/user/counselor'
import waves from '@/directive/waves' // Waves directive
import counselorDetail from './components/counselorDetails'
export default {
  name: 'Record',
  components: { counselorDetail },
  directives: { waves },
  filters: {},
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      lastReplyTime: '',
      valueFormat: 'yyyy-MM-dd',
      dialogSessionDetail: false,
      id: ''
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取数据
    getList() {
      this.listQuery.dateGte = this.lastReplyTime[0]
      this.listQuery.dateLte = this.lastReplyTime[1]
      getSessionList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    // 查看
    handleView(id) {
      this.id = id
      this.dialogSessionDetail = true
      setTimeout(() => {
        this.$refs.counselorRef.getList()
      }, 0)
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.counselorId = ''
      this.listQuery.counselorName = ''
      this.listQuery.patientId = ''
      this.listQuery.patientName = ''
      this.listQuery.workNumber = ''
      this.lastReplyTime = ''
      this.handleFilter()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog {
    height: 90%;
    overflow: auto;
}
::v-deep .el-dialog {
  background: #f8f8f8!important;
}
</style>
