<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="图文问诊" name="1">
        <div class="filter-container">
          <el-input
            v-model="listQuery.doctorId"
            clearable
            class="filter-item"
            placeholder="医生ID"
            style="width: 150px;"
            onkeyup="value=value.replace(/[^\d]/g,'')"
            @keyup.enter.native="handleFilter"
          />
          <el-input
            v-model="listQuery.doctorName"
            clearable
            class="filter-item"
            placeholder="医生姓名"
            style="width: 150px;"
            @keyup.enter.native="handleFilter"
          />
          <el-input
            v-model="listQuery.departmentName"
            clearable
            class="filter-item"
            placeholder="科室名称"
            style="width: 150px;"
            @keyup.enter.native="handleFilter"
          />
          <DatePicker
            ref="datePickerRef1"
            :query-model="listQuery"
            class="filter-item"
            style="width: 230px;"
            @change="handleFilter"
          />
          <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
          <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </div>

        <el-table
          :data="list"
          fit
          highlight-current-row
          style="width: 100%;"
          :row-style="{ height: '42px' }"
          :header-row-style="{ height: '42px' }"
          :header-cell-style="{
            background: '#F8F9FB'
          }"
        >
          <el-table-column label="医生ID" prop="doctorId" align="center" width="90" />
          <el-table-column label="医生姓名" prop="doctorName" align="center" width="100" />
          <el-table-column label="手机" prop="doctorPhone" align="center" width="150" />
          <el-table-column label="科室" prop="departmentName" align="center" />
          <el-table-column label="问诊数" prop="consultCount" align="center" />
          <el-table-column label="实收(元)" prop="consultIncome" align="center" />
        </el-table>

        <div class="footer-pagination">
          <div class="result">
            问诊数{{ totalConsultCount }}，实收<span style="color: red;">¥{{ totalConsultIncome }}</span>
          </div>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageNo"
            :limit.sync="listQuery.pageSize"
            @pagination="fetchList"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="视频问诊" name="2">
        <div class="filter-container">
          <el-input
            v-model="listQuery.doctorId"
            clearable
            class="filter-item"
            placeholder="医生ID"
            style="width: 150px;"
            onkeyup="value=value.replace(/[^\d]/g,'')"
            @keyup.enter.native="handleFilter"
          />
          <el-input
            v-model="listQuery.doctorName"
            clearable
            class="filter-item"
            placeholder="医生姓名"
            style="width: 150px;"
            @keyup.enter.native="handleFilter"
          />
          <el-input
            v-model="listQuery.departmentName"
            clearable
            class="filter-item"
            placeholder="科室名称"
            style="width: 150px;"
            @keyup.enter.native="handleFilter"
          />
          <DatePicker
            ref="datePickerRef2"
            :query-model="listQuery"
            class="filter-item"
            style="width: 230px;"
            @change="handleFilter"
          />
          <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
          <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </div>

        <el-table
          v-loading="loading"
          :data="list"
          fit
          highlight-current-row
          style="width: 100%;"
          :row-style="{ height: '42px' }"
          :header-row-style="{ height: '42px' }"
          :header-cell-style="{
            background: '#F8F9FB'
          }"
        >
          <el-table-column label="医生ID" fixed prop="doctorId" align="center" width="90" />
          <el-table-column label="医生姓名1" fixed prop="doctorName" align="center" width="100" />
          <el-table-column label="手机" prop="doctorPhone" align="center" width="150" />
          <el-table-column label="科室" prop="departmentName" align="center" />
          <el-table-column label="问诊数" prop="consultCount" align="center" />
          <el-table-column label="实收(元)" prop="consultIncome" align="center" />
        </el-table>

        <div class="footer-pagination">
          <div class="result">
            问诊数{{ totalConsultCount }}，实收<span style="color: red;">¥{{ totalConsultIncome }}</span>
          </div>
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="listQuery.pageNo"
            :limit.sync="listQuery.pageSize"
            @pagination="fetchList"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import DatePicker from '@/components/DatePicker'
import waves from '@/directive/waves'
import { getConsultation } from '@/api/dataStatistics'
export default {
  name: 'ConsultationIncome',
  directives: { waves },
  components: {
    DatePicker
  },
  data() {
    return {
      listQuery: {
        doctorId: '',
        doctorName: '',
        departmentName: '',
        consultType: '1',
        pageNo: 1,
        pageSize: 10
      },
      total: 0,
      list: [],
      activeName: '1',
      loading: false,
      totalConsultCount: '', //搜索结果问诊数
      totalConsultIncome: '' //搜索结果总收入
    }
  },
  mounted() {
    this.fetchList()
  },
  methods: {
    // 获取列表数据
    async fetchList() {
      try {
        this.loading = true
        const params = { ...this.listQuery }
        const response = await getConsultation(params)
        this.list = response.list
        this.total = response.totalCount
        this.totalConsultCount = response.totalConsultCount
        this.totalConsultIncome = response.totalConsultIncome
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    },
    // 筛选查询
    handleFilter() {
      this.listQuery.pageNo = 1
      this.fetchList()
    },
    // 重置
    handleReset() {
      this.listQuery.doctorId = ''
      this.listQuery.doctorName = ''
      this.listQuery.departmentName = ''
      this.listQuery.consultType = ''
      this.handleFilter()
      this.$refs.datePickerRef1.reset()
      this.$refs.datePickerRef2.reset()
    },
    handleClick(tab, event) {
      this.listQuery.consultType = tab.name
      this.listQuery.pageNo = 1
      this.fetchList()
      console.log(tab.name, tab, event)
    }
  }
}
</script>

<style lang="scss" scoped>
.footer-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .result {
    font-size: 14px;
  }
}
</style>
