<template>
  <div class="switch">
    <span class="switch_text" :class="getClass(1)" @click="tab(1)">
      <slot name="left"></slot>
    </span>
    <span class="line">丨</span>
    <span class="switch_text" :class="getClass(2)" @click="tab(2)">
      <slot name="right"></slot>
    </span>
  </div>
</template>

<script>
export default {
  name: 'TabSwitcher',
  data() {
    return {
      selectedWord: 1
    }
  },
  methods: {
    // 切换销售排行
    tab(wordId) {
      this.selectedWord = wordId
      this.$emit('tab', wordId)
    },
    getClass(wordId) {
      return {
        selected: this.selectedWord === wordId
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.switch {
  display: flex;
  .line {
    color: #888a98;
  }
  .switch_text {
    font-size: 14px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #888a98;
    cursor: pointer;
  }
  .selected {
    color: #315fff !important;
  }
}
</style>
