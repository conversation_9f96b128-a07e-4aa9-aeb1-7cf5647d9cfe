<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.userId"
        clearable
        class="filter-item"
        placeholder="用户ID"
        style="width: 150px"
        onkeyup="value=value.replace(/[^\d]/g,'')"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.phone"
        clearable
        class="filter-item"
        placeholder="手机号"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.orderSn"
        clearable
        class="filter-item"
        placeholder="订单号"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.serialNumber"
        clearable
        placeholder="处方笺编号"
        class="filter-item"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.channelTradeSn"
        clearable
        placeholder="支付流水号"
        class="filter-item"
        style="width: 200px"
        @keyup.enter.native="handleFilter"
      />
      <DictSelect
        v-model="listQuery.orderStatus"
        placeholder="订单状态"
        class="filter-item"
        style="width: 120px"
        type="order_status"
        @change="handleFilter"
      />
      <DatePicker
        ref="datePickerRef"
        :query-model="listQuery"
        class="filter-item"
        style="width: 230px"
        @change="handleFilter"
      />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      <el-button
        v-waves
        v-permission="['oms:order:statistical']"
        type="primary"
        icon="el-icon-search"
        @click="handleStatistical"
      >统计</el-button>
      <el-button
        v-waves
        v-permission="['oms:order:audit']"
        type="primary"
        :disabled="checkListLen === orderVerify_list.length && orderVerify_list.length > 0 ? false : true"
        @click="verifyOrders"
      >订单审核</el-button>
      <!-- <el-button
        v-waves
        v-permission="['oms:order:update']"
        type="primary"
        :disabled="checkListLen === orderCanel_list.length && orderCanel_list.length > 0 ? false : true"
        @click="canelOrders"
      >订单取消</el-button> -->
      <el-button
        v-waves
        v-permission="['oms:order:returnAudit']"
        type="primary"
        :disabled="checkListLen === order_return_audit_list.length && order_return_audit_list.length > 0 ? false : true"
        @click="returnAuditOrders"
      >订单反审核</el-button>
      <el-button
        v-waves
        v-permission="['oms:order:orderExport']"
        type="primary"
        @click="orderExport"
      >订单导出</el-button>
      <el-checkbox v-model="checked" style="margin-left: 5px" @change="handleChecked">
        含测试订单
        <el-tooltip class="item" effect="dark" content="订单备注、收件人、收货地址中含有“测试”二字的数据" placement="top">
          <i class="el-icon-question"> </i>
        </el-tooltip>
      </el-checkbox>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
      @selection-change="orderSelectionChange"
    >
      <el-table-column type="selection" align="center" width="50px"></el-table-column>
      <el-table-column label="订单号" fixed prop="orderSn" width="200px" align="center">
        <template slot-scope="scope">
          <div style="display: flex;align-items: center;justify-content:center">{{ scope.row.orderSn }}&nbsp;<el-tag v-if="scope.row.orderType == 1" size="mini">测</el-tag></div>
        </template>
      </el-table-column>
      <el-table-column label="处方id" fixed prop="recomId" width="100px" align="center" />
      <el-table-column label="处方笺编号" prop="serialNumber" width="150px" align="center" />
      <el-table-column label="用户ID" prop="userId" width="80px" align="center" />
      <el-table-column label="用户名" prop="userName" width="150px" align="center" />
      <el-table-column label="商品总价" prop="totalAmount" width="80px" align="center" />
      <el-table-column label="邮费" prop="freight" width="80px" align="center" />
      <el-table-column label="优惠券" prop="couponPay" width="80px" align="center" />
      <el-table-column label="实付" prop="realPay" width="80px" align="center" />
      <el-table-column label="订单状态" prop="orderStatusDescribe" width="120px" align="center" />
      <el-table-column label="支付状态" prop="payStatusDescribe" width="80px" align="center" />
      <el-table-column label="订单时间" prop="createdAt" width="140px" align="center" />
      <el-table-column label="支付时间" prop="payTime" width="140px" align="center" />
      <el-table-column label="发货时间" prop="sendTime" width="140px" align="center" />
      <el-table-column label="商品类型" prop="drugType" width="80px" align="center">
        <template slot-scope="{row}">
          {{ drugTypes[row.drugType] }}
        </template>
      </el-table-column>
      <el-table-column label="代煎费/制作费" prop="processFee" width="150px" align="center">
        <template slot-scope="{row}">
          {{ row.drugType===1?'--':row.processFee }}
        </template>
      </el-table-column>
      <el-table-column label="订单总额" prop="consultFee" width="80px" align="center">
        <template slot-scope="{row}">
          {{ parseFloat(row.realPay) + parseFloat(row.couponPay) }}
        </template>
      </el-table-column>
      <el-table-column label="支付流水号" prop="channelTradeSn" width="250px" align="center">
        <template slot-scope="{ row }">
          {{ row.channelTradeSn || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" align="center" width="200px">
        <template slot-scope="{row}">
          <el-button
            v-waves
            v-permission="['oms:order:list']"
            type="primary"
            size="mini"
            @click="handleUpdate(row.id)"
          >查看</el-button>
          <el-button
            v-waves
            v-permission="['oms:order:list']"
            :disabled="row.orderStatus == 3 || row.orderStatus == 4 || row.orderStatus == 5 "
            type="primary"
            size="mini"
            @click="canelOrders(row.id)"
          >取消订单</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogStatisticalFormVisible"
      width="80%"
    >
      <el-table :data="statisticalData" border fit highlight-current-row style="width: 100%">
        <el-table-column align="center" label="商品ID" prop="productId" />
        <el-table-column align="center" label="商品名称" prop="productName" />
        <el-table-column align="center" label="sku编码" prop="skuId" />
        <el-table-column align="center" label="sku名称" prop="skuName" />
        <el-table-column align="center" label="数量" prop="countNum" />
      </el-table>
    </el-dialog>

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="80%">
      <el-form
        ref="dataForm"
        :inline="true"
        :model="orderDetails"
        label-position="right"
        label-width="100px"
      >
        <el-tabs style="min-height: 300px;" type="card">
          <el-tab-pane label="订单信息">
            <el-form-item label="用户:" prop="userId">
              <el-input v-model="orderDetails.userId" readonly />
            </el-form-item>
            <el-form-item label="订单号:" prop="orderSn">
              <el-input v-model="orderDetails.orderSn" readonly />
            </el-form-item>
            <el-form-item label="物流单号:" prop="deliveryId">
              <el-input v-model="orderDetails.deliveryId" readonly />
            </el-form-item>
            <el-form-item label="物流公司:" prop="logisticsCompany">
              <el-input v-model="orderDetails.logisticsCompany" readonly />
            </el-form-item>
            <el-form-item label="仓库:" prop="warehouseId">
              <el-select
                v-model="orderDetails.warehouseId"
                placeholder="请选择"
                :disabled="orderDetails.orderStatus===2 || orderDetails.orderStatus===7 ? false : true"
                style="width: 157px;"
              >
                <el-option
                  v-for="item in selectOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  :disabled="!!item.status"
                ></el-option>
              </el-select>
              <!-- <el-input v-model="orderDetails.warehouseId" :readonly="orderDetails.warehouseId ? false : true" /> -->
            </el-form-item>
            <el-form-item label="订单时间:" prop="createdAt">
              <el-input v-model="orderDetails.createdAt" readonly />
            </el-form-item>
            <el-form-item label="捡货人:" prop="pickupMan">
              <el-input v-model="orderDetails.pickupMan" readonly />
            </el-form-item>
            <el-form-item label="订单状态:" prop="orderStatusDescribe">
              <el-input v-model="orderDetails.orderStatusDescribe" readonly />
            </el-form-item>
            <el-form-item label="订单备注:" prop="remark">
              <el-input v-model="orderDetails.remark" readonly />
            </el-form-item>
            <el-form-item label="商品类型:" prop="remark">
              <el-input v-model="drugTypes[orderDetails.drugType]" readonly />
            </el-form-item>
            <el-form-item v-if="orderDetails.drugType===2" label="中药类型:" prop="remark">
              <el-input v-model="packingTypes[orderDetails.packingType]" readonly />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="支付信息">
            <el-form-item label="支付状态:" prop="payStatusDescribe">
              <el-input v-model="orderDetails.payStatusDescribe" readonly />
            </el-form-item>
            <el-form-item label="支付时间:" prop="payTime">
              <el-input v-model="orderDetails.payTime" readonly />
            </el-form-item>
            <el-form-item label="订单金额:" prop="realPay">
              <el-input v-model="orderDetails.realPay" readonly />
            </el-form-item>
            <el-form-item label="商品总价:" prop="totalAmount">
              <el-input v-model="orderDetails.totalAmount" readonly />
            </el-form-item>
            <el-form-item label="邮费:" prop="freight">
              <el-input v-model="orderDetails.freight" readonly />
            </el-form-item>
            <el-form-item label="优惠券:" prop="freight">
              <el-input v-model="orderDetails.couponPay" readonly />
            </el-form-item>
            <el-form-item label="三方流水号:" prop="freight">
              <el-input v-model="orderDetails.channelTradeSn" readonly />
            </el-form-item>
            <el-form-item label="支付方式:" prop="freight">
              <el-input v-model="orderDetails.payTypeDescribe" readonly />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="收货信息">
            <el-form-item label="收货人:" prop="receiver">
              <el-input
                v-model="orderDetails.orderUser.receiver"
                :readonly="orderDetails.orderStatus==2 || orderDetails.orderStatus==7 ? false : true"
              />
            </el-form-item>
            <el-form-item label="电话:" prop="phone">
              <el-input
                v-model="orderDetails.orderUser.phone"
                :readonly="orderDetails.orderStatus==2 || orderDetails.orderStatus==7 ? false : true"
              />
            </el-form-item>
            <el-form-item label="邮编:" prop="zipcode">
              <el-input
                v-model="orderDetails.orderUser.zipcode"
                :readonly="orderDetails.orderStatus==2 || orderDetails.orderStatus==7 ? false : true"
              />
            </el-form-item>
            <el-form-item label="地区:" prop="provice">
              <el-cascader
                v-model="orderDetails.orderUser.cityId"
                :options="cityData"
                :props="props"
                style="width:300px;"
                clearable
                :disabled="orderDetails.orderStatus==2 || orderDetails.orderStatus==7 ? false : true"
              />
            </el-form-item>
            <el-form-item label="详细地址:" prop="address">
              <el-input
                v-model="orderDetails.orderUser.address"
                style="width:300px"
                type="textarea"
                :readonly="orderDetails.orderStatus==2 || orderDetails.orderStatus==7 ? false : true"
              />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="发票信息">
            <el-form-item label="发票抬头:" prop="invoiceTitle">
              <el-input v-model="orderDetails.invoiceTitle" readonly />
            </el-form-item>
            <el-form-item label="发票明细:" prop="invoiceContent">
              <el-input v-model="orderDetails.invoiceContent" readonly />
            </el-form-item>
            <el-form-item label="发票邮箱:" prop="invoiceEmail">
              <el-input v-model="orderDetails.invoiceEmail" readonly />
            </el-form-item>
            <el-form-item label="发票电话:" prop="invoicePhone">
              <el-input v-model="orderDetails.invoicePhone" readonly />
            </el-form-item>
            <el-form-item label="发票税号:" prop="invoiceTaxNo">
              <el-input v-model="orderDetails.invoiceTaxNo" readonly />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="商品信息">
            <el-table :data="orderDetails.orderItems" fit highlight-current-row style="width: 100%">
              <!-- <el-table-column align="center" label="商品ID" width="60" prop="productId" /> -->
              <!-- <el-table-column align="center" label="商品名称" prop="productName" /> -->
              <el-table-column align="center" label="sku编码" width="150" prop="skuNumber" />
              <el-table-column align="center" label="sku名称" prop="skuName" />
              <el-table-column align="center" label="数量" width="50" prop="quantity" />
              <el-table-column align="center" label="售价(元)" width="80" prop="salePrice" />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="日志信息">
            <el-table :data="orderDetails.logs" fit highlight-current-row style="width: 100%">
              <el-table-column align="center" label="操作时间" width="140" prop="createdAt" />
              <el-table-column align="center" label="操作内容" prop="content" />
              <el-table-column align="center" label="操作人" width="130" prop="createdBy" />
              <el-table-column
                align="center"
                label="操作之前订单状态"
                width="130"
                prop="beforeOrderStatusDescribe"
              />
              <el-table-column
                align="center"
                label="操作之后订单状态"
                width="130"
                prop="afterOrderStatusDescribe"
              />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="相关处方">

            <el-table :data="caseList" highlight-current-row fit style="width: 100%">
              <el-table-column label="相关处方" prop="prescriptionPhotoUrl" />
              <el-table-column label="操作" prop="createdAt" width="300px" align="center">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    @click="viewRecords(scope.row.prescriptionPhotoUrl)"
                  >查看</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!-- <pagination
              v-show="total>0"
              :total="total"
              :page.sync="listQuery2.pageNo"
              :limit.sync="listQuery2.pageSize"
              @pagination="getCaseList"
            /> -->
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-waves
          type="primary"
          @click="dialogFormVisible = false"
        >关闭</el-button>
        <el-button
          v-waves
          :disabled="orderDetails.orderStatus == 3 || orderDetails.orderStatus == 4 || orderDetails.orderStatus == 5 "
          type="primary"
          @click="saveOrderDetail"
        >保存</el-button>
        <!--el-button
          v-if="orderDetails.orderStatus == 2 || orderDetails.orderStatus == 9"
          v-waves
          v-permission="['oms:order:deliver']"
          type="primary"
          @click="deliverGoods()"
        >发货并打印面单</el-button>
        <el-button
          v-if="orderDetails.orderStatus == 3"
          v-waves
          v-permission="['oms:order:deliver']"
          type="primary"
          @click="exprEssprint()"
        >打印快递单</el-button>
        <el-button
          v-if="orderDetails.payStatus == 1 && orderDetails.orderStatus == 2"
          v-waves
          v-permission="['oms:order:update']"
          type="danger"
          @click="cancelOfTheGoods()"
        >取消订单</el-button>
        <el-button
          v-if="orderDetails.payStatus == 1 && orderDetails.orderStatus != 2 && orderDetails.orderStatus != 6"
          v-waves
          v-permission="['oms:order:return']"
          type="danger"
          @click="handleReturnOfTheGoodsCreate()"
        >生成退货单</el-button-->
      </div>
    </el-dialog>
    <!-- 退货信息 -->
    <el-dialog
      :title="textMap[returnOfTheGoodsdialogStatus]"
      :visible.sync="dialogReturnAddressFormVisible"
      width="500px"
    >
      <el-form
        ref="dataForm"
        :model="returnOrderUser"
        :rules="returnOrdeRules"
        label-position="right"
        label-width="100px"
      >
        <el-form-item label="地址类型:" prop="invoiceTaxNo">
          <el-radio-group v-model="returnOrderUser.type">
            <el-radio :label="0">原收件地址</el-radio>
            <el-radio :label="1">变更上门取件地址</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="returnOrderUser.type == 1" label="姓名:" prop="receiver">
          <el-input v-model="returnOrderUser.receiver" placeholder="姓名" />
        </el-form-item>
        <el-form-item v-if="returnOrderUser.type == 1" label="省:" prop="provice">
          <el-input v-model="returnOrderUser.provice" placeholder="省" />
        </el-form-item>
        <el-form-item v-if="returnOrderUser.type == 1" label="市:" prop="city">
          <el-input v-model="returnOrderUser.city" placeholder="市" />
        </el-form-item>
        <el-form-item v-if="returnOrderUser.type == 1" label="县:" prop="county">
          <el-input v-model="returnOrderUser.county" placeholder="县" />
        </el-form-item>
        <el-form-item v-if="returnOrderUser.type == 1" label="邮编:" prop="zipcode">
          <el-input v-model="returnOrderUser.zipcode" placeholder="邮编" />
        </el-form-item>
        <el-form-item v-if="returnOrderUser.type == 1" label="联系电话:" prop="phone">
          <el-input v-model="returnOrderUser.phone" placeholder="联系电话" />
        </el-form-item>
        <el-form-item v-if="returnOrderUser.type == 1" label="详细地址:" prop="addr">
          <el-input v-model="returnOrderUser.addr" placeholder="详细地址" />
        </el-form-item>
        <el-form-item v-if="returnOrderUser.type == 1" label="用户备注:" prop="remark">
          <el-input v-model="returnOrderUser.remark" placeholder="用户备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-waves @click="dialogReturnAddressFormVisible = false">取消</el-button>
        <el-button v-waves type="primary" @click="returnOfTheGoods">确定</el-button>
      </div>
    </el-dialog>

    <!-- pdf预览 -->
    <el-dialog title="相关处方" :visible.sync="pdfDialogVisible" class="pdf-dialog">
      <div class="dialog-content">
        <iframe ref="pdfIframe" :src="pdfsrc" width="100%" height="800px" style="border:0;"></iframe>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="pdfDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getList,
  get,
  deliverGoods,
  cancelOfTheGoods,
  canelOrders,
  returnOfTheGoods,
  statistical,
  canelAudit,
  editOrder,
  returnAudit,
  orderExport
} from '@/api/oms/order'
import { getToken, getTokenName } from '@/utils/auth'
import api_pharmacy from '@/api/pharmacy/index'
import { print } from '@/utils/print'
import waves from '@/directive/waves' // Waves directive
import DictSelect from '@/components/DictSelect'
import DatePicker from '@/components/DatePicker'
export default {
  name: 'Omsorder',
  directives: { waves },
  filters: {},
  components: {
    DictSelect,
    DatePicker
  },
  data() {
    return {
      orderCanel_list: [],
      orderVerify_list: [],
      order_return_audit_list: [],
      checkListLen: 0,
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'createdAt',
        orderBy: 'desc',
        userId: '',
        phone: '',
        orderSn: '',
        serialNumber: '',
        orderStatus: '',
        orderType: 0
      },
      checked: false,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      textMap: {
        update: '更新',
        create: '新增',
        info: '订单详情',
        statistical: '商品统计',
        returnOfTheGoodsCreate: '填写收获地址'
      },
      dialogStatus: '',
      returnOfTheGoodsdialogStatus: '',
      dialogFormVisible: false,
      dialogStatisticalFormVisible: false,
      dialogReturnAddressFormVisible: false,
      deliveryInfos: {
        traceList: []
      },
      orderDetails: {
        orderUser: {}
      },
      selectOptions: [],
      cityData: null,
      returnOrderUser: {},
      statisticalData: [],
      returnOrdeRules: {
        receiver: [
          { required: true, message: '请输入收件人姓名', trigger: 'blur' }
        ],
        provice: [{ required: true, message: '请输入省', trigger: 'blur' }],
        city: [{ required: true, message: '请输入市', trigger: 'blur' }],
        county: [{ required: true, message: '请输入县', trigger: 'blur' }],
        phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        addr: [{ required: true, message: '请输入详细地址', trigger: 'blur' }]
      },
      caseList: [],
      pdfsrc: '',
      recomId: '',
      types: ['', '下载', '打印'],
      caseLogList: [],
      pdfDialogVisible: false,
      uploadPath: '',
      headers: {},
      tempPath: '',
      fileList: [],
      drugTypes: ['', '西药', '中药', '其它'],
      packingTypes: ['', '中药饮片', '中药颗粒'],
      packingTypeUnit: ['', 'g', '袋']
    }
  },
  created() {
    this.getSelect()
    this.getCityList()
  },
  activated() {
    this.getList()
    console.log('============activated=============')
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    // 含测试订单
    handleChecked() {
      if (this.checked) {
        delete this.listQuery.orderType
      } else {
        this.listQuery.orderType = 0
      }
      this.handleFilter()
    },
    getSelect() {
      api_pharmacy.select().then(response => {
        this.selectOptions = response
      })
    },
    getCityList() {
      api_pharmacy.citylist().then(response => {
        this.cityData = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.userId = ''
      this.listQuery.phone = ''
      this.listQuery.orderSn = ''
      this.listQuery.serialNumber = ''
      this.listQuery.orderStatus = ''
      this.listQuery.channelTradeSn = ''
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.orderDetails = { orderUser: {}}
        this.statisticalData = []
        this.caseList = []
        this.returnOrderUser = {
          type: 0
        }
      })
    },
    handleUpdate(orderId) {
      this.orderId = orderId
      this.dialogStatus = 'info'
      this.dialogFormVisible = true
      this.resetTemp()
      get(orderId).then(response => {
        this.orderDetails = response
        if (response.prescriptionPhotoUrl) {
          this.caseList.push({
            prescriptionPhotoUrl: response.prescriptionPhotoUrl
          })
        }
      })
    },
    // 发货
    deliverGoods() {
      this.$confirm('是否确认发货？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deliverGoods(this.orderDetails.id).then(response => {
          this.handleFilter()
          this.handleUpdate(this.orderDetails.id)
          this.exprEssprint()
        })
      })
    },
    // 打印
    exprEssprint() {
      this.$confirm('是否打印快递单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        print('oms/order/print/' + this.orderDetails.deliveryId)
      })
    },
    // 统计
    handleStatistical() {
      this.resetTemp()
      statistical(this.listQuery).then(response => {
        this.statisticalData = response
      })
      this.dialogStatus = 'statistical'
      this.dialogStatisticalFormVisible = true
    },
    // 取消订单
    cancelOfTheGoods(orderId) {
      this.$confirm('是否确认取消订单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        cancelOfTheGoods(orderId).then(response => {
          this.$notify({
            title: '成功',
            message: '订单取消成功',
            type: 'success',
            duration: 2000
          })
          this.handleUpdate(orderId)
          this.handleFilter()
        })
      })
    },
    canelOrders(id) {
      this.$confirm('是否取消选中订单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var orderId = [id]
        canelOrders(orderId).then(response => {
          this.$message({
            message: '取消成功',
            type: 'success'
          })
          this.handleFilter()
        })
      })
    },
    verifyOrders() {
      this.$confirm('是否审核选中订单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        canelAudit(this.orderVerify_list).then(response => {
          this.$message({
            message: '审核成功',
            type: 'success'
          })
          this.handleFilter()
        })
      })
    },
    returnAuditOrders() {
      this.$confirm('是否反审核选中订单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        returnAudit(this.order_return_audit_list).then(response => {
          this.$message({
            message: '反审核成功',
            type: 'success'
          })
          this.handleFilter()
        })
      })
    },
    saveOrderDetail() {
      var data = this.orderDetails.orderUser
      data.warehouseId = this.orderDetails.warehouseId
      data.orderId = this.orderId
      editOrder(data).then(response => {
        this.dialogFormVisible = false
        this.$message({
          message: '更新成功',
          type: 'success'
        })
      })
    },
    orderSelectionChange(value) {
      if (value.length > 0) {
        var canelarr = []
        var verifyarr = []
        var order_return_arr = []
        value.forEach((currentValue, index, arr) => {
          if (currentValue.orderStatus <= 5) {
            canelarr.push(currentValue.id)
          }
          if (currentValue.orderStatus === 7) {
            verifyarr.push(currentValue.id)
          }
          if (currentValue.orderStatus === 3 || currentValue.orderStatus === 4 || currentValue.orderStatus === 5) {
            order_return_arr.push(currentValue.id)
          }
          // selectarr.push(currentValue.skuId)
        })
        this.order_return_audit_list = order_return_arr
        this.orderCanel_list = canelarr
        this.orderVerify_list = verifyarr
      } else {
        this.orderCanel_list = []
        this.orderVerify_list = []
        this.order_return_audit_list = []
      }
      this.checkListLen = value.length
    },
    handleReturnOfTheGoodsCreate() {
      this.returnOrderUser = {
        type: 0
      }
      this.returnOfTheGoodsdialogStatus = 'returnOfTheGoodsCreate'
      this.dialogReturnAddressFormVisible = true
    },
    // 退货功能
    returnOfTheGoods() {
      this.$confirm('是否确认生成退货单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        returnOfTheGoods(this.orderDetails.id, this.returnOrderUser).then(
          response => {
            this.dialogReturnAddressFormVisible = false
            this.$notify({
              title: '成功',
              message: '订单申请退货成功',
              type: 'success',
              duration: 2000
            })
            this.handleUpdate(this.orderDetails.id)
            this.handleFilter()
          }
        )
      })
    },
    orderExport() {
      const queryExport = this.listQuery
      delete queryExport.pageNo
      delete queryExport.pageSize
      queryExport[getTokenName()] = getToken()
      const url = orderExport(queryExport)
      // window.location.href = url + this.urlEncode(queryExport)
      // window.location.href = url
      window.open(url)
    },
    getCaseLogList() {
      this.pdfDialogVisible = true
      //response.pdfUrl
      this.pdfsrc = '../../plugin/pdf/web/viewer.html?file=' + encodeURIComponent(this.pdfUrl)
      // this.$nextTick(() => {
      // const iframe = this.$refs.pdfIframe
      //   iframe.onload = () => {
      //     const secondaryPrint = iframe.contentWindow.document.querySelector('#secondaryPrint')
      //     secondaryPrint.addEventListener('click', () => {
      //       console.log('===secondaryPrint===')
      //       this.setCaseLogList(2)
      //     })
      //     const secondaryDownload = iframe.contentWindow.document.querySelector('#secondaryDownload')
      //     secondaryDownload.addEventListener('click', () => {
      //       console.log('===secondaryDownload===')
      //       this.setCaseLogList(1)
      //     })
      //     const print = iframe.contentWindow.document.querySelector('#print')
      //     print.addEventListener('click', () => {
      //       console.log('===print===')
      //       this.setCaseLogList(2)
      //     })
      //     const download = iframe.contentWindow.document.querySelector('#download')
      //     download.addEventListener('click', () => {
      //       console.log('===download===')
      //       this.setCaseLogList(1)
      //     })
      //     console.log('onload')
      //   }
      //   console.log(iframe)
      // })
    },
    viewRecords(pdfUrl) {
      this.pdfUrl = pdfUrl
      this.getCaseLogList()
    },
    uploadEXCEL() {

    },
    handleExcelError() {
      this.fileList = []
    },
    handleExcelSuccess(response, file, fileList) {
      this.fileList = []
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.$message.success(response.msg)
        this.getList()
        // window.open(response.data, '_blank')
      }
    },
    handleRemoveExcel(file, fileList) {

    }
    // setCaseLogList(type) {
    //   const params = {}
    //   params.recordId = this.recomId
    //   params.type = type
    //   setCaseLogList(params)
    // }
  }
}
</script>
