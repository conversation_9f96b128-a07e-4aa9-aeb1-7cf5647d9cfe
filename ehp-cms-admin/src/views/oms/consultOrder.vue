<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.patientId"
        clearable
        class="filter-item"
        placeholder="用户ID"
        style="width: 150px"
        onkeyup="value=value.replace(/[^\d]/g,'')"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.doctorName"
        clearable
        class="filter-item"
        placeholder="医生姓名"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.orderSn"
        clearable
        class="filter-item"
        placeholder="订单号"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.channelTradeSn"
        clearable
        placeholder="支付流水号"
        class="filter-item"
        style="width: 200px"
        @keyup.enter.native="handleFilter"
      />
      <DictSelect
        v-model="listQuery.consultStatus"
        placeholder="订单状态"
        class="filter-item"
        style="width: 120px"
        :options="options.consultStatus"
        @change="handleFilter"
      />
      <DictSelect
        v-model="listQuery.payStatus"
        placeholder="支付状态"
        class="filter-item"
        style="width: 120px"
        :options="options.payStatus"
        @change="handleFilter"
      />
      <DatePicker
        ref="datePickerRef"
        start-placeholder="完成开始时间"
        end-placeholder="完成结束时间"
        :query-model="listQuery"
        class="filter-item"
        style="width: 230px"
        gte="completeTimeBegin"
        lte="completeTimeEnd"
        @change="handleFilter"
      />
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >搜索</el-button>
      <el-button
        v-waves
        type="primary"
        icon="el-icon-refresh"
        @click="handleReset"
      >重置</el-button>
      <el-button
        v-waves
        v-permission="['consult:order:export']"
        type="primary"
        @click="orderExport"
      >订单导出</el-button>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
      @selection-change="orderSelectionChange"
    >
      <el-table-column
        type="selection"
        align="center"
        width="50px"
      ></el-table-column>
      <el-table-column
        label="订单号"
        prop="orderSn"
        :show-overflow-tooltip="true"
        width="160px"
        align="center"
      />
      <el-table-column
        label="医生"
        prop="doctorName"
        width="80px"
        align="center"
      />
      <el-table-column
        label="科室"
        prop="departmentName"
        width="80px"
        align="center"
      />
      <el-table-column
        label="用户ID"
        prop="patientId"
        width="80px"
        align="center"
      />
      <el-table-column
        label="就诊人"
        prop="inquirerName"
        width="80px"
        align="center"
      />
      <el-table-column
        label="问诊类型"
        prop="consultType"
        width="80px"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ row.consultType == 1 ? "图文" : "视频" }}
        </template>
      </el-table-column>
      <el-table-column
        label="问诊来源"
        prop="consultType"
        width="80px"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ row.consultType == 1 ? "极速问诊" : "普通问诊" }}
        </template>
      </el-table-column>
      <el-table-column
        label="问诊状态"
        prop="consultStatus"
        width="80px"
        align="center"
      >
        <template slot-scope="{ row }">
          <span v-if="row.consultStatus == 1">待接诊</span>
          <span v-if="row.consultStatus == 2">进行中</span>
          <span v-if="row.consultStatus == 3">已完成</span>
          <span v-if="row.consultStatus == 4">已取消</span>
        </template>
      </el-table-column>
      <el-table-column
        label="支付状态"
        prop="payStatus"
        width="80px"
        align="center"
      >
        <template slot-scope="{ row }">
          <span v-if="row.payStatus == 1">待支付</span>
          <span v-if="row.payStatus == 2">已支付</span>
          <span v-if="row.payStatus == 3">支付失败</span>
          <span v-if="row.payStatus == 4">支付中</span>
          <span v-if="row.payStatus == 5">订单已关闭</span>
          <span v-if="row.payStatus == 6">退款</span>
        </template>
      </el-table-column>
      <el-table-column
        label="开具处方"
        prop="drugType"
        width="80px"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ row.recomFlag ? "是" : "否" }}
        </template>
      </el-table-column>
      <el-table-column
        label="下单时间"
        prop="createdAt"
        min-width="140px"
        :show-overflow-tooltip="true"
        align="center"
      />
      <el-table-column
        label="接诊时间"
        prop="receivedTime"
        min-width="140px"
        :show-overflow-tooltip="true"
        align="center"
      />
      <el-table-column
        label="完成时间"
        prop="completeTime"
        min-width="140px"
        :show-overflow-tooltip="true"
        align="center"
      />
      <el-table-column
        label="订单金额"
        prop="orderAmount"
        width="80px"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ row.orderAmount }}
        </template>
      </el-table-column>
      <el-table-column
        label="优惠金额"
        prop="discountAmount"
        width="80px"
        align="center"
      />
      <el-table-column label="实付" prop="realPay" width="80px" align="center">
        <template slot-scope="{ row }">
          {{ row.realPay }}
        </template>
      </el-table-column>
      <el-table-column label="支付流水号" prop="channelTradeSn" width="250px" align="center">
        <template slot-scope="{ row }">
          {{ row.channelTradeSn || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" align="center" width="100px">
        <template slot-scope="{ row }">
          <el-button
            v-waves
            v-permission="['consult:order:detail']"
            type="primary"
            size="mini"
            @click="handleUpdate(row)"
          >查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      width="80%"
    >
      <el-form
        ref="dataForm"
        :inline="true"
        :model="orderDetails"
        label-position="right"
        label-width="100px"
      >
        <el-tabs style="min-height: 300px;" type="card">
          <el-tab-pane label="订单信息">
            <el-form-item label="用户ID:">
              <el-input v-model="orderDetails.patientId" readonly />
            </el-form-item>
            <el-form-item label="订单号:">
              <el-input v-model="orderDetails.orderSn" readonly />
            </el-form-item>
            <el-form-item label="医生:">
              <el-input v-model="orderDetails.doctorName" readonly />
            </el-form-item>
            <el-form-item label="科室:">
              <el-input v-model="orderDetails.departmentName" readonly />
            </el-form-item>
            <el-form-item label="下单时间:">
              <el-input v-model="orderDetails.createdAt" readonly />
            </el-form-item>
            <el-form-item label="支付时间:">
              <el-input v-model="orderDetails.payTime" readonly />
            </el-form-item>
            <el-form-item label="完成时间:">
              <el-input v-model="orderDetails.completeTime" readonly />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="支付信息">
            <el-form-item label="支付状态:" prop="payStatusDescribe">
              <DictSelect
                v-model="orderDetails.payStatus"
                class="filter-item"
                style="width: 120px"
                :options="options.payStatus"
                disabled
              />
            </el-form-item>
            <el-form-item label="支付时间:" prop="payTime">
              <el-input v-model="orderDetails.payTime" readonly />
            </el-form-item>
            <el-form-item label="订单金额:" prop="orderAmount">
              <el-input v-model="orderDetails.orderAmount" readonly />
            </el-form-item>
            <el-form-item label="优惠金额:" prop="discountAmount">
              <el-input v-model="orderDetails.discountAmount" readonly />
            </el-form-item>
            <el-form-item label="实付金额:" prop="realPay">
              <el-input v-model="orderDetails.realPay" readonly />
            </el-form-item>
            <el-form-item label="三方流水号:" prop="tradeSn">
              <el-input v-model="orderDetails.tradeSn" readonly />
            </el-form-item>
            <el-form-item label="支付方式:" prop="freight">
              <DictSelect
                v-model="orderDetails.payType"
                class="filter-item"
                style="width: 120px"
                :options="options.payType"
                disabled
              />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="问诊信息">
            <el-form-item v-if="orderDetails.consultType == 1" label="会话ID：">
              <el-button v-if="orderDetails.consultStatus == 3" type="text" @click="openChatPdf(orderDetails)">{{ orderDetails.sessionId }}</el-button>
              <span v-else>{{ orderDetails.sessionId }}</span>
            </el-form-item>
            <br />
            <el-form-item label="姓名:" prop="inquirerName">
              <el-input v-model="orderDetails.inquirerName" readonly />
            </el-form-item>
            <el-form-item label="性别:" prop="phone">
              <DictSelect
                v-model="orderDetails.gender"
                class="filter-item"
                style="width: 120px"
                :options="options.gender"
                disabled
              />
            </el-form-item>
            <el-form-item label="年龄:" prop="age">
              <el-input v-model="orderDetails.age" readonly />
            </el-form-item>
            <el-form-item label="线下诊断:" prop="offlineDiagnosis">
              <el-input v-model="orderDetails.offlineDiagnosis" readonly />
            </el-form-item>
            <el-form-item
              v-if="orderDetails.refusalReason"
              label="拒绝原因:"
              prop="refusalReason"
            >
              <el-input v-model="orderDetails.refusalReason" readonly />
            </el-form-item>
            <el-form-item label="开具处方:" prop="recomFlag">
              {{ orderDetails.recomFlag ? "是" : "否" }}
            </el-form-item>
            <el-row>
              <el-col :span="12">
                <el-form-item label="图片:" prop="offlineDiagnosisImg">
                  <uploadImage
                    :value="orderDetails.offlineDiagnosisImg"
                    :show-file-list="false"
                    :read-only="true"
                  >
                  </uploadImage>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="病历档案">
            <el-table :data="caseList" highlight-current-row fit style="width: 100%">
              <el-table-column align="center" label="就诊人" width="100px" prop="name" />
              <el-table-column align="center" label="医生姓名" width="100px" prop="doctorName" />
              <el-table-column align="center" label="就诊医院" prop="hospitalName" />
              <el-table-column align="center" label="就诊科室" width="100px" prop="departmentName" />
              <el-table-column align="center" label="时间" width="155px" prop="finishTimeDate" />
              <el-table-column label="操作" prop="createdAt" width="300px" align="center">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    @click="viewRecords(scope.row.id,1,scope.$index)"
                  >查看</el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="castListTotal>0"
              :total="castListTotal"
              :page.sync="caseListQuery.pageNo"
              :limit.sync="caseListQuery.pageSize"
              @pagination="getCaseList"
            />
          </el-tab-pane>
          <el-tab-pane label="日志信息">
            <el-table
              :data="orderDetails.logs"
              fit
              highlight-current-row
              style="width: 100%"
            >
              <el-table-column
                align="center"
                label="操作时间"
                width="150"
                prop="createdAt"
              />
              <el-table-column align="center" label="操作内容" prop="content" />
              <el-table-column
                align="center"
                label="操作人"
                width="130"
                prop="createdBy"
              />
              <el-table-column
                align="center"
                label="操作之前订单状态"
                width="130"
                prop="consultStatusBefore"
              >
                <template slot-scope="{ row }">
                  <span v-if="row.consultStatusBefore == 1">待接诊</span>
                  <span v-if="row.consultStatusBefore == 2">进行中</span>
                  <span v-if="row.consultStatusBefore == 3">已完成</span>
                  <span v-if="row.consultStatusBefore == 4">已取消</span>
                </template>
              </el-table-column>
              <el-table-column
                align="center"
                label="操作之后订单状态"
                width="130"
                prop="consultStatusAfter"
              >
                <template slot-scope="{ row }">
                  <span v-if="row.consultStatusAfter == 1">待接诊</span>
                  <span v-if="row.consultStatusAfter == 2">进行中</span>
                  <span v-if="row.consultStatusAfter == 3">已完成</span>
                  <span v-if="row.consultStatusAfter == 4">已取消</span>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-waves
          type="primary"
          @click="dialogFormVisible = false"
        >关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog title="操作日志" :visible.sync="pdfDialogVisible" class="pdf-dialog">
      <div class="dialog-content">
        <iframe ref="pdfIframe" :src="pdfsrc" width="100%" height="800px" style="border:0;"></iframe>
        <!-- <pdf class="pdfbox" :src="pdfsrc"></pdf> -->
        <h2>操作记录</h2>
        <el-table :data="caseLogList" highlight-current-row fit style="width: 100%">
          <el-table-column label="ID" fixed prop="id" width="90px" align="center" />
          <el-table-column align="center" label="用户操作">
            <template slot-scope="{row}">
              {{ types[row.type] }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作人" width="180px" prop="operator" />
          <el-table-column align="center" label="操作时间" width="155px" prop="createdAt" />
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="pdfDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getList, getDetail, orderExport } from '@/api/oms/consult'
import { getSessionPdf, getCaseLogList, setCaseLogList, getCaseList } from '@/api/user/patient'

import { getToken, getTokenName } from '@/utils/auth'
import waves from '@/directive/waves' // Waves directive
import DictSelect from '@/components/DictSelect'
import DatePicker from '@/components/DatePicker'
import uploadImage from '@/components/uploadImage'
export default {
  name: 'ConsultOrder',
  directives: { waves },
  filters: {},
  components: {
    DictSelect,
    DatePicker,
    uploadImage
  },
  data() {
    return {
      checkListLen: 0,
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      textMap: {
        update: '更新',
        create: '新增'
      },
      dialogStatus: '',
      dialogFormVisible: false,
      orderDetails: {},
      options: {
        consultStatus: [
          { value: '待接诊', code: 1 },
          { value: '进行中', code: 2 },
          { value: '已完成', code: 3 },
          { value: '已取消', code: 4 }
        ],
        payStatus: [
          { value: '待支付', code: 1 },
          { value: '已支付', code: 2 },
          { value: '支付失败', code: 3 },
          { value: '支付中', code: 4 },
          { value: '订单已关闭', code: 5 },
          { value: '退款', code: 6 }
        ],
        payType: [{ value: '微信支付', code: 1 }],
        gender: [
          { value: '女', code: 0 },
          { value: '男', code: 1 }
        ]
      },
      recomId: 0,
      caseLogListQuery: {
        pageNo: 1,
        pageSize: 10
      },
      caseListQuery: {
        pageNo: 1,
        pageSize: 10
      },
      castListTotal: 0,
      caseLogList: [],
      caseLogTotal: 0,
      caseList: [],
      pdfsrc: '',
      pdfDialogVisible: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    getCaseList(data) {
      this.caseListQuery.sessionId = data.sessionId
      getCaseList(this.caseListQuery).then(response => {
        this.caseList = response.result
        this.castListTotal = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.patientId = ''
      this.listQuery.orderSn = ''
      this.listQuery.consultStatus = ''
      this.listQuery.payStatus = ''
      this.listQuery.doctorName = ''
      this.listQuery.channelTradeSn = ''
      this.$refs.datePickerRef.reset()
      this.handleFilter()
    },
    handleUpdate(row) {
      this.dialogStatus = 'info'
      this.dialogFormVisible = true
      this.resetTemp()
      getDetail({
        consultId: row.consultId,
        consultType: row.consultType
      }).then(response => {
        this.orderDetails = response
        const offlineDiagnosisImgArr = this.orderDetails.offlineDiagnosisImg.split(
          ','
        )
        this.orderDetails.offlineDiagnosisImg = offlineDiagnosisImgArr.map(
          item => {
            return { url: item }
          }
        )
        this.getCaseList(response)
      })
    },
    orderSelectionChange(value) {
      this.listQuery.orderSns = value.map((i) => i.orderSn).join()
      console.log(this.listQuery, 460)
    },
    resetTemp() {
      this.$nextTick(() => {
        this.orderDetails = { orderUser: {}}
      })
    },
    orderExport() {
      const queryExport = this.listQuery
      delete queryExport.pageNo
      delete queryExport.pageSize
      queryExport[getTokenName()] = getToken()
      const url = orderExport(queryExport)
      window.open(url)
    },
    // 查看聊天记录pdf
    openChatPdf(row) {
      getSessionPdf({ sessionId: row.sessionId }).then(data => {
        if (data) {
          window.open(data)
        }
      })
    },
    viewRecords(id) {
      this.recomId = id
      this.getCaseLogList()
    },
    getCaseLogList() {
      getCaseLogList(this.recomId, this.caseLogListQuery).then(response => {
        if (response.pdfUrl === '') {
          this.$message({
            message: '没有病历档案！',
            type: 'error'
          })
          return
        }
        this.caseLogList = response.operatorList.list
        this.caseLogTotal = response.totalCount
        this.pdfDialogVisible = true
        //response.pdfUrl
        this.pdfsrc = '../../plugin/pdf/web/viewer.html?file=' + encodeURIComponent(response.pdfUrl)
        this.$nextTick(() => {
          const iframe = this.$refs.pdfIframe
          iframe.onload = () => {
            const secondaryPrint = iframe.contentWindow.document.querySelector('#secondaryPrint')
            secondaryPrint.addEventListener('click', () => {
              console.log('===secondaryPrint===')
              this.setCaseLogList(2)
            })
            const secondaryDownload = iframe.contentWindow.document.querySelector('#secondaryDownload')
            secondaryDownload.addEventListener('click', () => {
              console.log('===secondaryDownload===')
              this.setCaseLogList(1)
            })
            const print = iframe.contentWindow.document.querySelector('#print')
            print.addEventListener('click', () => {
              console.log('===print===')
              this.setCaseLogList(2)
            })
            const download = iframe.contentWindow.document.querySelector('#download')
            download.addEventListener('click', () => {
              console.log('===download===')
              this.setCaseLogList(1)
            })
            console.log('onload')
          }
          console.log(iframe)
        })
      })
    },
    setCaseLogList(type) {
      const params = {}
      params.recordId = this.recomId
      params.type = type
      setCaseLogList(params)
    }
  }
}
</script>
