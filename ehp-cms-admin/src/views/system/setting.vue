<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane label="开关配置" name="first">
        <el-card v-for="(item,index) in list" :key="index" class="setingCard">
          <div class="setingList">
            <div>
              <div class="seting-title">{{ item.configTitle }}</div>
              <div class="seting-description">{{ item.configDesc }}</div>
            </div>
            <div class="seting-switch">
              <el-switch
                v-model="item.configSwith"
                active-color="#13ce66"
                inactive-color="#ff4949"
                @change="onSwitchChange($event,item)"
              >
              </el-switch>
            </div>
          </div>
        </el-card>
      </el-tab-pane>
      <el-tab-pane label="其它配置" name="second">
        <el-card v-for="(item,index) in otherList" :key="index" class="setingCard">
          <div class="setingCard-title">{{ item.configTitle }}</div>
          <div class="seting-description">作用于医生端和患者端的客服电话 <span class="set-text" @click="createSetPhone(item)">立即设置</span></div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 客服电话设置 -->
    <el-dialog :visible.sync="dialogVisible" width="500px" @close="phoneDialogClose">
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules">
        <el-form-item label="客服电话" prop="phone">
          <el-input v-model="ruleForm.phone" placeholder="请输入客服电话" @input="handleInput"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm('ruleForm')">确 认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getList,
  setConfig,
  getOtherList,
  setOtherConfig
} from '@/api/system/setting'
// import { validateMobile } from '@/utils/validate'
export default {
  components: {},
  data() {
    return {
      list: [],
      otherList: [],
      activeName: 'first',
      dialogVisible: false,
      ruleForm: {
        phone: ''
      },
      rules: {
        phone: [
          { required: true, message: '请输入客服电话', trigger: 'blur' }
          // { validator: validateMobile.bind(this), trigger: 'blur' }
        ]
      },
      phoneConfig: {}
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getList()
    this.getOtherList()
  },
  mounted() {

  },
  methods: {
    // 获取数据
    getList() {
      getList().then(response => {
        this.list = response
      })
    },
    getOtherList() {
      getOtherList().then(response => {
        this.otherList = response
      })
    },
    onSwitchChange(event, item) {
      setConfig(item).then(response => {
        this.$notify({
          title: '成功',
          message: '更新成功',
          type: 'success',
          duration: 2000
        })
      })
    },
    handleInput(val) {
      this.ruleForm.phone = val.replace(/[^\d+-]/g, '')
    },
    createSetPhone(item) {
      this.ruleForm.phone = item.configValue
      this.phoneConfig = item
      this.dialogVisible = true
    },
    phoneDialogClose() {
      this.$nextTick(() => {
        this.$refs['ruleForm'].resetFields()
      })
    },
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            configKey: this.phoneConfig.configKey,
            value: this.ruleForm.phone
          }
          setOtherConfig(params).then(response => {
            this.dialogVisible = false
            this.$message.success('设置成功')
            setTimeout(() => {
              this.getOtherList()
            }, 500)
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.setingCard{
	margin-bottom: 10px;
}
.setingList{
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.seting-title{
	font-weight: 500;
}
.seting-description{
	margin-top: 10px;
	color: #999999;
}
.set-text {
  color: #1890ff;
  font-weight: 500;
  cursor: pointer;
}
.setingCard-title {
  font-weight: 500;
}
::v-deep.el-input--mini .el-input__inner {
  height: 40px;
  line-height: 40px;
}
::v-deep.el-form-item--mini .el-form-item__label {
  margin-bottom: 10px;
}
</style>
