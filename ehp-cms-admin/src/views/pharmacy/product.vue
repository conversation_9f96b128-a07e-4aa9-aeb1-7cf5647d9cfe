<template>
  <div class="app-container">
    <el-row :gutter="20" style="font-size:20px;">
      <el-col :span="8" class="fcolor">商家名称：{{ warehouseDetail.name }}</el-col>
      <el-col :span="8" class="fcolor">商家编码：{{ warehouseDetail.code }}</el-col>
    </el-row>
    <el-divider></el-divider>
    <div class="box">
      <div class="fcolor" style="padding-bottom: 20px;">商品查询</div>
      <el-form ref="dataForm" :model="goodsQuery" :inline="true" label-width="150px">
        <el-form-item prop="number">
          <span class="fcolor" style="font-weight: 900;">商品代码&nbsp;</span>
          <el-input v-model="goodsQuery.number" style="width:150px;" clearable @keyup.enter.native="handleFilter" />
        </el-form-item>
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="goodsQuery.name" clearable @keyup.enter.native="handleFilter" />
        </el-form-item>
        <el-form-item label="商品基础分类" prop="pharmacologyClassificationId">
          <el-cascader
            v-model="goodsQuery.pharmacologyClassificationId"
            :options="pharmacologyData"
            :props="props"
            :show-all-levels="false"
            clearable
            @change="handleFilter"
          />
        </el-form-item>
        <el-button type="primary" style="margin-left:65px;" @click="handleFilter">查询</el-button>
      </el-form>
      <el-table
        ref="goodsTable"
        style="margin-top:20px;"
        :data="goodsList"
        fit
        highlight-current-row
        @selection-change="goodsSelectionChange"
      >
        <el-table-column type="selection" align="center" width="50"></el-table-column>
        <el-table-column label="商品代码" prop="number" width="150" align="center" />
        <el-table-column label="商品名称" prop="name" align="center" />
        <el-table-column label="商品基础分类" prop="pharmacologyClassificationName" align="center" />
        <el-table-column label="售卖价" prop="salePrice" width="80" align="center" />
      </el-table>
      <el-row :gutter="20" style="margin-top:20px;">
        <el-col :span="8" class="fcolor"></el-col>
        <el-col :span="8" class="fcolor">
          已勾选 {{ goodsSelect.length }} 条记录 &nbsp;&nbsp;&nbsp;
          <el-button
            v-if="goodsSelect.length>0"
            v-permission="['pharmacy:partner:set']"
            type="primary"
            @click="setProduct"
          >设置为合作商品</el-button>
          <el-button v-else v-permission="['pharmacy:partner:set']" type="primary" disabled>设置为合作商品</el-button>
        </el-col>
      </el-row>
      <pagination
        v-show="goodstotal>0"
        style="margin-top:0;"
        :total="goodstotal"
        :page.sync="goodsQuery.pageNo"
        :limit.sync="goodsQuery.pageSize"
        @pagination="getGoodsList"
      />
    </div>
    <el-divider></el-divider>
    <div class="box">
      <div class="fcolor">合作商品数 共{{ warehousetotal }}个</div>
      <el-table
        ref="warehouseTable"
        style="margin-top:20px;"
        :data="warehouseList"
        fit
        highlight-current-row
        @selection-change="warehouseSelectionChange"
      >
        <el-table-column type="selection" align="center" width="50"></el-table-column>
        <el-table-column label="商品代码" prop="number" width="150" align="center" />
        <el-table-column label="商品名称" prop="name" align="center" />
        <el-table-column label="商品基础分类" prop="pharmacologyClassificationName" align="center" />
        <el-table-column label="售卖价" prop="salePrice" width="80" align="center" />
      </el-table>
      <el-row :gutter="20" style="margin-top:20px;">
        <el-col :span="8" class="fcolor"></el-col>
        <el-col :span="8" class="fcolor">
          已勾选 {{ warehouseSelect.length }} 条记录 &nbsp;&nbsp;&nbsp;
          <el-button
            v-if="warehouseSelect.length>0"
            v-permission="['pharmacy:partner:delete']"
            type="primary"
            @click="delProduct"
          >删除</el-button>
          <el-button v-else v-permission="['pharmacy:partner:delete']" type="primary" disabled>删除</el-button>
        </el-col>
      </el-row>
      <pagination
        v-show="warehousetotal>0"
        style="margin-top:0;"
        :total="warehousetotal"
        :page.sync="warehouseQuery.pageNo"
        :limit.sync="warehouseQuery.pageSize"
        @pagination="getPartnerList"
      />
    </div>
  </div>
</template>
<style scoped>
  .fcolor {
    color: #606266;
  }

  .box {
    /* background-color: #e8f4ff; */
    padding: 20px;
  }

  .pagination-container /deep/ {
    background-color: transparent;
    padding-bottom: 10px;
  }

</style>
<script>
import api_base from '@/api/product/base'
import api_pharmacy from '@/api/pharmacy/index'
import api_warehouse from '@/api/pharmacy/warehouse'
export default {
  name: '',
  data() {
    return {
      warehouseDetail: {},
      goodsList: [],
      warehouseList: [],
      goodsSelect: [],
      warehouseSelect: [],
      pharmacologyData: null, //药理分类数据
      goodsQuery: {
        pageNo: 1,
        pageSize: 10
      },
      warehouseQuery: {
        pageNo: 1,
        pageSize: 10
      },
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      goodstotal: 0,
      warehousetotal: 0
    }
  },
  created() {
    console.log(this.$store.state.tagsView.visitedViews, 167)
    this.getPharmacologyData()
    this.warehouseId = this.$route.params.warehouseId
    this.goodsQuery.warehouseId = this.$route.params.warehouseId
    this.getPartnerDetail()
    this.getGoodsList()
    this.getPartnerList()
  },
  methods: {
    getPartnerDetail() {
      api_pharmacy.warehouseDetail(this.warehouseId).then(response => {
        this.warehouseDetail = response
      })
    },
    // 获取数据
    getGoodsList() {
      api_warehouse.goodslist(this.goodsQuery).then(response => {
        this.goodsList = response.list
        this.goodstotal = response.totalCount
      })
    },
    handleFilter() {
      this.goodsQuery.pageNo = 1
      this.getGoodsList()
    },
    getPartnerList() {
      api_warehouse
        .getGoodsList(this.warehouseId, this.warehouseQuery)
        .then(response => {
          this.warehouseList = response.list
          this.warehousetotal = response.totalCount
        })
    },
    getPharmacologyData() {
      api_base.pharmacology().then(response => {
        this.pharmacologyData = response
      })
    },
    setProduct() {
      this.$confirm('是否设置为合作商品？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api_warehouse
          .setGoods(this.warehouseId, this.goodsSelect)
          .then(response => {
            this.$message({
              message: '设置成功',
              type: 'success'
            })
            if (
              this.goodsList.length === this.goodsSelect.length &&
                this.goodsQuery.pageNo > 1
            ) {
              this.goodsQuery.pageNo = this.goodsQuery.pageNo - 1
            }
            this.$refs.goodsTable.clearSelection()
            this.getGoodsList()
            this.getPartnerList()
          })
      })
    },
    delProduct() {
      this.$confirm('是否删除合作商品？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api_warehouse
          .delGoods(this.warehouseId, this.warehouseSelect)
          .then(response => {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            if (
              this.warehouseList.length === this.warehouseSelect.length &&
                this.warehouseQuery.pageNo > 1
            ) {
              this.warehouseQuery.pageNo = this.warehouseQuery.pageNo - 1
            }
            this.$refs.warehouseTable.clearSelection()
            this.getGoodsList()
            this.getPartnerList()
          })
      })
    },
    goodsSelectionChange(value) {
      if (value.length > 0) {
        var selectarr = []
        value.forEach((currentValue, index, arr) => {
          selectarr.push(currentValue.skuId)
        })
        this.goodsSelect = selectarr
      } else {
        this.goodsSelect = []
      }
    },
    warehouseSelectionChange(value) {
      if (value.length > 0) {
        var selectarr = []
        value.forEach((currentValue, index, arr) => {
          selectarr.push(currentValue.skuId)
        })
        this.warehouseSelect = selectarr
      } else {
        this.warehouseSelect = []
      }
    },
    beforeRouteLeave(to, from, next) {
      this.$store.state.tagsView.visitedViews.pop()
      next()
    }
  },
  beforeRouteLeave(to, from, next) {
    console.log(this.$store.state.tagsView.visitedViews, 271)
    this.$store.state.tagsView.visitedViews.pop()
    next()
  }
}

</script>
