<template>
  <div class="app-container">
    <el-form ref="dataForm" :model="warehouseData" :rules="rules" label-width="160px" class="demo-form-inline">
      <el-form-item label="排序号" prop="sort">
        <el-input-number v-model="warehouseData.sort" :min="-999" :max="999"></el-input-number>
      </el-form-item>

      <el-form-item label="商家名称" prop="name">
        <el-input v-model="warehouseData.name" style="width: 200px;" clearable />
      </el-form-item>

      <el-form-item v-if="!warehouseId" label="密码" prop="password">
        <el-input v-model="warehouseData.password" style="width: 200px;" clearable />
      </el-form-item>

      <div style="display: flex;">
        <el-form-item label="商家联系人" prop="linkman">
          <el-input v-model="warehouseData.linkman" style="width: 200px;" clearable />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="warehouseData.phone" style="width: 200px;" clearable />
        </el-form-item>
        <el-form-item label="联系邮箱" prop="email">
          <el-input v-model="warehouseData.email" style="width: 200px;" type="email" clearable />
        </el-form-item>
      </div>

      <div style="display: flex;">
        <el-form-item label="商家地址" prop="cityId">
          <el-cascader v-model="warehouseData.cityId" :options="cityData" :props="props" style="width: 200px;" clearable />
        </el-form-item>
        <el-form-item class="address" prop="address">
          <el-input v-model="warehouseData.address" style="width: 200px;" clearable />
        </el-form-item>
      </div>

      <el-form-item label="售后客服电话" prop="serviceHotline">
        <el-input v-model="warehouseData.serviceHotline" style="width: 200px;" clearable />
      </el-form-item>

      <!-- <el-form-item label="是否包邮" prop="freightConfig.freeFreight" @input="changeFree">
        <el-radio-group v-model="warehouseData.freightConfig.freeFreight">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item> -->

      <el-form-item label="邮费类型" prop="freightConfig.freightType" @input="changeFree">
        <el-radio-group v-model="warehouseData.freightConfig.freightType">
          <el-radio :label="0">包邮</el-radio>
          <el-radio :label="1">统一邮费</el-radio>
          <el-radio :label="2">满额邮费</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="warehouseData.freightConfig.freightType === 1 || warehouseData.freightConfig.freightType === 2" label="邮费(元)" prop="freightConfig.freight">
        <el-input
          v-model="warehouseData.freightConfig.freight"
          style="width: 200px;"
        />
      </el-form-item>

      <el-form-item
        v-if="warehouseData.freightConfig.freightType === 2"
        class="order"
        label="订单满"
        prop="freightConfig.freeFreightThreshold"
        style="display: flex;"
      >
        <el-input
          v-model="warehouseData.freightConfig.freeFreightThreshold"
          style="width: 200px;"
        />
        <span style="margin-left: 10px;">元包邮</span>
      </el-form-item>

      <el-form-item label="支持药品类型" prop="config">
        <el-checkbox-group v-model="warehouseData.config" @change="changeCheckBox0">
          <el-checkbox :label="1">中药</el-checkbox>
          <el-checkbox :label="3">西药</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <div v-if="warehouseData.config.indexOf(1) > -1">
        <el-row v-show="warehouseData.config.indexOf(1) > -1" :gutter="20">
          <el-form-item label="支持中药剂型" prop="config">
            <el-checkbox-group v-model="warehouseData.config" @change="changeCheckBox1">
              <el-checkbox :label="9">内服中药</el-checkbox>
              <el-checkbox :label="10">滋补膏方</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-row>
        <el-row v-show="warehouseData.config.indexOf(9) > -1" :gutter="20" class="config">
          <el-form-item label="是否代煎">
            <el-checkbox-group v-model="warehouseData.config" @change="changeCheckBox2">
              <el-checkbox ref="childCheckBox" :label="8">是</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-row>
        <el-row v-show="warehouseData.config.indexOf(8) > -1" :gutter="20">
          <el-form-item label="代煎费" prop="processFee">
            <el-input
              v-model.number="warehouseData.processFee"
              style="width: 200px;"
              onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
            />
          </el-form-item>
        </el-row>
        <el-row v-if="warehouseData.config.indexOf(10) > -1" :gutter="20">
          <el-form-item label="膏方制作费" prop="productionFee">
            <el-input
              v-model.number="warehouseData.productionFee"
              style="width: 200px;"
              onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
            />
          </el-form-item>
        </el-row>
        <el-row v-if="warehouseData.config.indexOf(10) > -1" style="display: flex;" :gutter="20">
          <el-form-item label="膏方制作周期" prop="productionCycle">
            <el-input
              v-model.number="warehouseData.productionCycle"
              style="width: 200px;"
              onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
            />
          </el-form-item>
          <span style="line-height: 30px; margin-left: 20px;">天</span>
        </el-row>
        <div v-if="warehouseData.config.indexOf(10) > -1">
          <el-row :gutter="20">
            <el-form-item label="膏方辅料:"> </el-form-item>
          </el-row>
          <el-row v-for="(item, index) in Accessories" :key="index" :gutter="20">
            <el-form-item :label="item.name">
              <el-checkbox-group v-model="warehouseData.accessoriesIds">
                <el-checkbox v-for="(accessory, idx) in item.accessories" :key="idx" :label="accessory.id">{{
                  accessory.name
                }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-row>
        </div>
      </div>
      <!-- <el-row :gutter="20">
        <el-form-item label="配送方式" prop="sendWay">
          <el-checkbox-group v-model="warehouseData.config" @change="changeCheckBox3">
            <el-checkbox :label="4">物流配送</el-checkbox>
            <el-checkbox :label="5">到店购药</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-row>
      <el-row v-show="warehouseData.config.indexOf(4)>-1" :gutter="20">
        <el-form-item label="物流费用" prop="freight">
          <el-input v-model="warehouseData.freight" oninput="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" clearable />
        </el-form-item>
      </el-row> -->
      <el-row :gutter="20">
        <el-form-item label="备注信息" prop="remark">
          <el-input v-model="warehouseData.remark" style="width: 200px;" type="textarea" clearable />
        </el-form-item>
      </el-row>
    </el-form>
    <div style="text-align: center;">
      <el-button type="primary" @click="savePartner('dataForm')">保存</el-button>
      <el-button type="primary" @click="savePartner('dataForm', 'product')">保存并维护商品信息</el-button>
      <el-button type="primary" @click="canelBack">取消</el-button>
    </div>
  </div>
</template>
<script>
import api_pharmacy from '@/api/pharmacy/index'
export default {
  name: '',
  data() {
    // var checkEmail = (rule, value, callback) => {
    //   const mailReg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/
    //   if (!value) {
    //     return callback(new Error('邮箱不能为空'))
    //   }
    //   setTimeout(() => {
    //     if (mailReg.test(value)) {
    //       callback()
    //     } else {
    //       callback(new Error('请输入正确的邮箱格式'))
    //     }
    //   }, 100)
    // }
    return {
      warehouseId: null,
      warehouseData: {
        config: [4],
        accessoriesIds: [],
        address: '',
        processFee: '',
        productionFee: '',
        productionCycle: '',
        freight: '',
        sort: null,
        freightConfig: {
          // freeFreight: '', // 是否包邮
          freightType: null, // 邮费类型(0: 包邮 1:统一邮费, 2:满额包邮)
          freight: null, // 邮费
          freeFreightThreshold: null // 满额包邮金额
        }
      },
      merchantOptions: [], //商家归属
      Accessories: [],
      cityData: null,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      rules: {
        name: [{ required: true, message: '请输入商家名称', trigger: 'blur' }],
        sort: [{ required: true, message: '请输入排序号', trigger: 'blur' }],
        linkman: [{ required: true, message: '请输入商家联系人', trigger: 'blur' }],
        phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        // email: [{ required: true, validator: checkEmail, trigger: 'blur' }],
        cityId: [{ required: true, message: '请选择商家地址', trigger: 'blur' }],
        serviceHotline: [{ required: true, message: '请输入商家客服电话', trigger: 'blur' }],
        address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        config: [{ required: true, message: '请选择支持药品类型', trigger: 'blur' }],
        // processFee: [
        //   { required: true, message: '请输入代煎费', trigger: 'blur' }
        // ],
        productionFee: [{ required: true, message: '请输入膏方制作费', trigger: 'blur' }],
        productionCycle: [{ required: true, message: '请输入膏方制作周期', trigger: 'blur' }],
        // 'freightConfig.freeFreight': [{ required: true, message: '请选择是否包邮', trigger: 'change' }],
        'freightConfig.freightType': [{ required: true, message: '请选择邮费类型', trigger: 'change' }],
        'freightConfig.freight': [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              const regex = /^\d+(\.\d{1,2})?$/
              if (value.toString().trim() === '') {
                callback(new Error('请输入邮费'))
              } else if (!regex.test(value) || value <= 0) {
                callback(new Error('请输入大于0的整数或小数(保留两位小数)'))
              } else {
                callback()
              }
            }
          }
        ],
        'freightConfig.freeFreightThreshold': [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              const regex = /^\d+(\.\d{1,2})?$/
              if (value.toString().trim() === '') {
                callback(new Error('请输入订单金额'))
              } else if (!regex.test(value) || value <= 0) {
                callback(new Error('请输入大于0的整数或小数(保留两位小数)'))
              } else {
                callback()
              }
            }
          }
        ]
      }
    }
  },
  created() {
    this.getCityList()
    if (this.$route.params.warehouseId) {
      this.warehouseId = this.$route.params.warehouseId
      this.getPartnerDetail(this.$route.params.warehouseId)
    }
    if (this.$route.query.sort) {
      this.warehouseData.sort = this.$route.query.sort
    }
    this.getAccessories()
  },
  methods: {
    canelBack() {
      this.$router.push({ path: '/pharmacy/list' })
      this.$store.state.tagsView.visitedViews.pop()
      console.log(this.$store.state.tagsView.visitedViews, 113)
    },
    getCityList() {
      api_pharmacy.citylist().then((response) => {
        this.cityData = response
      })
    },
    getPartnerDetail(id) {
      api_pharmacy.warehouseDetail(id).then((response) => {
        console.log(response, '123')
        response.config = [...new Set([...response.config, ...this.warehouseData.config])]
        response.freightConfig = response.freightConfig ? response.freightConfig : { freightType: null, freight: null, freeFreightThreshold: null }
        this.warehouseData = response
      })
    },
    getAccessories() {
      const params = {}
      params.warehouseId = this.warehouseId
      api_pharmacy.getAccessories(params).then((response) => {
        console.log(response)
        this.Accessories = response
      })
    },
    // 药品类型选择
    changeCheckBox0() {
      if (this.warehouseData.config.indexOf(1) === -1) {
        this.warehouseData.processFee = ''
        this.warehouseData.productionFee = ''
        this.warehouseData.productionCycle = ''
        delete this.warehouseData.processFee
        delete this.warehouseData.productionFee
        delete this.warehouseData.productionCycle
        this.removeByValue(this.warehouseData.config, 8)
        this.removeByValue(this.warehouseData.config, 9)
        this.removeByValue(this.warehouseData.config, 10)
      }
    },
    // 中药剂型选择
    changeCheckBox1() {
      if (this.warehouseData.config.indexOf(9) === -1) {
        this.warehouseData.config.includes(8) && this.$refs.childCheckBox.$el.click() // 取消8勾选
      }
      // if (this.warehouseData.config.indexOf(10) === -1) {
      //   delete this.warehouseData.productionFee
      //   delete this.warehouseData.productionCycle
      // }
    },
    // 是否代煎选择
    changeCheckBox2() {
      // if (this.warehouseData.config.indexOf(8) === -1) {
      //   delete this.warehouseData.processFee
      // }
    },
    changeCheckBox3() {
      if (this.warehouseData.config.indexOf(9) === -1) {
        this.warehouseData.config.includes(4) && this.$refs.childCheckBox.$el.click() // 取消8勾选
      }
    },
    changeFree() {
      if (this.warehouseData.freightConfig.freeFreight === 1) {
        this.warehouseData.freightConfig.freight = 0
        this.warehouseData.freightConfig.freeFreightThreshold = null
        this.warehouseData.freightConfig.freightType = null
        delete this.warehouseData.freightConfig.freight
        delete this.warehouseData.freightConfig.freeFreightThreshold
        delete this.warehouseData.freightConfig.freightType
      }
    },
    removeByValue(arr, val) {
      //删除数组中指定元素
      for (var i = 0; i < arr.length; i++) {
        // eslint-disable-next-line eqeqeq
        if (arr[i] == val) {
          arr.splice(i, 1)
          break
        }
      }
    },
    savePartner(dataForm, product) {
      if (this.warehouseData.config.indexOf(8) === -1) {
        delete this.warehouseData.processFee
      }
      if (this.warehouseData.config.indexOf(10) === -1) {
        delete this.warehouseData.productionFee
        delete this.warehouseData.productionCycle
      }
      console.log(this.warehouseData)
      this.$refs[dataForm].validate((valid) => {
        if (this.warehouseData.config.indexOf(1) === -1 && this.warehouseData.config.indexOf(3) === -1) {
          this.$message.error('请选择药品类型！')
          return
        }
        if (
          this.warehouseData.config.indexOf(1) !== -1 &&
          this.warehouseData.config.indexOf(9) === -1 &&
          this.warehouseData.config.indexOf(10) === -1
        ) {
          this.$message.error('请选择中药剂型！')
          return
        }
        if (
          this.warehouseData.config.indexOf(10) !== -1 &&
          (this.warehouseData.productionFee === '' || this.warehouseData.productionCycle === '')
        ) {
          this.$message.error('请填写制作搞方费和制作周期！')
          return
        }
        if (
          this.warehouseData.config.indexOf(8) !== -1 &&
          (this.warehouseData.processFee === undefined || this.warehouseData.processFee === '')
        ) {
          this.$message.error('代煎费不能为空！')
          return
        }
        const params = Object.assign({}, this.warehouseData)
        if (this.warehouseData.config.indexOf(10) === -1) {
          delete params.productionFee
          delete params.productionCycle
        }
        if (valid) {
          console.log(this.warehouseData, 'this.warehouseData')
          if (this.warehouseData.id) {
            api_pharmacy.update(this.warehouseData).then((response) => {
              this.$message({
                message: '更新成功',
                type: 'success'
              })
              // var x = document.querySelector('.el-scrollbar__view .router-link-active .el-icon-close')
              // x.click()
              if (product) {
                const query = { active: 'second' }
                this.$router.push({
                  path: '/pharmacy/product/' + this.warehouseData.id,
                  query
                })
              } else {
                var x = document.querySelector('.el-scrollbar__view .router-link-active .el-icon-close')
                x.click()
                this.$router.push({
                  path: '/pharmacy/list'
                })
              }
            })
          } else {
            api_pharmacy.save(this.warehouseData).then((response) => {
              this.$message({
                message: '保存成功',
                type: 'success'
              })
              if (product) {
                this.$router.push({
                  path: '/pharmacy/product/' + response
                })
              } else {
                var x = document.querySelector('.el-scrollbar__view .router-link-active .el-icon-close')
                x.click()
                this.$router.push({
                  path: '/pharmacy/list'
                })
              }
            })
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .address .el-form-item__content {
  margin-left: 20px !important;
}

::v-deep .order .el-form-item__content {
  margin-left: 0px !important;
}
</style>
