@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

.el-dialog__body {
  padding: 20px 20px;
  color: #606266;
  font-size: 14px;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  outline: none;
  text-decoration: none;
}

div:focus {
  outline: none;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}

.warn-content {
  background: rgba(66, 185, 131, 0.1);
  border-radius: 2px;
  padding: 11px;
  word-spacing: 0.05rem;
  a {
    color: #42b983;
    font-weight: 400;
  }
}

//main-container全局样式
.app-main {
  height: 100%;
}

.app-container {
  padding: 20px;
}

.head-container {
  padding-bottom: 10px;
  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
  input {
    height: 30.5px;
    line-height: 30.5px;
  }
  .el-input__icon {
    line-height: 31px;
  }
}

.el-avatar {
  display: inline-block;
  text-align: center;
  background: #ccc;
  color: #fff;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  vertical-align: middle;
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: 16px;
}

.logo-con {
  height: 60px;
  padding: 13px 0px 0px;
  img {
    height: 32px;
    width: 135px;
    display: block;
    //margin: 0 auto;
  }
}

#el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

#el-main-footer {
  background: none repeat scroll 0 0 white;
  border-top: 1px solid #e7eaec;
  overflow: hidden;
  padding: 10px 6px 0px 6px;
  height: 33px;
  font-size: 0.7rem !important;
  color: #7a8b9a;
  letter-spacing: 0.8px;
  font-family: Arial, sans-serif !important;
  position: fixed;
  bottom: 0;
  z-index: 99;
  width: 100%;
}

.filter-container {
  padding-bottom: 10px;
  .filter-item {
    margin-bottom: 10px;
  }
}

.detail-nav-top {
  position: fixed;
  top: 84px;
  right: 0;
  z-index: 1005;
  background: white;
  width: calc(100% - 210px);
  transition: width 0.28s;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  .operate {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  .toptips {
    width: 100%;
    padding: 10px 10px;
    background: #edf6ff;
    color: #666;
    font-size: 14px;
    .example {
      color: #1890ff;
      cursor: pointer;
    }
  }
  .toptips i {
    color: #1890ff;
    font-size: 16px;
    margin-right: 6px;
  }
}
.detail-nav-top.wide {
  width: calc(100% - 54px) !important;
}
