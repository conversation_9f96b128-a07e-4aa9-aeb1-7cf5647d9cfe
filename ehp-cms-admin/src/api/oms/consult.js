import request from '@/utils/request'
import qs from 'qs'
export function getList(data) {
  return request({
    url: '/consult/order/page',
    method: 'get',
    params: data
  })
}
export function getDetail(data) {
  return request({
    url: `/consult/order/${data.consultId}/${data.consultType}`,
    method: 'get'
  })
}

export function orderExport(data) {
  const query = qs.stringify(data)
  return process.env.VUE_APP_BASE_API + '/consult/order/export?' + query
}

