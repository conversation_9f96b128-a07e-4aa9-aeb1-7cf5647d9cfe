import request from '@/utils/request'

export function getList() {
  return request({
    url: '/sys/config/list',
    method: 'get'
  })
}

export function setConfig(data) {
  return request({
    url: `/sys/config/${data.configKey}/${data.configSwith}`,
    method: 'put'
  })
}
export function getPermissionConfig(key) {
  return request({
    url: `/sys/config/${key}`,
    method: 'get'
  })
}

export function getOtherList(key) {
  return request({
    url: 'sys/config/value/list',
    method: 'get'
  })
}

export function setOtherConfig(data) {
  return request({
    url: `sys/config/value/${data.configKey}`,
    method: 'post',
    params: data
  })
}

export default {
  getList,
  setConfig,
  getPermissionConfig,
  getOtherList,
  setOtherConfig
}
