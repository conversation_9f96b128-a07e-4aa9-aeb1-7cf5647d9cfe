import request from '@/utils/request'

// 诊费管理列表
export function getList(data) {
  return request({
    url: '/rank/charge/list',
    method: 'get',
    params: data
  })
}

// 诊费管理-新增诊费
export function addConsultationFee(data) {
  return request({
    url: '/rank/charge/save',
    method: 'post',
    data
  })
}

// 诊费管理-编辑诊费
export function editConsultationFee(data) {
  return request({
    url: data.id ? '/rank/charge/update' : '/rank/charge/save',
    method: 'post',
    data
  })
}

// 诊费管理-删除诊费
export function deleteConsultationFee(id) {
  return request({
    url: `/rank/charge/${id}`,
    method: 'delete'
  })
}

// 诊费管理-启用/停用诊费
export function changeConsultationFeeStatus(data) {
  return request({
    url: `/base/consultation-fee/status/${data.id}/${data.status}`,
    method: 'post'
  })
}
