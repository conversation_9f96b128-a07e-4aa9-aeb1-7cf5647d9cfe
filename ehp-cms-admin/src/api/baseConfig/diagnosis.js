import request from '@/utils/request'

// 诊断与科室管理列表
export function getList(data) {
  return request({
    url: '/api/offline/diagnosis/department/list',
    method: 'get',
    params: data
  })
}

// 诊断与科室管理-新增编辑诊断
export function editDiagnosis(data) {
  return request({
    url: '/api/offline/diagnosis/department/save',
    method: 'post',
    data
  })
}

// 诊断与科室管理-删除诊断
export function deleteDiagnosis(id) {
  return request({
    url: `/api/offline/diagnosis/department/${id}`,
    method: 'delete'
  })
}

// 诊断与科室管理-启用/停用诊断
export function changeDiagnosisStatus(obj) {
  return request({
    url: `/api/offline/diagnosis/department/${obj.id}/toggle`,
    method: 'put'
  })
}

// 获取科室列表（用于多选）
export function getDepartmentList() {
  return request({
    url: '/base/department/tree',
    method: 'get',
    params: {
      pageNo: 1,
      pageSize: 10,
      orderBy: 'desc',
      available: false
    }
  })
}
