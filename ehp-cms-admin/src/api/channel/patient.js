import request from '@/utils/request'

// 渠道管理列表
export function getChannelList(data) {
  return request({
    url: '/patient/channel/list',
    method: 'get',
    params: data
  })
}

// 新增渠道
export function addChannel(data) {
  return request({
    url: '/patient/channel/',
    method: 'post',
    data
  })
}

// 编辑渠道
export function editChannel(data, id) {
  return request({
    url: '/patient/channel/' + id,
    method: 'put',
    data
  })
}
export function channelStatus(id, status) {
  return request({
    url: `/patient/channel/status/${id}/${status}`,
    method: 'put'
  })
}
//
export function getChannel(id) {
  return request({
    url: '/patient/channel/' + id,
    method: 'get'
  })
}
// 渠道列表
export const getSelectList = () => {
  return request({
    url: '/patient/channel/select',
    method: 'get'
  })
}
