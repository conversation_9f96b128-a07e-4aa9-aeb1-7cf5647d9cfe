import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/user/counselor/list',
    method: 'get',
    params: data
  })
}

// 咨询师详情
export function get(id) {
  return request({
    url: '/user/counselor/' + id,
    method: 'get'
  })
}
// 保存咨询师
export function save(data) {
  return request({
    url: '/user/counselor/',
    method: data.id ? 'put' : 'post',
    data
  })
}

export function Export() {
  return request({
    url: '/user/counselor/expert/model/',
    method: 'get'
  })
}

/**
 * 启用停用咨询师
 * @param id
 * @param status
 * @returns {*}
 */

export function editStatus(id, status) {
  return request({
    url: '/user/counselor/status/' + id + '/' + status,
    method: 'put'
  })
}

// 咨询师会话列表
export function getSessionList(data) {
  return request({
    url: '/user/counselor/consult/list',
    method: 'get',
    params: data
  })
}

// 咨询师会话详情
export function getSessionDetail(data) {
  return request({
    url: '/user/counselor/consult/detail',
    method: 'get',
    params: data
  })
}
