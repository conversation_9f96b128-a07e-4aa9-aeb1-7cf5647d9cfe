import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/user/patient/list',
    method: 'get',
    params: data
  })
}

export function get(patientId) {
  return request({
    url: '/user/patient/' + patientId,
    method: 'get'
  })
}

export function getCaseList(data) {
  return request({
    url: '/user/patient/case',
    method: 'get',
    params: data
  })
}

export function getInquirerList(data) {
  return request({
    url: '/user/patient/inquirer/list',
    method: 'get',
    params: data
  })
}

export function getCaseLogList(id, data) {
  return request({
    url: '/user/patient/case/' + id,
    method: 'get',
    params: data
  })
}

export function setCaseLogList(data) {
  return request({
    url: '/user/patient/case/download/report',
    method: 'get',
    params: data
  })
}

export function getFormList(data) {
  return request({
    url: '/user/patient/counselor/form/List',
    method: 'get',
    params: data
  })
}
export function getFollowDetail(counselorFollowId) {
  return request({
    url: '/user/patient/counselor/form/detail?counselorFollowId=' + counselorFollowId,
    method: 'get'
  })
}

export function getCounselorsDetail(patientId) {
  return request({
    url: `user/patient/${patientId}/counselors`,
    method: 'get'
  })
}

// 患者详情-历史订单列表（分页）
export function getOrderList(data) {
  return request({
    url: '/user/patient/orders',
    method: 'get',
    params: data
  })
}

// 患者详情-咨询列表（分页）
export function getConsultSessionList(data) {
  return request({
    url: '/user/patient/sessions',
    method: 'get',
    params: data
  })
}

export function getSessionPdf(data) {
  return request({
    url: '/user/patient/consult/content/file',
    method: 'get',
    params: data
  })
}

// AI自测相关API
export function getAiTestList(data) {
  return request({
    url: '/user/patient/ai-test/list',
    method: 'get',
    params: data
  })
}

export function getAiTestDetail(testId) {
  return request({
    url: `/user/patient/ai-test/${testId}`,
    method: 'get'
  })
}

// 服务包相关API
export function getServicePackageList(data) {
  return request({
    url: '/user/patient/service-package/list',
    method: 'get',
    params: data
  })
}

export function getServicePackageDetail(packageId) {
  return request({
    url: `/user/patient/service-package/${packageId}`,
    method: 'get'
  })
}

export function updateServicePackageTodos(packageId, todoData) {
  return request({
    url: `/user/patient/service-package/${packageId}/todos`,
    method: 'put',
    data: todoData
  })
}
