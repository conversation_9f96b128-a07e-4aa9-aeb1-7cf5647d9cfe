import request from '@/utils/request'
import qs from 'qs'
export function getList(data) {
  return request({
    url: '/user/patient/list',
    method: 'get',
    params: data
  })
}

export function get(patientId) {
  return request({
    url: '/user/patient/' + patientId,
    method: 'get'
  })
}

export function getCaseList(data) {
  return request({
    url: '/user/patient/case',
    method: 'get',
    params: data
  })
}

export function getInquirerList(data) {
  return request({
    url: '/user/patient/inquirer/list',
    method: 'get',
    params: data
  })
}

export function getCaseLogList(id, data) {
  return request({
    url: '/user/patient/case/' + id,
    method: 'get',
    params: data
  })
}

export function setCaseLogList(data) {
  return request({
    url: '/user/patient/case/download/report',
    method: 'get',
    params: data
  })
}

export function getFormList(data) {
  return request({
    url: '/user/patient/counselor/form/List',
    method: 'get',
    params: data
  })
}
export function getFollowDetail(counselorFollowId) {
  return request({
    url: '/user/patient/counselor/form/detail?counselorFollowId=' + counselorFollowId,
    method: 'get'
  })
}

export function getCounselorsDetail(patientId) {
  return request({
    url: `user/patient/${patientId}/counselors`,
    method: 'get'
  })
}

// 患者详情-历史订单列表（分页）
export function getOrderList(data) {
  return request({
    url: '/user/patient/orders',
    method: 'get',
    params: data
  })
}

// 患者详情-咨询列表（分页）
export function getConsultSessionList(data) {
  return request({
    url: '/user/patient/sessions',
    method: 'get',
    params: data
  })
}

export function getSessionPdf(data) {
  return request({
    url: '/user/patient/consult/content/file',
    method: 'get',
    params: data
  })
}
export function exportData(data) {
  return process.env.VUE_APP_BASE_API + `/user/patient/export?` + qs.stringify(data)
}
export function tempUrl() {
  return request({
    url: '/wms/medicine/sku/default/usage/expert/model',
    method: 'get'
  })
}
export function excelURL() {
  return process.env.VUE_APP_BASE_API + '/user/patient/batchBindPatientChannel'
}

export function changeChannel(patientId, patientChannelId) {
  return request({
    url: `/user/patient/updatePatientChannel/${patientId}/${patientChannelId}`,
    method: 'get'
  })
}
