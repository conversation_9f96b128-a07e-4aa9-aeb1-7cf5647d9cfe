import request from '@/utils/request'

// 服务包列表
export function getList(data) {
  return request({
    url: '/service/package/page',
    method: 'get',
    params: data
  })
}

// 新增服务包
export function addServicePackage(data) {
  return request({
    url: '/service/package/save',
    method: 'post',
    data
  })
}

// 编辑服务包
export function updateServicePackage(data) {
  return request({
    url: '/service/package/update',
    method: 'post',
    data
  })
}

// 删除服务包
export function deleteServicePackage(servicePackageId) {
  return request({
    url: `/service/package/${servicePackageId}`,
    method: 'delete'
  })
}

// 获取服务包详情
export function getServicePackageDetail(servicePackageId) {
  return request({
    url: `/service/package/detail/${servicePackageId}`,
    method: 'get'
  })
}

// 启用/禁用服务包
export function toggleServicePackageStatus(data) {
  return request({
    url: '/service/package/enable',
    method: 'post',
    data
  })
}

// 根据关键字搜索SKU
export function searchSku(data) {
  return request({
    url: '/wms/medicine/sku/page',
    method: 'get',
    params: data
  })
}

export default {
  getList,
  addServicePackage,
  updateServicePackage,
  deleteServicePackage,
  getServicePackageDetail,
  toggleServicePackageStatus
}
