// 疼痛管理模块API配置
// 接口开发完成后，修改这里的URL地址

export const API_CONFIG = {
  // 疼痛内容相关接口
  PAIN_CONTENT: {
    LIST: '/pain-content/list', // 获取疼痛内容列表
    DETAIL: '/pain-content/:id', // 获取疼痛内容详情
    CREATE: '/pain-content', // 创建疼痛内容
    UPDATE: '/pain-content/:id', // 更新疼痛内容
    DELETE: '/pain-content/:id', // 删除疼痛内容
    BATCH_DELETE: '/pain-content/batch-delete', // 批量删除疼痛内容
    UPDATE_STATUS: '/pain-content/:id/status' // 更新疼痛内容状态
  },

  // 疼痛分类相关接口
  PAIN_CATEGORY: {
    LIST: '/pain-category/list', // 获取疼痛分类列表
    ALL: '/pain-category/all', // 获取所有疼痛分类（用于下拉选择）
    DETAIL: '/pain-category/:id', // 获取疼痛分类详情
    CREATE: '/pain-category', // 创建疼痛分类
    UPDATE: '/pain-category/:id', // 更新疼痛分类
    DELETE: '/pain-category/:id', // 删除疼痛分类
    BATCH_DELETE: '/pain-category/batch-delete', // 批量删除疼痛分类
    UPDATE_STATUS: '/pain-category/:id/status' // 更新疼痛分类状态
  }
}

// 模拟数据配置
export const MOCK_DATA = {
  // 疼痛内容模拟数据
  PAIN_CONTENT_LIST: [
    {
      id: 1,
      title: '疼痛内容标题测试',
      categoryId: 1,
      categoryName: '急性疼痛',
      content: '<p>这是疼痛内容的详细描述，Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo.</p>',
      image: 'https://via.placeholder.com/300x200',
      servicePackageId: 1,
      servicePackageName: '春季专属服务包',
      status: 'published',
      createdAt: '2025-06-23 22:11:21',
      createdBy: 'Admin'
    },
    {
      id: 2,
      title: '慢性疼痛管理指南',
      categoryId: 2,
      categoryName: '慢性疼痛',
      content: '<p>慢性疼痛的管理需要综合性的治疗方案，包括药物治疗、物理治疗、心理支持等多个方面。</p>',
      image: 'https://via.placeholder.com/300x200',
      servicePackageId: 2,
      servicePackageName: '夏季健康套餐',
      status: 'published',
      createdAt: '2025-06-22 15:30:00',
      createdBy: 'Doctor'
    }
  ],

  // 疼痛分类模拟数据
  PAIN_CATEGORY_LIST: [
    {
      id: 1,
      name: '急性疼痛',
      status: 'active',
      createdAt: '2025-06-22 21:14:44',
      createdBy: '李四'
    },
    {
      id: 2,
      name: '慢性疼痛',
      status: 'active',
      createdAt: '2025-06-21 10:20:30',
      createdBy: '张三'
    },
    {
      id: 3,
      name: '神经性疼痛',
      status: 'inactive',
      createdAt: '2025-06-20 14:15:22',
      createdBy: '王五'
    }
  ],

  // 分类选项（用于下拉选择）
  CATEGORY_OPTIONS: [
    { id: 1, name: '急性疼痛' },
    { id: 2, name: '慢性疼痛' },
    { id: 3, name: '神经性疼痛' }
  ],

  // 服务包选项（用于下拉选择）
  SERVICE_PACKAGE_OPTIONS: [
    { id: 1, name: '春季专属服务包' },
    { id: 2, name: '夏季健康套餐' },
    { id: 3, name: '秋季养生包' },
    { id: 4, name: '冬季保健方案' }
  ]
}

// 接口状态配置
export const API_STATUS = {
  // 是否使用模拟数据（true: 使用模拟数据, false: 使用真实接口）
  USE_MOCK: true,

  // 模拟请求延迟时间（毫秒）
  MOCK_DELAY: 500,

  // 接口基础URL（如果后端接口有统一前缀）
  BASE_URL: '/api/v1'
}

// 工具函数：替换URL中的参数
export function replaceUrlParams(url, params) {
  let result = url
  Object.keys(params).forEach(key => {
    result = result.replace(`:${key}`, params[key])
  })
  return result
}

// 工具函数：生成完整的API URL
export function getApiUrl(apiPath, params = {}) {
  const baseUrl = API_STATUS.BASE_URL
  const url = replaceUrlParams(apiPath, params)
  return baseUrl + url
}

/*
使用说明：

1. 当前配置为使用模拟数据（API_STATUS.USE_MOCK = true）
2. 接口开发完成后，将 API_STATUS.USE_MOCK 设置为 false
3. 根据实际后端接口地址，修改 API_CONFIG 中的URL
4. 如果需要修改模拟数据，可以编辑 MOCK_DATA 对象

示例：
// 获取疼痛内容详情的完整URL
const detailUrl = getApiUrl(API_CONFIG.PAIN_CONTENT.DETAIL, { id: 123 })
// 结果: /api/v1/pain-content/123

// 在API文件中使用
import { API_CONFIG, API_STATUS, MOCK_DATA } from './config'

export function getList(query) {
  if (API_STATUS.USE_MOCK) {
    // 返回模拟数据
    return Promise.resolve({
      data: {
        items: MOCK_DATA.PAIN_CONTENT_LIST,
        total: MOCK_DATA.PAIN_CONTENT_LIST.length
      }
    })
  } else {
    // 调用真实接口
    return request({
      url: API_CONFIG.PAIN_CONTENT.LIST,
      method: 'get',
      params: query
    })
  }
}
*/
