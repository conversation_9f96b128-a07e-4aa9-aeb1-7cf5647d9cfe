import request from '@/utils/request'
import { API_CONFIG, API_STATUS, MOCK_DATA } from './config'

// 获取疼痛内容列表
export function getList(query) {
  if (API_STATUS.USE_MOCK) {
    // 返回模拟数据
    return new Promise((resolve) => {
      setTimeout(() => {
        const { title } = query || {}
        let list = [...MOCK_DATA.PAIN_CONTENT_LIST]

        // 模拟搜索功能
        if (title) {
          list = list.filter(item => item.title.includes(title))
        }

        resolve({
          data: {
            items: list,
            total: list.length
          }
        })
      }, API_STATUS.MOCK_DELAY)
    })
  } else {
    return request({
      url: API_CONFIG.PAIN_CONTENT.LIST,
      method: 'get',
      params: query
    })
  }
}

// 获取疼痛内容详情
export function getPainContent(id) {
  return request({
    url: `/pain-content/${id}`,
    method: 'get'
  })
}

// 创建疼痛内容
export function createPainContent(data) {
  if (API_STATUS.USE_MOCK) {
    // 模拟创建成功
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            id: Date.now(),
            ...data,
            status: 'published',
            createdAt: new Date().toLocaleString(),
            createdBy: 'Admin'
          }
        })
      }, API_STATUS.MOCK_DELAY)
    })
  } else {
    return request({
      url: API_CONFIG.PAIN_CONTENT.CREATE,
      method: 'post',
      data
    })
  }
}

// 更新疼痛内容
export function updatePainContent(data) {
  if (API_STATUS.USE_MOCK) {
    // 模拟更新成功
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            ...data,
            updatedAt: new Date().toLocaleString()
          }
        })
      }, API_STATUS.MOCK_DELAY)
    })
  } else {
    return request({
      url: API_CONFIG.PAIN_CONTENT.UPDATE.replace(':id', data.id),
      method: 'put',
      data
    })
  }
}

// 删除疼痛内容
export function deletePainContent(id) {
  return request({
    url: `/pain-content/${id}`,
    method: 'delete'
  })
}

// 批量删除疼痛内容
export function batchDeletePainContent(ids) {
  return request({
    url: '/pain-content/batch-delete',
    method: 'post',
    data: { ids }
  })
}

// 更新疼痛内容状态
export function updatePainContentStatus(id, status) {
  return request({
    url: `/pain-content/${id}/status`,
    method: 'put',
    data: { status }
  })
}
