import request from '@/utils/request'
import { API_CONFIG, API_STATUS, MOCK_DATA } from './config'

// 获取疼痛分类列表
export function getCategoryList(query) {
  if (API_STATUS.USE_MOCK) {
    // 返回模拟数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            items: MOCK_DATA.PAIN_CATEGORY_LIST,
            total: MOCK_DATA.PAIN_CATEGORY_LIST.length
          }
        })
      }, API_STATUS.MOCK_DELAY)
    })
  } else {
    return request({
      url: API_CONFIG.PAIN_CATEGORY.LIST,
      method: 'get',
      params: query
    })
  }
}

// 获取所有疼痛分类（用于下拉选择）
export function getAllCategories() {
  if (API_STATUS.USE_MOCK) {
    // 返回模拟数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: MOCK_DATA.CATEGORY_OPTIONS
        })
      }, API_STATUS.MOCK_DELAY)
    })
  } else {
    return request({
      url: API_CONFIG.PAIN_CATEGORY.ALL,
      method: 'get'
    })
  }
}

// 获取疼痛分类详情
export function getCategory(id) {
  return request({
    url: `/pain-category/${id}`,
    method: 'get'
  })
}

// 创建疼痛分类
export function createCategory(data) {
  if (API_STATUS.USE_MOCK) {
    // 模拟创建成功
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            id: Date.now(),
            ...data,
            status: 'active',
            createdAt: new Date().toLocaleString(),
            createdBy: 'Admin'
          }
        })
      }, API_STATUS.MOCK_DELAY)
    })
  } else {
    return request({
      url: API_CONFIG.PAIN_CATEGORY.CREATE,
      method: 'post',
      data
    })
  }
}

// 更新疼痛分类
export function updateCategory(data) {
  if (API_STATUS.USE_MOCK) {
    // 模拟更新成功
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            ...data,
            updatedAt: new Date().toLocaleString()
          }
        })
      }, API_STATUS.MOCK_DELAY)
    })
  } else {
    return request({
      url: API_CONFIG.PAIN_CATEGORY.UPDATE.replace(':id', data.id),
      method: 'put',
      data
    })
  }
}

// 删除疼痛分类
export function deleteCategory(id) {
  return request({
    url: `/pain-category/${id}`,
    method: 'delete'
  })
}

// 批量删除疼痛分类
export function batchDeleteCategory(ids) {
  return request({
    url: '/pain-category/batch-delete',
    method: 'post',
    data: { ids }
  })
}

// 更新疼痛分类状态
export function updateCategoryStatus(id, status) {
  return request({
    url: `/pain-category/${id}/status`,
    method: 'put',
    data: { status }
  })
}
