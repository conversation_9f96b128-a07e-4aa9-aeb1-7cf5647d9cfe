<template>
  <div ref="wrap" class="editor-wrap">
    <!-- 编辑器 -->
    <div v-show="isedit" style="border: 1px solid #ccc">
      <Toolbar
        style="border-bottom: 1px solid #ccc"
        :editor="editor"
        :default-config="toolbarConfig"
        :mode="mode"
      />
      <Editor
        v-model="html"
        style="resize: vertical; overflow-y: hidden"
        :style="{ height: height + 'px' }"
        :default-config="editorConfig"
        :mode="mode"
        @onCreated="onCreated"
        @onChange="onChange"
      />
    </div>
  </div>
</template>

<script>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { getToken } from '@/utils/auth'
import { uploadImage } from '@/api/common'
export default {
  name: 'WEditor',
  components: { Editor, Toolbar },
  props: {
    value: {
      type: String,
      default: ''
    },
    isedit: {
      type: Boolean,
      default: true
    },
    height: {
      type: Number,
      default: 500
    }
  },
  data() {
    return {
      content: '',
      editor: null,
      mode: 'default', // or 'simple'
      html: '',
      // 菜单栏配置
      toolbarConfig: {
        excludeKeys: [
          'fullScreen',
          'insertVideo',
          'uploadVideo',
          'group-video',
          'insertImage'
        ]
      },
      // 编辑器配置
      editorConfig: {
        placeholder: '请输入内容...',
        MENU_CONF: {
          //配置上传图片
          uploadImage: {
            // server: process.env.VUE_APP_BASE_API + "admin/attachment/image",
            // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
            allowedFileTypes: [],
            headers: {
              //token放这里
              token: 'bearer ' + getToken()
            },
            // 自定义插入图片 方法
            customInsert: this.insertImg,
            // 自定义上传图片 方法
            customUpload: this.uploadImg
          },
          insertImage: {
            // 隐藏网络图片tab
            showNetworkTab: false
          }
        }
      }
    }
  },
  watch: {
    value(val) {
      if (val) {
        if (val !== this.content) {
          this.setContent(val)
        }
      } else {
        this.setContent('')
      }
    },
    content(val) {
      this.$emit('input', val)
    }
  },
  mounted() {},
  beforeDestroy() {
    const editor = this.editor
    if (editor == null) return
    editor.destroy() // 组件销毁时，及时销毁编辑器
  },
  methods: {
    // 初始化富文本编辑器
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
      this.editor.setHtml(`<p>${this.value}</p>`)
    },
    onChange(editor) {
      this.content = editor.getHtml()
    },
    setContent(val) {
      // console.log(val);
      this.editor && this.editor.setHtml(val)
    },
    // 上传图片
    uploadImg(file, insertFn) {
      const imgData = new FormData()
      imgData.append('file', file)
      uploadImage(imgData)
        .then((resp) => {
          insertFn(resp)
          this.$message.success('上传成功')
        })
        .catch((error) => {
          this.$message('上传失败,请重新上传')
        })
    },
    // 自定义插入图片
    insertImg(file) {
      console.log(file)
    }
  }
}
</script>

<style src="@wangeditor/editor/dist/css/style.css"></style>
