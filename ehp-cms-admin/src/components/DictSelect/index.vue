<template>
  <el-select
    v-model="selectValue"
    :placeholder="placeholder"
    :disabled="disabled"
    :multiple="multiple"
    clearable
    :filterable="filterable"
    @change="handleSelect"
  >
    <el-option v-for="item in dictData" :key="item.code" :label="item.value" :value="item.code" />
  </el-select>
</template>

<script>
import { getDict } from '@/api/system/dict'
import request from '@/utils/request'
export default {
  name: 'DictSelect',
  model: {
    event: 'change'
  },
  props: {
    value: {
      type: [Number, String],
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: null
    },
    placeholder: {
      type: String,
      default: null
    },
    options: {
      type: Array,
      default: null
    },
    api: {
      type: String,
      default: null
    },
    filterable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectValue: null,
      dictData: []
    }
  },
  watch: {
    value: function(val) {
      this.selectValue = this.value
    }
  },
  mounted() {
    if (this.options) {
      this.dictData = this.options
      this.selectValue = this.value
    } else {
      this.getDict()
      this.selectValue = this.value
    }
  },
  methods: {
    // 获取数据
    getDict() {
      if (this.api) {
        request({
          url: this.api,
          method: 'get'
        }).then((res) => {
          this.dictData = res.map((item) => {
            return {
              code: item.id,
              value: item.name
            }
          })
        })
      } else {
        getDict(this.type).then(response => {
          this.dictData = response
        })
      }
    },
    handleSelect() {
      if (this.selectValue === '') {
        this.$emit('change', null)
      } else {
        this.$emit('change', this.selectValue)
      }
    }
  }
}
</script>
