<template>
  <div class="desensitization">
    <div v-if="view === 'text'">
      <div v-if="value" class="t">
        <span>{{ value }}</span>
        <i
          v-permission="['user:common:decrypt']"
          class="el-icon-view"
          style="cursor: pointer;"
          @click="encrypt"
        ></i>
      </div>
    </div>
    <div v-else class="input">
      <el-input v-model="value" :readonly="readonly">
        <i slot="suffix" class="el-icon-view" @click="encrypt"></i>
      </el-input>
    </div>
  </div>
</template>

<script>
import { getDecrypt } from '@/api/user/doctor'
export default {
  name: 'Desensitization',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    value: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    view: {
      type: String,
      default: 'text'
    },
    readonly: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    encrypt() {
      const { phoneEnc, cardNoEnc, emailEnc } = this.data
      const params = new FormData()
      params.append('value', this.type === 'phone' ? phoneEnc : this.type === 'cardNo' ? cardNoEnc : emailEnc)
      getDecrypt(params)
        .then((res) => {
          if (res) {
            this.$emit('change', res)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    }
  }
}
</script>

<style></style>
